import request from "@/utils/request"
import { basicPath10 } from '@/api/base.js'


/**
 * 养殖人员 
 */

// 新增
/**
    请求参数：{
        "breederType": 1, //角色 1：饲养组长 2：兽医
        "nickName": "石头", //姓名
        "phonenumber": "13683297936", //手机号
        "remark": "职责"
        }
 */
export const breederAdd = (data) => {
    return request({
        url: `${basicPath10}pasture/breeder/add`,
        method: 'post',
        data: data
    })
}
// 编辑
/**
 * 
 请求参数：
    {
    "xmbBreederId": 1953277003350196200, //ID
    "breederType": 2, //角色类型 1：饲养组长 2：兽医
    "nickName": "石头2", //姓名
    "remark": "职责2" //职责
}
 */
export const breederEdit = (data) => {
    return request({
        url: `${basicPath10}pasture/breeder/edit`,
        method: 'post',
        data: data
    })
}

// 详情
/**
 * 
请求参数：
{"xmbBreederId":1953277003350196200} // ID
 */
export const breederInfo = (data) => {
    return request({
        url: `${basicPath10}pasture/breeder/info`,
        method: 'post',
        data: data
    })
}

// 列表
/**
 * 
请求参数：
{
"pageNum": 1, //页数
"pageSize": 10, //行数
"nickName": "", //姓名
"phonenumber": "" //手机号
}

响应参数名：
"remark": "职责2",
"xmbBreederId": "1953277003350196225", //ID
"userId": "1857311084673175553", //用户ID
"pastureUserId": "1721397883321843714", //牧场所属者用户ID
"breederType": 2, //角色类型 1：饲养组长 2：兽医
"nickName": "石头2", //姓名
"phonenumber": "13683297936", //手机号
"todayRecordNum": 0, //今日操作数量
"breederTypeName": "兽医" //角色名称
 */
export const breederList = (data) => {
    return request({
        url: `${basicPath10}pasture/breeder/page`,
        method: 'post',
        data: data
    })
}
<template>
  <div>
    <el-dialog
      :title="detailsFormData.title"
      :visible.sync="detailsFormData.open"
      :close-on-click-modal="false"
      @close="close"
      class="fieldList"
    >
      <el-descriptions :column="1" :contentStyle="CS" :label-style="LS">
        <el-descriptions-item label="被监管方类型">{{info.patrolLivestockList[0].patrolPos}}</el-descriptions-item>
        <el-descriptions-item label="被监管方名称">{{info.patrolLivestockList[0].eartagSituation}}</el-descriptions-item>
        <el-descriptions-item label="营业执照号">{{info.patrolLivestockList[0].patrolLivestockCount}}</el-descriptions-item>
        <el-descriptions-item label="营业执照">
          <el-image
            class="qianmad"
            :src="picPath(info.patrolLivestockList[0].patrolSign)"
            :preview-src-list="srcList"
            @click="bigImage(picPath(info.patrolLivestockList[0].patrolSign))"
          ></el-image>
        </el-descriptions-item>

        <el-descriptions-item label="法人姓名">{{info.patrolUserName}}</el-descriptions-item>
        <el-descriptions-item label="法人联系电话">{{info.patrolUserName}}</el-descriptions-item>
        <el-descriptions-item label="法人身份证号">{{info.patrolUserName}}</el-descriptions-item>
        <el-descriptions-item label="法人身份证信息">
          <el-image
            class="qianmad"
            :src="picPath(info.patrolLivestockList[0].patrolSign)"
            :preview-src-list="srcList"
            @click="bigImage(picPath(info.patrolLivestockList[0].patrolSign))"
          ></el-image>
        </el-descriptions-item>
        <el-descriptions-item label="详细地址">{{info.patrolUserName}}</el-descriptions-item>
      </el-descriptions>

      <span slot="footer" class="dialog-footer">
        <el-button type="info" @click="close">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { inspectionInfo } from "@/api/ffs/supervisionSheet/livingSupervisionApi";
import { getFilePath } from "@/utils/east.js";
export default {
  name: "inspectionInfodetailsForm",
  props: {
    detailsFormData: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      CS: {
        "max-width": "300px", //最小宽度
        "word-break": "break-all", //过长时自动换行
      },
      LS: {
        "word-break": "keep-all",
        color: "#000",
      },

      queryParams: { fileName: "", superviseType: "" },
      form: {
        fileName: "",
        datetime: "",
        imagesArr: [
          { url: "dev/2022/09/4a1662446333102.jpg" },
          { url: "dev/2022/09/4a1662446333102.jpg" },
        ],
        videosArr: [{ url: "dev/2022/09/ar1662446361190.mp4" }],
      },

      // 遮罩层
      loading: true,
      // 总条数
      total: 3,
      // 表格数据
      list: [
        {
          fileName: "嚯哈哈",
          orderNum: "13201860552ABCD3",
          mobile: "13201860552",
          money: "30.00",
          createTime: "2022-12-12 10:10:11",
        },
      ],
      srcList: [],
      info: {
        patrolLivestockList: [{ patrolPos: "" }],
      },
    };
  },
  created() {
    this.getDetails();
  },
  computed: {
    patrolStatusName() {
      return (val) => {
        if (val == 0) {
          return "正常";
        } else {
          return "异常";
        }
      };
    },
  },
  methods: {
    bigImage(url) {
      this.srcList = [];
      this.srcList.push(url);
    },
    /** 查询巡检记录详情 */
    getDetails(id) {
      inspectionInfo({ ids: [this.detailsFormData.patrolId] }).then((res) => {
        this.loading = false;
        if (res.code == 200) {
          res.result.patrolLivestockList[0].patrolImgArr = getFilePath(
            res.result.patrolLivestockList[0].patrolImg
          );
          res.result.patrolLivestockList[0].patrolVideoArr = getFilePath(
            res.result.patrolLivestockList[0].patrolVideo
          );

          console.log("查询巡检记录详情------", res);
          this.info = res.result;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    close() {
      this.$emit("close");
    },
  },
};
</script>

<style lang="scss">
.fieldList {
  .el-dialog__header {
    background-color: #f4f4f4;
  }

  .el-dialog__footer {
    text-align: center;
  }

  .selectWidth {
    width: 100%;
  }
}
.head-title {
  font-size: 16px;
  color: #999;
  margin-bottom: 20px;
}
.qihsd {
  color: red;
}
.qianmad {
  height: 50px;
  margin-top: -20px;
}
.mlt20 {
  margin-left: 20px;
}
.f {
  display: flex;
}
</style>

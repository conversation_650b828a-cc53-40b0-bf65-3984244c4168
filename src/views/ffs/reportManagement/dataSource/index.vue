<template>
  <div class="app-container">
    <el-row :gutter="10" class="mb8">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
        <el-form-item label="数据源名称：" prop="fileName">
          <el-input v-model="queryParams.fileName" placeholder="请输入数据源名称" clearable />
        </el-form-item>

        <el-form-item label="数据源类型：" prop="fileType">
          <el-select v-model="queryParams.fileType" clearable>
            <el-option label="存量数据" value="1" />
            <el-option label="累放数据" value="2" />
          </el-select>
        </el-form-item>

<!--        <el-form-item label="上传时间">-->
<!--          <el-date-picker-->
<!--            v-model="dateRange"-->
<!--            style="width: 240px"-->
<!--            value-format="yyyy-MM-dd"-->
<!--            type="daterange"-->
<!--            range-separator="-"-->
<!--            start-placeholder="开始日期"-->
<!--            end-placeholder="结束日期"-->
<!--          ></el-date-picker>-->
<!--        </el-form-item>-->
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="insertData">导入</el-button>
      </el-col>
    </el-row>
    <el-table v-loading="loading" :data="list" style="width: 100%">
      <el-table-column label="序号" type="index"></el-table-column>
      <el-table-column label="数据源名称" prop="fileName"></el-table-column>
      <el-table-column label="数据源类型" prop="fileType" :formatter="fileTypeName"></el-table-column>
      <el-table-column label="报表日期" prop="fileDate"></el-table-column>
      <el-table-column label="上传时间" prop="createTime"></el-table-column>
      <el-table-column label="上传人员" prop="createUserName"></el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            icon="el-icon-delete"
            size="mini"
            type="text"
            @click="handleDelete(scope.row.fileId)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <add-form :addData="addData" @close="close" v-if="addData.open" @refresh="refresh"></add-form>
  </div>
</template>

<script>
import { getDataSourceList, deleteDataSource } from "@/api/ffs/reportDataApi";
import addForm from "./components/addForm";
export default {
  components: {
    addForm,
  },
  data() {
    return {
      addData: {
        open: false,
        id: "",
      },
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 表格数据
      list: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        fileDate: "",
        fileName: "",
        fileType: "",
      },
      // dateRange: [],
    };
  },
  async created() {
    this.getList();
  },
  computed: {
    fileTypeName() {
      return (row, com, val) => {
        if (val == 1) {
          return "存量数据";
        } else if (val == 2) {
          return "累放数据";
        }
      };
    },
  },
  methods: {
    //刷新页面
    refresh() {
      this.getList();
    },

    /** 查询列表 */
    getList() {
      this.loading = true;
      getDataSourceList(this.queryParams).then((res) => {
        this.loading = false;
        if (res.code == 200) {
          this.list = res.result.list || [];
          this.total = Number(res.result.total || 0);
        } else {
          this.$message.error(res.message);
        }
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.pageNum = 1;

      // if (this.dateRange) {
      //   this.queryParams.startTime = this.dateRange[0];
      //   this.queryParams.endTime = this.dateRange[1];
      // } else {
      //   this.queryParams.startTime = "";
      //   this.queryParams.endTime = "";
      // }

      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },

    //关闭弹框
    close() {
      this.addData.open = false;
    },

    //导入数据源
    insertData() {
      this.addData.open = true;
      this.addData.title = "导入数据源";
    },

    //删除
    handleDelete(fileId) {
      this.$confirm("确定删除吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          deleteDataSource({ fileId }).then((res) => {
            if (res.code == 200) {
              this.getList();
              this.$message({
                message: "删除成功",
                type: "success",
              });
            }
          });
        })
        .catch(() => {});
    },
  },
};
</script>
<style scoped>
</style>


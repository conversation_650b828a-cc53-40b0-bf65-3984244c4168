<template>
  <div class="app-container">
    <el-row :gutter="10" class="mb8">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
        <el-form-item label="报表日期" prop="fileDateStr">
          <el-date-picker v-model="queryParams.fileDateStr" type="month" placeholder="选择日期"></el-date-picker>
        </el-form-item>

        <!-- <el-form-item label="创建人" prop="createUserName">
          <el-input v-model="queryParams.createUserName" placeholder="请输入创建人名称" clearable />
        </el-form-item>-->

        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="insertData">生成报表</el-button>
      </el-col>
    </el-row>
    <el-table v-loading="loading" :data="list" style="width: 100%">
      <el-table-column label="序号" type="index"></el-table-column>
      <el-table-column label="累放数据" prop="cumulativeStock">
        <template slot-scope="scope">
          <a
            class="load-url"
            :href="scope.row.cumulativeStock"
          >{{getMonth(scope.row.fileDate,scope.row.cumulativeStock,0)}}</a>
        </template>
      </el-table-column>
      <el-table-column label="上月累放数据" prop="lastCumulativeStock">
        <template slot-scope="scope">
          <a
            class="load-url"
            :href="scope.row.lastCumulativeStockUrl"
          >{{getMonth(scope.row.fileDate,scope.row.lastCumulativeStock,3)}}</a>
        </template>
      </el-table-column>

      <el-table-column label="存量数据" prop="monthlyStock">
        <template slot-scope="scope">
          <a
            class="load-url"
            :href="scope.row.monthlyStock"
          >{{getMonth(scope.row.fileDate,scope.row.monthlyStock,1)}}</a>
        </template>
      </el-table-column>

      <el-table-column label="上月存量数据" prop="lastMonthlyStock">
        <template slot-scope="scope">
          <a
            class="load-url"
            :href="scope.row.lastMonthlyStock"
          >{{getMonth(scope.row.fileDate,scope.row.lastMonthlyStock,2)}}</a>
        </template>
      </el-table-column>

      <el-table-column label="报表日期" prop="fileDate"></el-table-column>
      <el-table-column label="创建人" prop="createUserName"></el-table-column>
      <el-table-column label="生成时间" prop="createTime"></el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            icon="el-icon-download"
            size="mini"
            type="text"
            @click="exportExcel(scope.row.downloadLink)"
          >下载报表</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <add-form :addData="addData" @close="close" v-if="addData.open" @refresh="refresh"></add-form>
  </div>
</template>

<script>
import { getRecordList } from "@/api/ffs/reportDataApi";
import addForm from "./components/addForm";
import { parseTime } from "@/utils/east.js";
export default {
  components: {
    addForm,
  },
  data() {
    return {
      addData: {
        open: false,
        id: "",
        title: "",
      },
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 表格数据
      list: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        fileDateStr: "",
        fileDate: "",
        createUserName: "",
      },
    };
  },

  async created() {
    const { name, fileType, title } = this.$route.query;
    if (!name || !fileType || !title) {
      this.$message({
        type: "error",
        message: "未携带必要参数进入!",
      });
      this.$router.go(-1);
      return;
    }
    this.addData.title = "生成" + name + title;
    this.queryParams.fileType = fileType;
    this.addData.fileName = name + title;
    this.addData.fileType = fileType;
    this.getList();
  },
  computed: {
    getMonth() {
      return (val, fileUrl, source) => {
        if (!val || !fileUrl) return "";
        if (source == 0) {
          return val.split("-")[1] * 1 + "月份累放";
        } else if (source == 3) {
          return val.split("-")[1] * 1 - 1 + "月份累放";
        } else if (source == 2) {
          return val.split("-")[1] * 1 - 1 + "月份存量";
        } else {
          return val.split("-")[1] * 1 + "月份存量";
        }
      };
    },
  },

  methods: {
    //刷新页面
    refresh() {
      this.getList();
    },

    /** 查询列表 */
    getList() {
      this.loading = true;
      getRecordList(this.queryParams).then((res) => {
        this.loading = false;
        if (res.code == 200) {
          this.list = res.result.list || [];
          this.total = Number(res.result.total || 0);
        } else {
          this.$message.error(res.message);
        }
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      if (this.queryParams.fileDateStr) {
        this.queryParams.fileDate =
          parseTime(this.queryParams.fileDateStr, "{y}-{m}") + "-01";
      }
      this.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.fileDate = "";
      this.queryParams.fileDateStr = "";
      this.resetForm("queryForm");
      this.handleQuery();
    },

    //关闭弹框
    close() {
      this.addData.open = false;
    },

    //导入数据源
    insertData() {
      this.addData.open = true;
    },

    //导出
    exportExcel(url) {
      window.location.href = url;
    },
  },
};
</script>
<style scoped>
.load-url {
  color: #1890ff;
}
</style>


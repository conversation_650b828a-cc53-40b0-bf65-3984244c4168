<template>
  <div class="app-container">
    <el-row :gutter="10" class="mb8">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
        <el-form-item label="报表名称：" prop="fileName">
          <el-input v-model="queryParams.fileName" placeholder="请输入报表名称" clearable />
        </el-form-item>

<!--        <el-form-item label="生成时间">-->
<!--          <el-date-picker-->
<!--            v-model="dateRange"-->
<!--            style="width: 240px"-->
<!--            value-format="yyyy-MM-dd"-->
<!--            type="daterange"-->
<!--            range-separator="-"-->
<!--            start-placeholder="开始日期"-->
<!--            end-placeholder="结束日期"-->
<!--          ></el-date-picker>-->
<!--        </el-form-item>-->
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="insertData">生成报表</el-button>
      </el-col>
    </el-row>
    <el-table v-loading="loading" :data="list" style="width: 100%">
      <el-table-column label="序号" type="index" fixed></el-table-column>
      <el-table-column label="报表名称" prop="fileName" fixed></el-table-column>
      <el-table-column label="报表日期" prop="fileDate" fixed></el-table-column>
      <el-table-column label="操作人员" prop="createUserName" fixed></el-table-column>
      <el-table-column label="生成时间" prop="createTime"></el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            icon="el-icon-download"
            size="mini"
            type="text"
            @click="exportExcel(scope.row.downloadLink)"
          >导出报表</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <model-form :fieldData="fieldData" @close="close" v-if="fieldData.open" @refresh="refresh"></model-form>
  </div>
</template>

<script>
import { getRecordList, deleteDataSource } from "@/api/ffs/reportDataApi";
import modelForm from "./components/modelForm";
export default {
  components: {
    modelForm,
  },
  data() {
    return {
      fieldData: {
        open: false,
        id: "",
      },
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 表格数据
      list: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        fileDate: "",
        fileName: "",
      },
      //dateRange: [],
    };
  },
  async created() {
    this.getList();
  },

  methods: {
    //刷新页面
    refresh() {
      this.getList();
    },

    /** 查询列表 */
    getList() {
      this.loading = true;
      getRecordList(this.queryParams).then((res) => {
        this.loading = false;
        if (res.code == 200) {
          this.list = res.result.list || [];
          this.total = Number(res.result.total || 0);
        } else {
          this.$message.error(res.message);
        }
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.pageNum = 1;

      // if (this.dateRange) {
      //   this.queryParams.startTime = this.dateRange[0];
      //   this.queryParams.endTime = this.dateRange[1];
      // } else {
      //   this.queryParams.startTime = "";
      //   this.queryParams.endTime = "";
      // }

      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },

    //关闭弹框
    close() {
      this.fieldData.open = false;
    },

    //导入数据源
    insertData() {
      this.fieldData.open = true;
      this.fieldData.title = "生成报表";
    },

    //导出
    exportExcel(url) {
      window.location.href = url;
    },
  },
};
</script>
<style scoped>
</style>


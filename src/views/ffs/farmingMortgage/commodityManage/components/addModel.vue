<template>
  <div>
    <el-dialog
      :title="dialogAdd.title"
      :visible.sync="dialogAdd.open"
      width="800px"
      :close-on-click-modal="false"
      @close="refuse"
      class="siteManagement"
      append-to-body
    >
      <el-form :model="form" :rules="rules" ref="ruleForm" label-width="98px" class="demo-ruleForm">
        <el-row>
          <el-col :span="12">
            <el-form-item label="质押品类型" prop="mortgageType">
              <el-select
                v-model="form.mortgageType"
                clearable
                class="selectWidth"
                @change="selectName('ffs_farming_mortgage_type','mortgageTypeName',form.mortgageType)"
                :disabled="dialogAdd.disabled"
              >
                <el-option
                  v-for="dict in dict.type.ffs_farming_mortgage_type"
                  :key="dict.label"
                  :label="dict.label"
                  :value="parseInt(dict.value)"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="商品品类" prop="commodityCategory">
              <el-select
                v-model="form.commodityCategory"
                clearable
                class="selectWidth"
                :disabled="dialogAdd.disabled"
                @change="selectName('ffs_farming_commodity_category','commodityCategoryName',form.commodityCategory)"
              >
                <el-option
                  v-for="dict in dict.type.ffs_farming_commodity_category"
                  :key="dict.label"
                  :label="dict.label"
                  :value="parseInt(dict.value)"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="加工方式" prop="processWay">
              <el-select
                v-model="form.processWay"
                clearable
                class="selectWidth"
                @change="selectName('ffs_farming_machining_type','processWayName',form.processWay)"
                :disabled="dialogAdd.disabled"
              >
                <el-option
                  v-for="dict in dict.type.ffs_farming_machining_type"
                  :key="dict.label"
                  :label="dict.label"
                  :value="parseInt(dict.value)"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="商品类型" prop="commodityType">
              <el-select
                v-model="form.commodityType"
                clearable
                class="selectWidth"
                @change="selectName('ffs_farming_commodity_type','commodityTypeName',form.commodityType)"
                :disabled="dialogAdd.disabled"
              >
                <el-option
                  v-for="dict in dict.type.ffs_farming_commodity_type "
                  :key="dict.label"
                  :label="dict.label"
                  :value="parseInt(dict.value)"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="商品名称" prop="commodityName">
              <el-input
                v-model="form.commodityName"
                placeholder="请输入商品名称"
                clearable
                :disabled="dialogAdd.disabled"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="商品编码" prop="commodityNo">
              <el-input
                v-model="form.commodityNo"
                placeholder="请输入商品编码"
                clearable
                :disabled="dialogAdd.disabled"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="计量单位" prop="commodityUnit">
              <el-select
                v-model="form.commodityUnit"
                clearable
                class="selectWidth"
                @change="selectUnit"
                :disabled="dialogAdd.disabled"
              >
                <el-option
                  v-for="dict in unit"
                  :key="dict.value"
                  :label="dict.text"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="库存单位" prop="inventoryUnit">
              <el-select
                v-model="form.inventoryUnit"
                clearable
                class="selectWidth"
                @change="selectName('ffs_farming_stock_unit','inventoryUnitName',form.inventoryUnit)"
                :disabled="dialogAdd.disabled"
              >
                <el-option
                  v-for="dict in dict.type.ffs_farming_stock_unit"
                  :key="dict.label"
                  :label="dict.label"
                  :value="parseInt(dict.value)"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="商品规格" prop="commoditySpecifications">
              <el-input
                type="number"
                v-model="form.commoditySpecifications"
                placeholder="请输入商品规格"
                clearable
                :disabled="dialogAdd.disabled"
              >
              <template slot="append" >
                <div v-show="form.inventoryUnitName">
                {{form.commodityUnitName}}/{{form.inventoryUnitName}}
            </div>
            </template>
            </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer" v-show="!dialogAdd.disabled">
        <el-button @click="refuse">关闭</el-button>
        <el-button type="primary" @click="submitForm">提交</el-button>
      </span>
    </el-dialog>
  </div>
</template>

  <script>
import {
  addCommodity,
  editCommodity,
  commodityInfo,
  checkCommodity,
} from "@/api/ffs/farmingMortgage/commodityManage";
import { getDicts } from "@/api/system/dict/data.js";
export default {
  dicts: [
    "ffs_farming_mortgage_type",
    "ffs_farming_commodity_category",
    "ffs_farming_commodity_type",
    "ffs_farming_machining_type",
    "ffs_farming_stock_unit",
  ],

  props: {
    dialogAdd: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
      //校验商品编号是否存在
     const checkExistName=(rule, value, callback) =>{
      if (!this.dialogAdd.id) {
        checkCommodity({ commodityNo: value }).then((res) => {
          if (res.code= 200) {
            res.result?callback():callback(new Error('此编号已经存在'))
          }
        });
      }else{
        callback();
      }
    }
     const noneNumber=(rule, value, callback)=>{
        if(value<=0){
            callback(new Error('请输入大于0的数'))
        }else{
            callback()
        }
     }
    return {
        unit:[{text:'千克',value:'kg'}],
      form: {
        mortgageType: "",
        mortgageTypeName: "",
        processWay: "",
        processWayName: "",
        commodityCategory: "",
        commodityCategoryName: "",
        commodityType: "",
        commodityTypeName: "",
        commodityName: "",
        commodityNo: "",
        commodityUnit: "",
        commodityUnitName: "",
        inventoryUnit: "",
        inventoryUnitName: "",
        commoditySpecifications: "",
      },
      rules: {
        mortgageType: [
          {
            required: true,
            message: "请选择质押品类型",
            trigger: "blur",
          },
        ],
        processWay: [
          {
            required: true,
            message: "请选择加工方式",
            trigger: "blur",
          },
        ],
        commodityCategory: [
          {
            required: true,
            message: "请选择商品品类",
            trigger: "blur",
          },
        ],
        commodityType: [
          {
            required: true,
            message: "请选择商品类型",
            trigger: "blur",
          },
        ],
        commodityName: [
          {
            required: true,
            message: "请输入商品名称",
            trigger: "blur",
          },
        ],
        commodityNo: [
          {
            required: true,
            message: "请输入商编码",
            trigger: "blur",
          },
          { validator: checkExistName, trigger: "blur" },
        ],
        commodityUnit: [
          {
            required: true,
            message: "请选择计量单位",
            trigger: "blur",
          },
        ],
        inventoryUnit: [
          {
            required: true,
            message: "请选择库存单位",
            trigger: "blur",
          },
        ],
        commoditySpecifications: [
          {
            required: true,
            message: "请输入商品规格",
            trigger: "blur",
          },{validator:noneNumber,trigger: ['change','blur']}
        ],
      },
    };
  },
  computed: {},
  created() {
    this.getInfo();
  },
  methods: {
    selectUnit(value){
       this.unit.forEach(item=>{
        if(item.value==value){
            this.form.commodityUnitName=item.text
        }
       })
    },
    submitForm() {
      this.$refs["ruleForm"].validate(async (valid) => {
        if (!valid) {
          return;
        } else {
          if (this.dialogAdd.id) {
            this.editInfo();
          } else {
            this.addInfo();
          }
        }
      });
    },
    addInfo() {
      addCommodity(this.form).then((res) => {
        if (res.code == 200) {
          this.$message({
            type: "success",
            message: "添加成功",
          });
          this.$emit("refresh");
          this.refuse();
        }
      });
    },
    editInfo() {
      editCommodity(this.form).then((res) => {
        if (res.code == 200) {
          this.$message({
            type: "success",
            message: "编辑成功",
          });
          this.$emit("refresh");
          this.refuse();
        }
      });
    },
    getInfo() {
      if (!this.dialogAdd.id) {
        return;
      } else {
        commodityInfo({ commodityId: this.dialogAdd.id }).then((res) => {
          if (res.code == 200) {
            this.form = Object.assign(this.form, res.result);
          }
        });
      }
    },

    refuse() {
      this.$emit("close");
    },
    selectName(type, name, value) {
      getDicts(type).then((res) => {
        if (res.code == 200) {
          res.data.forEach((item) => {
            if (item.dictValue == value) {
              this.form[name] = item.dictLabel;
            }
          });
        }
      });
    },
  },
};
</script>

  <style lang="scss">
.siteManagement {
  .el-dialog__header {
    background-color: #f4f4f4;
  }

  .el-dialog__footer {
    text-align: center;
  }

  .selectWidth {
    width: 100%;
  }
  &-info {
    background: #f4f4f4;
    width: 540px;
    margin: 0 0 22px 120px;
    border-radius: 8px;
    padding: 10px 14px;
    &-row {
      padding: 5px 0;
    }
  }
}

.inputWidth {
  width: 100%;
}
</style>

<template>
  <div>加载中</div>
</template>

<script>
  import {
    getToken
  } from "@/utils/auth";
  import axios from "axios";
  import store from '@/store'
  export default {

    data() {
      return {

      }
    },

    beforeRouteEnter(to, from, next) {
      from.meta.title && store.dispatch('settings/setTitle', from.meta.title)
      // 添加一个事件监听器，捕获浏览器的后退按钮事件
      // window.addEventListener('popstate', this.handlePopstate);
      console.log(to, from)
      let fromPath = from.path
      let token = getToken()
      console.log(token)
      console.log(11111111111)


      // window.location.href = process.env.VUE_APP_WMS_LARGE_WEB+'wmsLogin?token=' + token + '&fromPath=' + fromPath
      // alert(111)
      next(vm=>{
        // setTimeout(() => {
          window.open(process.env.VUE_APP_WMS_LARGE_WEB+'wmsLogin?token=' + token + '&fromPath=' + fromPath, '_self');
        // },100)
      })

    },

    created() {

    },
    methods: {
      handlePopstate() {

      }
    }


  };
</script>

<style>
</style>

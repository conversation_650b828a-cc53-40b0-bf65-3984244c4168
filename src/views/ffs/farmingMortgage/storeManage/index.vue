<template>
  <div class="app-container">
    <el-card class="mb10 form_box" shadow="never" ref="formBox">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="140px">
        <el-row class="form_row">
          <el-col class="form_col">
            <el-form-item label="区域查询：" prop="areaDataRange" v-show="regionDom">
                <RegCascaderTag v-model="queryParams.areaDataRange"></RegCascaderTag>
            </el-form-item>
            <el-form-item label="仓库名称：" prop="storehouseName">
              <el-input v-model="queryParams.storehouseName" placeholder="请输入仓库名称" clearable @keyup.enter.native="handleQuery"/>
            </el-form-item>
            <el-form-item label="仓库编号：" prop="storehouseNo">
              <el-input v-model="queryParams.storehouseNo" placeholder="请输入仓库编号" clearable @keyup.enter.native="handleQuery"/>
            </el-form-item>
            <el-form-item label="仓库所属人：" prop="ownerName">
              <el-input v-model="queryParams.ownerName" placeholder="请输入仓库所属人姓名或企业名称" clearable @keyup.enter.native="handleQuery"/>
            </el-form-item>
            <!-- <el-form-item label="创建人：" prop="createUserName">
              <el-input v-model="queryParams.createUserName" placeholder="请输入创建人姓名" clearable />
            </el-form-item> -->
            <el-form-item label="创建时间：">
              <el-date-picker
                v-model="time"
                style="width: 215px"
                value-format="yyyy-MM-dd hh:mm:ss"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              ></el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row style="margin-left: 140px;">
          <el-col>
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
              <template v-if="toggleSearchDom">
                <el-button type="text" @click="packUp">
                  {{ toggleSearchStatus ? '收起' : '展开' }}
                  <i
                    :class="{ 'el-icon-arrow-down': !toggleSearchStatus, 'el-icon-arrow-up': toggleSearchStatus }"
                  ></i>
                </el-button>
              </template>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    <el-card shadow="never">
      <el-row class="mb8 form_btn">
        <el-col class="form_btn_col">
          <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="addForm">新增</el-button>
        </el-col>
      </el-row>
      <!-- 表格数据 -->
      <el-table :data="tableData" stripe style="width: 100%" v-loading="loading" :height="tableHeight" border>
        <el-table-column type="index" label="序号" fixed="left" align="center" width="50"></el-table-column>
        <el-table-column prop="storehouseName" label="仓库名称" show-overflow-tooltip  min-width="150" />
        <el-table-column prop="storehouseNo" label="仓库编号" show-overflow-tooltip  min-width="100"  />
        <el-table-column prop="positionCount" label="货区数量" align="right" width="140px" sortable :sort-method="(a,b) => sortBy(a ,b , 'positionCount')"/>
        <el-table-column prop="ownerName" label="仓库所属人" min-width="150" show-overflow-tooltip/>
        <el-table-column prop="address" label="仓库详细地址" min-width="200" show-overflow-tooltip />
        <el-table-column prop="createUserName" label="创建人" show-overflow-tooltip  min-width="150" />
        <el-table-column prop="createTime" label="创建时间" width="170" align="center" sortable :sort-method="(a,b) => sortBy(a ,b , 'createTime')"/>
        <el-table-column label="操作" align="center" width="220" fixed="right">
          <template slot-scope="scope">
            <el-button
                @click="goCargoAreaList(scope.row.storehouseId,scope.row.storehouseName)"
                type="text"
                size="mini"
                icon="el-icon-thumb"
              >货区管理</el-button>
            <el-button
              @click="editForm(scope.row.storehouseId)"
              type="text"
              icon="el-icon-edit"
              size="mini"
              class="btn_color_t"
            >编辑</el-button>
            <el-button
              @click="deleteInfo(scope.row.storehouseId)"
              type="text"
              icon="el-icon-delete"
              size="mini"
              class="btn_color_f"
            >删除</el-button>
            <el-popover style="margin-left:10px"
                placement="right"
                trigger="click"
                >
              <el-button
                @click="goMonitorList(scope.row.storehouseId)"
                type="text"
                icon="el-icon-video-camera"
                size="mini"
                v-hasPermi="['ffs:video:list']"
              >视频监控</el-button>
              <el-button
                @click="goOffLine(scope.row.storehouseId)"
                type="text"
                icon="el-icon-s-platform"
                size="mini"
                v-hasPermi="['ffs:video:list']"
              >离线视频</el-button>
              <el-button
                @click="goSensorList(scope.row.storehouseId,scope.row.storehouseName)"
                type="text"
                size="mini"
                icon="el-icon-setting"
              >传感器管理</el-button>
              <el-button slot="reference" type="text" size="mini"  class="btn_color_three">
                <!-- 更多 -->
                <i class="el-icon-more"></i>
              </el-button>
            </el-popover>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>
    <model-form :dialogAdd="dialogAdd" v-if="dialogAdd.open" @close="close" @refresh="refresh"></model-form>
  </div>
</template>

  <script>
import { storeHousList, deleteById } from "@/api/ffs/farmingMortgage/storeManage.js";
import modelForm from "./components/modelForm.vue";
import { tableUi } from "@/utils/mixin/tableUi.js";
export default {
  name: "farmingStorehouse",
  mixins: [tableUi],
  components: {
    modelForm,
  },
  data() {
    return {
      //新增
      dialogAdd: {
        open: false,
        title: "",
        id: "",
      },
      time: [],
      queryParams: {
        storehouseName: undefined,
        storehouseNo: undefined,
        ownerName: undefined,
        createUserName: undefined,
        areaDataRange:'',
        pageNum: 1,
        pageSize: 20,
      },
      loading: true,
      total: 0,
      tableData: [],
    };
  },
  computed: {},
  created() {
    this.getList();
  },

  methods: {
    sortBy(a,b,key){
        let at=a[key]
        let bt=b[key]
        if(key=='createTime'){
            return  at-bt
        }else{
            return parseFloat(at) - parseFloat(bt)
        }
    },
    //列表查询
    getList() {
      storeHousList(this.queryParams).then((res) => {
        if (res.code == 200) {
          this.tableData = res.result.list;
          this.total = Number(res.result.total);
          this.loading = false;
        }
      });
    },
    reset() {
      this.resetForm("queryForm");
    },
    close() {
      this.dialogAdd.open = false;
      this.dialogAdd.id = "";
    },
    //重置
    resetQuery() {
      this.time = [];
      this.reset();
      this.handleQuery();
    },
    //刷新页面
    refresh() {
      this.getList();
    },
    //搜索
    handleQuery() {
      this.queryParams.pageNum = 1;
      if (this.time && this.time.length > 0) {
        this.queryParams.createStartTime = this.time[0];
        this.queryParams.createEndTime = this.time[1];
      } else {
        this.queryParams.createStartTime = undefined;
        this.queryParams.createEndTime = undefined;
      }
      this.getList();
    },

    goMonitorList(id) {
      this.$router.push({
        path: "/farmingCargoArea/monitorList",
        query: {
          storehouseId: id,
        },
      });
    },
    // 传感器管理
    goSensorList(id, name) {
      console.log(id);
      this.$router.push({
        path: "/cargoArea/sensorList",
        query: {
          storehouseId: id,
          sourceName: encodeURIComponent(name),
        },
      });
    },
    //删除
    deleteInfo(id) {
      this.$confirm("确定删除吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          deleteById({ ids: [id] }).then((res) => {
            if (res.code == 200) {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              this.getList();
            }
          });
        })
        .catch(() => {});
    },
    //创建仓库
    addForm() {
      this.dialogAdd.open = true;
      this.dialogAdd.title = "新增";
    },
    editForm(id) {
      this.dialogAdd.id = id;
      this.dialogAdd.open = true;
      this.dialogAdd.title = "编辑";
    },
    goCargoAreaList(id, title) {
      this.$router.push({
        path: "/farmingCargoArea/cargoAreaList",
        query: {
          id: id,
          title: title,
        },
      });
    },
    goOffLine(id) {
      this.$router.push({
        path: "/farmingPasture/offLineVideo",
        query: { id: id, type: "2" },
      });
    },
  },
};
</script>

  <style lang="scss" scoped>
</style>

<template>
    <div class="app-container">
        <el-card style="padding:10px 0" shadow="never">
            <div class="clearfix" v-if="headBar">
                <el-button type="success" size="mini" icon="el-icon-arrow-left" @click="goback" v-show="!storehouseId">返回</el-button>
                <div style="display: flex; justify-content: end; width: 100%;">
                    <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="addMonitor" v-hasPermi="['ffs:video:add']">新增视频设备</el-button>
                </div>
            </div>
            <div>
                <el-row :gutter="20" style="padding:10px 0;font-size:16px ;font-weight: 700;" v-if="list.length">
                    <el-col :span="14">
                        <!-- <div style="padding:10px 0">仓库名称名称：{{storehouseData.storehouseName}}</div>
            <div>仓库地址：{{storehouseData.address}}</div> -->
                        <span>视频监控总数：{{ list.length }}</span>
                    </el-col>
                    <!-- <el-col :span="10">
            <div class="monitor-census">
              <span>视频监控总数：{{list.length}}</span>
              <span>在线：19</span>
              <span>离线：19</span>
            </div>
          </el-col> -->
                </el-row>
                <div class="monitor-conter"  style="display: flex; flex-wrap: wrap;">
                    <div style="padding: 10px 6px;" v-for="item  in list" :key="item.videoId">
                        <div class="monitor-conter-imge">
                            <img :src="item.coverPicture" alt style="width:100%;height: 100%;" v-show="item.coverPicture" @click="pay(item)" />
                            <img src="@/assets/images/ffs/monitor.png" alt style="width:100%;height: 100%;" v-show="!item.coverPicture" @click="pay(item)" />
                            <div class="monitor-conter-imge-onLine" :style="{'color':item.status == 1 ? '#04FF00' : '#FF4302'}">{{ item.status == 1 ? '在线' : (item.status != null ? '离线' : '未知') }}</div>
                        </div>
                        <div style="display: flex; justify-content: space-between;">
                            <div class="monitor-conter-imge-number">仓库名称名称：{{ storehouseData.storehouseName}}</div>
                            <div>
                                <el-popover
                                placement="left"
                                trigger="hover"
                                content="">
                                <div style="display: flex; justify-content: space-around; cursor: pointer;">
                                    <div  @click="editPastureVideo(item)" v-if="headBar" v-hasPermi="['ffs:video:edit']">编辑</div>
                                    <div v-hasPermi="['ffs:video:delete']"  @click="deletePastureVideo(item)" v-if="headBar">删除</div>
                                </div>
                                <img  slot="reference" src="@/assets/images/ffs/monitorBtn.png"  style="width:20px;height: 20px" />
                            </el-popover>
                         </div>
                        </div>
                        <div class="monitor-conter-address" >安装地点：{{ item.mountLocation }}</div>
                    </div>
                </div>
            </div>
            <div v-if="list.length == 0" style="text-align: center;color:#909399">暂无数据</div>
        </el-card>
        <addMonitor v-if="monitor.open" :monitor="monitor" @close="close" @refresh="refresh" ref="addMonitor"></addMonitor>
        <videoModel v-if="videoOpen.open" :videoOpen="videoOpen" @close="close"></videoModel>
    </div>
</template>
<script>
import addMonitor from "./components/addMonitor.vue";
import videoModel from "@/views/ffs/supervisionSheet/siteManagement/components/videoModel.vue";

import {
    videoList,
    selectById,
    delVideo,
    cameraBusinessDel
} from "@/api/ffs/farmingMortgage/storeManage.js";
export default {
    components: {
        addMonitor,
        videoModel,
    },
    props: {
        headBar: {
            typeof: Boolean,
            default: true,
        },
        storehouseId: {
            type: String,
            default: "",
        },
    },
    data() {
        return {
            monitor: {
                open: false,
                objItem: {},
            },
            videoOpen: {
                open: false,
                url: "",
            },
            storehouseData: {},
            monitorToast: 0,
            list: [],
        };
    },
    computed: {},

    watch: {
        storehouseId: {
            handler(newName, oldName) {

                this.getList();
                this.getStoreHouse();
            },
            immediate: true,
            deep: true,
        },
    },
    created() {
        if (this.headBar) {
            this.getList();
            this.getStoreHouse();
        }
    },

    methods: {
        //查看仓库详情
        getStoreHouse() {
            selectById({
                ids: [this.$route.query.storehouseId || this.storehouseId],
            }).then((res) => {
                if (res.code == 200) {
                    this.storehouseData = res.result;
                }
            });
        },

        //列表查询
        getList() {
            videoList({
                storehouseId: this.$route.query.storehouseId || this.storehouseId,
            }).then((res) => {
                if (res.code == 200) {
                    const result = res.result.list || res.result || [];
                    console.log("视频监列表查询：", res);
                    this.list = result;
                }
            });
        },
        //删除
        deletePastureVideo(item) {
            this.$confirm("此操作将永久删除, 是否继续?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    if (item.ownerFlag == 1) {
                        cameraBusinessDel({ ids: [item.cameraBusinessId] }).then(res => {
                            if (res.code == 200) {
                                this.$message({
                                    type: "success",
                                    message: "删除成功!",
                                });
                                this.getList();
                            }
                        })
                    } else {
                        delVideo({ ids: [item.videoId] }).then((res) => {
                            if (res.code == 200) {
                                this.$message({
                                    type: "success",
                                    message: "删除成功!",
                                });
                                this.getList();
                            }
                        });
                    }

                })
                .catch(() => {
                    this.$message({
                        type: "info",
                        message: "已取消删除",
                    });
                });
        },
        //视频编辑
        editPastureVideo(item) {
            this.monitor.open = true;
            this.monitor.objItem = JSON.parse(JSON.stringify(item))
            this.$nextTick(() => {
                this.$refs.addMonitor.title = '编辑'
                this.$refs.addMonitor.disabled = true
                this.$refs.addMonitor.ownerFlag = item.ownerFlag
                this.$refs.addMonitor.storehouseName = this.storehouseData.storehouseName
            });

        },
        refresh() {
            this.getList();
        },
        //页面返回
        goback() {
            this.$tab.closeOpenPage();
            this.$router.go(-1);
        },
        close() {
            this.monitor.open = false;
            this.monitor.objItem = {}
            this.videoOpen.open = false;
        },
        addMonitor() {
            this.monitor.open = true;
            this.$nextTick(() => {
                this.$refs.addMonitor.title = '新增'
                this.$refs.addMonitor.disabled = false
                this.$refs.addMonitor.storehouseName = this.storehouseData.storehouseName
                this.$refs.addMonitor.form.storehouseId=this.$route.query.storehouseId || this.storehouseId
            });
        },
        //视频播放
        pay(item) {
            this.videoOpen.open = true;
            this.videoOpen.url = item;
            console.log(item);
        },
    },
};
</script>
<style lang="scss" scoped>
video::-webkit-media-controls-timeline {
    display: none;
}
::v-deep.el-popover{
    width: 100px !important;
}
.clearfix {
    display: flex;
    justify-content: space-between;
}

.monitor {
    &-census {
        font-weight: 400;
        display: flex;
        width: 100%;
        justify-content: flex-end;

        span {
            padding: 0 10px;
        }
    }

    &-conter {
        display: inline-block;
        font-size: 14px;
        &-address{
            width: 400px;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 1;
        }
        &-imge {
            position: relative;
            display: inline-block;
            overflow: hidden;
            box-sizing: border-box;
            width: 400px;
            height: 260px;
            border-radius: 10px;

            &-onLine {
                position: absolute;
                right: 10px;
                top: 10px;
                color: #13ce66;
            }

            &-play {
                font-size: 70px;
                position: absolute;
                left: 50%;
                top: 50%;
                z-index: 1;
                color: black;
                transform: translate(-50%, -50%);
            }

            &-delete {
                position: absolute;
                right: 10px;
                bottom: 10px;
                color: red;
                z-index: 1;
                cursor: pointer;
            }

            &-edit {
                position: absolute;
                right: 11px;
                bottom: 40px;
                color: #5173FF;
                z-index: 1;
                cursor: pointer;
            }
        }
    }
}
</style>
  
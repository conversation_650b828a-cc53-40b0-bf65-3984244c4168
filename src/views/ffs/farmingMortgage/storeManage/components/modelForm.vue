<template>
  <div>
    <el-dialog
      :title="dialogAdd.title"
      :visible.sync="dialogAdd.open"
      width="900px"
      :close-on-click-modal="false"
      @close="refuse"
      class="addMonitor"
      append-to-body
    >
      <el-form
        :model="form"
        :rules="rules"
        ref="ruleForm"
        label-width="95px"
        class="demo-ruleForm demo-dynamic"
      >
        <el-form-item label="仓库名称" prop="storehouseName">
          <el-input v-model.trim="form.storehouseName" placeholder="请输入仓库名称"></el-input>
        </el-form-item>
        <el-form-item label="仓库编号" prop="storehouseNo">
          <el-input v-model.trim="form.storehouseNo" placeholder="请输入仓库编号" :disabled="dialogAdd.id!=''"></el-input>
        </el-form-item>
        <el-form-item label="仓库所属人" prop="ownerName">
          <el-input v-model.trim="form.ownerName" placeholder="请输入仓库所属人姓名或企业名称"></el-input>
        </el-form-item>
        <template v-if="dialogAdd.id==''">
        <el-form-item
          :label="'货区名称'+(index + 1)"
          :prop="'positionList.'+index+'.positionName'"
          v-for="(item, index) in form.positionList"
          :key="index"
          :rules="{required: true, message: '货区名称不能为空', trigger: 'blur' }"
        >
          <div class="domains">
            <el-input
              maxlength="10"
              v-model.trim="item.positionName"
              placeholder="请输入货区名称"
              style="margin-right:5px"
              clearable
            ></el-input>
            <i
              class="el-icon-delete"
              @click.prevent="removeDomain(index)"
              style="font-size:22px"
              v-show="index>0"
            ></i>
            <i
              class="el-icon-circle-plus-outline"
              @click.prevent="addDomain"
              style="font-size:22px"
              v-show="index==0"
            ></i>
          </div>
          <span v-show="form.positionList.length-1==index" style="color:red">注：仓库默认至少有一个货区，可增加多个</span>
        </el-form-item>
    </template>
        <el-form-item label="仓库地址：" prop="areas">
              <el-cascader
                class="inputWidth"
                :options="areaProps"
                placeholder="请选择仓库地址"
                ref="city"
                @change="goodsSourceChange"
                v-model="form.areas"
              ></el-cascader>
            </el-form-item>
        <el-form-item label="详细地址" prop="address">
            <el-input
              v-model.trim="form.address"
              placeholder="请输入详细地址"
              clearable
            ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="refuse">关闭</el-button>
        <el-button type="primary" @click="submitForm">提交</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  addStoreHous,
  selectById,
  editStoreHouse,
  checkStoreNo
} from "@/api/ffs/farmingMortgage/storeManage.js";
import {loopArry } from '@/utils/getaeraDataRange'
import { getAreaTreeList} from "@/api/ffs/home.js";
export default {
  props: {
    dialogAdd: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      dom: false,
      areaProps:[],
      listString: this.$store.state.user.user.areaDataRange,
      form: {
        areas:[],
        areaDataRange:'',
        storehouseName: "",
        storehouseNo: "",
        ownerName: "",
        latitudeLongitude: "",
        address: "",
        positionList: [
          {
            positionName: "",
          },
        ],
      },
      rules: {
        storehouseName: [
          {
            required: true,
            message: "请输入仓库名称",
            trigger: "blur",
          },
        ],
        areas:[
        {
            required: true,
            message: "请输入仓库地址",
            trigger: "blur",
          },
        ],
        storehouseNo: [
          {
            required: true,
            message: "请输入仓库编号",
            trigger: "blur",
          },
          { validator: this.checkExistName, trigger: "blur" },
        ],
        ownerName: [
          {
            required: true,
            message: "请输入仓库所属人姓名或企业名称",
            trigger: "blur",
          },
        ],
        address: [
          {
            required: true,
            message: "请输入详细地址",
            trigger: "blur",
          },
        ],
      },
    };
  },
  created() {
    this.getIfno();
        // this.getAreaList()
        this.areaProps=loopArry()

  },
  methods: {
    // getAreaList(){
    //     getAreaTreeList({}).then(res=>{
    //         this.areaProps=res.result
    //     })
    // },
    checkExistName(rule, value, callback){
        if(this.dialogAdd.id){
            callback()
        }else{
            checkStoreNo({storehouseNo:value}).then(res=>{
            callback()
        })

        }
    },
    goodsSourceChange(val){
        let list=JSON.parse(JSON.stringify(val))
      list.unshift('0')
     this.form.areaDataRange=list.toString()
    },
    getIfno() {
      if (!this.dialogAdd.id) {
        this.dom = true;
        return;
      } else {
        selectById({ ids: [this.dialogAdd.id] }).then((res) => {
          if (res.code == 200) {
            this.form = Object.assign(this.form, res.result);
            this.form.areas=[]
                this.handelData(this.form.areaDataRange,this)
            this.dom = true;
          }
        });
      }
    },
     handelData :(value,that) =>{
         if(value){
                let list=value.split(',');
                list.shift()
                that.form.areas=list
            }

        },
    //仓单编辑
    editInfo() {
      editStoreHouse(this.form).then((res) => {
        if (res.code == 200) {
          this.$message({
            type: "success",
            message: "编辑成功",
          });
          this.$emit("refresh");
          this.refuse();
        }
      });
    },
    //添加仓库
    addInfo() {
      addStoreHous(this.form).then((res) => {
        if (res.code == 200) {
          this.$message({
            type: "success",
            message: "添加成功",
          });
          this.$emit("refresh");
          this.refuse();
        }
      });
    },
    submitForm() {
      this.$refs["ruleForm"].validate(async (valid) => {
        if (!valid) {
          return;
        }
        if (this.dialogAdd.id) {
          this.editInfo();
        } else {
          this.addInfo();
        }
      });
    },

    refuse() {
      this.$emit("close");
    },

    removeDomain(index) {
      this.form.positionList.splice(index, 1);
    },
    //添加表单域
    addDomain() {
      this.form.positionList.push({ positionName: ""});
    },
    mapPosition(value) {
      this.form.address = value.position.address;
      this.form.latitudeLongitude = value.position.addressLoglat;
    },
  },
};
</script>

      <style lang="scss">
.addMonitor {
  .el-dialog__header {
    background-color: #f4f4f4;
  }

  .el-dialog__footer {
    text-align: center;
  }

  .selectWidth {
    width: 100%;
  }
  &-info {
    background: #f4f4f4;
    width: 540px;
    margin: 0 0 22px 120px;
    border-radius: 8px;
    padding: 10px 14px;
    &-row {
      padding: 5px 0;
    }
  }
  .domains {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

.inputWidth {
  width: 100%;
}
</style>

<template>
  <div>
    <el-dialog
      :title="dialogAdd.title"
      :visible.sync="dialogAdd.open"
      width="900px"
      :close-on-click-modal="false"
      @close="refuse"
      class="addMonitor"
      append-to-body
    >
      <el-form
        :model="form"
        :rules="rules"
        ref="ruleForm"
        label-width="80px"
        class="demo-ruleForm demo-dynamic"
      >
        <el-form-item label="货区号" prop="positionName">
          <el-input v-model.trim="form.positionName" placeholder="请输入货区号"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="refuse">关闭</el-button>
        <el-button type="primary" @click="submitForm">提交</el-button>
      </span>
    </el-dialog>
  </div>
</template>
    
  <script>
import { addPosition, editPosition } from "@/api/ffs/farmingMortgage/storeManage.js";
import MapControl from "@/views/ffs/supervisionSheet/livingSupervision/components/addForm/MapControl.vue";
export default {
  components: {
    MapControl,
  },
  props: {
    dialogAdd: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      form: {
        storehouseId: "",
        positionName: "",
      },
      rules: {
        positionName: [
          {
            required: true,
            message: "请输入货区名称",
            trigger: "blur",
          },
        ],
      },
    };
  },

  created() {
    this.form.storehouseId = this.dialogAdd.storehouseId;
  },
  methods: {
    submitForm() {
      this.$refs["ruleForm"].validate(async (valid) => {
        if (!valid) {
          return;
        }
        if(this.dialogAdd.id){
            this.editInfo()
        }else{
     
            this.addInfo()
        }
      });
    },
    //新增
    addInfo() {
      addPosition(this.form).then((res) => {
        if (res.code == 200) {
          this.$message({
            type: "success",
            message: "添加成功",
          });
          this.$emit("refresh");
          this.refuse();
        }
      });
    },
    //编辑
    editInfo(){
        editPosition(this.form).then(res=>{
            if (res.code == 200) {
          this.$message({
            type: "success",
            message: "编辑成功",
          });
          this.$emit("refresh");
          this.refuse();
        }
      });
    },
    refuse() {
      this.$emit("close");
    },
  },
};
</script>
    
        <style lang="scss">
.addMonitor {
  .el-dialog__header {
    background-color: #f4f4f4;
  }
  .el-dialog__footer {
    text-align: center;
  }
}
</style>
    
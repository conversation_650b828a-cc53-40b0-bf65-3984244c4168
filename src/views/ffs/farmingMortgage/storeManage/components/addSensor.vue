<template>
  <div>
    <el-dialog
      :title="dialogAdd.title"
      :visible.sync="dialogAdd.open"
      width="600px"
      :close-on-click-modal="false"
      @close="refuse"
      class="addMonitor"
      append-to-body
    >
      <el-form
        :model="form"
        :rules="rules"
        ref="ruleForm"
        label-width="80px"
        class="demo-ruleForm demo-dynamic"
      >
        <el-form-item label="仓库名称">
          <span style="font-size: 18px; font-weight: 700;">{{ form.sourceName }}</span>
        </el-form-item>
        <el-form-item label="设备类型" prop="equipmentType">
          <el-select v-model="form.equipmentType" placeholder="请选择" style="width: 100%;">
            <el-option
              v-for="item in dataType"
              :label="item.label"
              :value="item.value"
              :key="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="设备编号" prop="equipmentCode">
          <el-input v-model.trim="form.equipmentCode" placeholder="请输入设备编号"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="refuse">关闭</el-button>
        <el-button type="primary" @click="submitForm">提交</el-button>
      </span>
    </el-dialog>
  </div>
</template>
      
<script>
import { equipmentAdd, equipmentEdit } from "@/api/ffs/farmingMortgage/storeManage.js";
import MapControl from "@/views/ffs/supervisionSheet/livingSupervision/components/addForm/MapControl.vue";
export default {
  components: {
    MapControl,
  },
  props: {
    dialogAdd: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      form: {
        businessType: "2",
        sourceId: "",
        sourceName: "",
        equipmentType: "",
        equipmentCode: "",
      },
      dataType: [{ label: "传感器", value: 1 }],
      rules: {
        equipmentType: [
          {
            required: true,
            message: "请选择",
            trigger: "blur",
          },
        ],

        equipmentCode: [
          {
            required: true,
            message: "请输入设备编号",
            trigger: "blur",
          },
        ],
      },
    };
  },

  created() {
    this.form.sourceId = this.$route.query.storehouseId;
    this.form.sourceName = decodeURIComponent(this.$route.query.sourceName);
 
  },
  methods: {
    submitForm() {
      this.$refs["ruleForm"].validate(async (valid) => {
        if (!valid) {
          return;
        }
        if (this.form?.superviseEquipmentId) {
          this.editInfo();
        } else {
          this.addInfo();
        }
      });
    },
    //新增
    addInfo() {
        equipmentAdd(this.form).then((res) => {
        if (res.code == 200) {
          this.$message({
            type: "success",
            message: "添加成功",
          });
          this.$emit("refresh");
          this.refuse();
        }
      });
    },
    //编辑
    editInfo() {
        equipmentEdit(this.form).then((res) => {
        if (res.code == 200) {
          this.$message({
            type: "success",
            message: "编辑成功",
          });
          this.$emit("refresh");
          this.refuse();
        }
      });
    },
    refuse() {
      this.$emit("close");
    },
  },
};
</script>
      
<style lang="scss">
.addMonitor {
  .el-dialog__header {
    background-color: #f4f4f4;
  }

  .el-dialog__footer {
    text-align: center;
  }
}
</style>
      
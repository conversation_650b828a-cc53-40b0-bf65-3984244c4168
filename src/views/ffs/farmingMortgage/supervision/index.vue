<template>
  <div class="app-container">
    <el-card class="mb10 form_box" shadow="never" ref="formBox">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true"  label-width="140px">
            <el-row class="form_row">
                <el-col class="form_col">
                    <el-form-item label="区域查询：" prop="areaDataRange" v-show="regionDom">
                        <RegCascaderTag v-model="queryParams.areaDataRange" :multiple="true"></RegCascaderTag>
                    </el-form-item>
                        <el-form-item label="监管状态：" prop="superviseStatus">
                            <el-select v-model="queryParams.superviseStatus" clearable>
                                <el-option label="全部" value />
                                <el-option label="待监管" value="1" />
                                <el-option label="监管中" value="2" />
                                <el-option label="已结束" value="3" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="续单状态：" prop="operateType">
                        <el-select v-model="queryParams.operateType" clearable>
                            <el-option label="全部" value="" />
                            <el-option label="展期" value="1" />
                            <el-option label="续贷" value="2" />
                            <el-option label="续约" value="3" />
                        </el-select>
                </el-form-item>
                    <el-form-item label="被监管方：" prop="applyName">
                        <el-input v-model="queryParams.applyName" placeholder="请输入被监管方名称" clearable @keyup.enter.native="handleQuery"/>
                    </el-form-item>
                    <el-form-item label="被监管方联系电话：" prop="applyPhone" >
                        <el-input
                            v-model="queryParams.applyPhone"
                            placeholder="请输入被监管方联系电话"
                            type="number"
                            oninput="if(value.length>11)value=value.slice(0,11)"
                            clearable
                            @keyup.enter.native="handleQuery"
                        />
                    </el-form-item>
                        <el-form-item label="委托方：" prop="bankName">
                            <el-input v-model="queryParams.bankName" placeholder="请输入委托方名称" clearable @keyup.enter.native="handleQuery"/>
                        </el-form-item>
                        <el-form-item label="客户经理：" prop="loanOfficerName">
                            <el-input v-model="queryParams.loanOfficerName" placeholder="请输入客户经理姓名" clearable @keyup.enter.native="handleQuery"/>
                        </el-form-item>

                        <el-form-item label="监管员：" prop="supervisorName">
                            <el-input v-model="queryParams.supervisorName" placeholder="请输入监管员姓名" clearable @keyup.enter.native="handleQuery"/>
                        </el-form-item>
                        <el-form-item label="监管期限：" prop="supervisePeriodList">
                          <el-select v-model="supervisePeriodList" multiple clearable>
                            <el-option label="近七天到期" value="1" />
                            <el-option label="已超出监管期限" value="2" />
                          </el-select>
                        </el-form-item>
                        <!-- <el-form-item label="监管时间：">
          <el-date-picker
            v-model="superviseTime"
            style="width: 215px"
            value-format="yyyy-MM-dd"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
        </el-form-item>
 -->
                        <el-form-item label="创建时间：">
                            <el-date-picker
                                v-model="dateRange"
                                style="width: 215px"
                                value-format="yyyy-MM-dd"
                                type="daterange"
                                range-separator="-"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                            ></el-date-picker>
                        </el-form-item>
                    <el-form-item label="监管单号：" prop="superviseId">
                        <el-input v-model="queryParams.superviseId" placeholder="请输入监管单号" clearable  @input="queryParams.superviseId=queryParams.superviseId.replace(/[^0-9]/g,'')" @keyup.enter.native="handleQuery"/>
                    </el-form-item>
                  <el-form-item label="硬件拆除情况：" prop="isSelectFileInsurance">
                    <el-select v-model="queryParams.deviceFlag" clearable>
                      <el-option label="是" :value="1" />
                      <el-option label="否" :value="0" />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="服务费结清情况：" prop="isSelectFileInsurance">
                    <el-select v-model="queryParams.superviseFeeFlag" clearable>
                      <el-option label="是" :value="1" />
                      <el-option label="否" :value="0" />
                    </el-select>
                  </el-form-item>
                </el-col>
            </el-row>
            <el-row style="margin-left: 140px;">
                <el-col >
                    <el-form-item >
                    <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                    <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                    <template v-if="toggleSearchDom">
                        <el-button type="text" @click="packUp" >
                        {{ toggleSearchStatus ? '收起' : '展开' }}
                        <i
                        :class="{ 'el-icon-arrow-down': !toggleSearchStatus, 'el-icon-arrow-up': toggleSearchStatus }"
                        ></i>
                    </el-button>
                    </template>

                    </el-form-item>
                </el-col>
             </el-row>
        </el-form>
      </el-card>
    <el-card shadow="never">
        <el-row class="mb8 form_btn">
        <el-col  class="form_btn_col">
            <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="insertData"
            v-hasPermi="['ffs:mortgage:supervision:add']"
            >通过意向单新增仓单监管</el-button>
            <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            v-hasPermi="['ffs:mortgage:supervision:add']"
            @click="addSupervisionNoIntention"
            >无意向单新增仓单监管</el-button>
            <el-button
            v-hasPermi="['ffs:supervision:empower']"
            type="primary"
            plain
            icon="el-icon-edit"
            size="mini"
            @click="batchAddUser"
            >授权</el-button>
            <el-button
            type="primary"
            plain
            icon="el-icon-download"
            size="mini"
            v-hasPermi="['ffs:mortgage:supervision:add']"
            @click="exportList"
            >导出</el-button>
        </el-col>
        </el-row>
        <el-table v-loading="loading" :data="list"  @selection-change="handleSelectionChange" ref="multipleTable" :height="tableHeight" class="className" border>
            <el-table-column type="selection" width="50" align="center"    v-if="columnDom"/>
        <el-table-column label="序号" type="index" fixed align="center" width="50" ></el-table-column>
        <el-table-column
            label="监管状态"
            prop="superviseStatus"
            width="90px"
            fixed

        >
        <template slot-scope="scope">
            <div class="round">
                <div class="round-dot" :style="{background:handelColor(scope.row.superviseStatus)}"></div>
                <span :style="{color:handelColor(scope.row.superviseStatus)}">  {{ superviseStatusName(scope.row.superviseStatus) }}</span>
                <el-tooltip class="item" effect="dark" :content="scope.row.closeDesc" placement="top" v-show="(scope.row.closeReason==2||scope.row.closeReason==3)&& scope.row.superviseStatus==3" style="color:#FF4F34">
                     <i class="el-icon-warning"></i>
                </el-tooltip>
                <el-tooltip class="item" effect="dark" :content="handelText(scope.row)" placement="top" v-show="scope.row.superviseStatus==2&&scope.row.expireAndExceedFlag==1 " style="color:#FF9901">
                    <i class="el-icon-warning"></i>
                </el-tooltip>
            </div>
        </template>
        </el-table-column>
        <!-- <el-table-column label="监管方式"  width="100px" fixed align="center">
            <template slot-scope="scope">
                {{ scope.row.superviseWay==2?'政府监管':'抵押监管' }}
            </template>
        </el-table-column> -->
        <el-table-column label="被监管方" prop="applyName" width="150px" show-overflow-tooltip>
        <template slot-scope="scope">
            <span class="delayOperateType">
                    <img src="@/assets/images/ffs/delayOperateType1.png" class="delayOperateType-img" v-show="scope.row.delayOperateType==1">
                    <img src="@/assets/images/ffs/delayOperateType2.png" class="delayOperateType-img" v-show="scope.row.delayOperateType==2">
                    <img src="@/assets/images/ffs/delayOperateType3.png" class="delayOperateType-img" v-show="scope.row.delayOperateType==3">
                    <span class="delayOperateType-img" v-show="!scope.row.delayOperateType"></span>
                    <span>{{ scope.row.applyName }}</span>
            </span>
        </template>
      </el-table-column>
        <el-table-column label="联系电话" prop="applyPhone" width="110px"></el-table-column>
        <el-table-column label="委托方" prop="bankName" width="200px" show-overflow-tooltip></el-table-column>
        <el-table-column label="客户经理" prop="loanOfficerName" width="110px" show-overflow-tooltip></el-table-column>
        <el-table-column label="监管员" prop="supervisorName" width="124px" show-overflow-tooltip></el-table-column>
        <el-table-column label="监管时间" prop="superviseEnd" width="185px" :formatter="superviseDate" align="center" ></el-table-column>
        <el-table-column label="监管周期(月)" prop="superviseLimit" width="130px" align="right" sortable :sort-method="(a,b) => sortBy(a ,b , 'superviseLimit')" ></el-table-column>
        <el-table-column label="授信金额(万元)" prop="superviseAmount" width="140px" align="right" sortable :sort-method="(a,b) => sortBy(a ,b , 'superviseAmount')"></el-table-column>
        <el-table-column label="服务费金额(元)" prop="superviseServiceFee" min-width="140px"  align="right" sortable :sort-method="(a,b) => sortBy(a ,b , 'superviseServiceFee')"></el-table-column>
        <el-table-column label="监管单号" prop="superviseId" width="170px"></el-table-column>
        <el-table-column label="质押重量(吨)" prop="mortgageTotalWeight" min-width="130px" align="right" sortable :sort-method="(a,b) => sortBy(a ,b , 'mortgageTotalWeight')"></el-table-column>
        <el-table-column label="质押货值(万元)" prop="mortgageTotalAmount" width="140px" align="right" sortable :sort-method="(a,b) => sortBy(a ,b , 'mortgageTotalAmount')"></el-table-column>
        <el-table-column label="创建时间" prop="createTime" width="175px" sortable :sort-method="(a,b) => sortBy(a ,b , 'createTime')"></el-table-column>
        <el-table-column label="备注说明" prop="fileRemark" width="150px" show-overflow-tooltip></el-table-column>
        <el-table-column label="操作" align="left" fixed="right" width="220px">
            <template slot-scope="scope">
                <el-button
                size="mini"
                type="text"
                @click="handDetails(scope.row.superviseId, scope.row.applyId)"
                v-hasPermi="['ffs:mortgage:supervision:edit']"
                icon="el-icon-info"
            >详情</el-button>
            <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-coordinate"
                    :disabled="(scope.row.offlineVideoUrl.length>0||scope.row.onlineVideoUrl.length>0)?false:true"
                    @click="supervisorVideo(scope.row.offlineVideoUrl,scope.row.onlineVideoUrl)"
                    v-hasPermi="['ffs:supervision:edit']"
                    :class="{'btn_color_t':handelBtn(scope.row)}"
                >视频</el-button>
            <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                class="btn_color_t"
                v-if="scope.row.superviseStatus == 1"
                @click="handleEdit(scope.row)"
                v-hasPermi="['ffs:mortgage:supervision:edit']"
            >编辑</el-button>
            <el-button
                v-if="scope.row.superviseStatus==2"
                icon="el-icon-circle-close"
                size="mini"
                type="text"
                class="btn_color_f"
                @click="settleFeed(scope.row)"
                v-hasPermi="['ffs:mortgage:supervision:stop']"
            >关闭</el-button>
            <el-popover
                placement="left"
                width="50"
                trigger="click"
                class="btn_color_f"
                popper-class="my-popover"
                v-show="haveMenu(['ffs:mortgage:supervision:edit','ffs:mortgage:supervision:delete','ffs:supervision:mortgage:cleanDevice','ffs:supervision:mortgage:cleanSuperviseFee'],[2,1,3],scope.row.superviseStatus)"
            >

                  <el-tooltip class="item" effect="dark" content="追加用信金额记录" placement="top-start">
                    <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-s-finance"
                      v-if="scope.row.superviseStatus == 2"
                      @click="useLetterDig(scope.row)"
                    >追加</el-button>
                  </el-tooltip>

                <el-col :span="12">
                <el-tooltip class="item" effect="dark" content="上传/查看附件" placement="top-start">
                  <el-button
                    size="mini"
                    type="text"
                    v-show="scope.row.superviseStatus!=3"
                    icon="el-icon-folder-opened"
                    @click="editFiles(scope.row)"
                    v-hasPermi="['ffs:mortgage:supervision:edit']"
                  >附件</el-button>
                </el-tooltip>
                <el-tooltip class="item" effect="dark" content="续单" placement="top-start">
                  <el-button
                    size="mini"
                    type="text"
                    v-if="scope.row.superviseStatus==2"
                    icon="el-icon-eleme"
                    @click="putOff(scope.row)"
                    v-hasPermi="['ffs:supervision:mortgage:extension']"
                  >续单</el-button>
                </el-tooltip>

                <el-button
                  v-if="scope.row.superviseStatus == 1"
                  icon="el-icon-delete"
                  size="mini"
                  type="text"
                  @click="handleDelete(scope.row.superviseId)"
                  v-hasPermi="['ffs:mortgage:supervision:delete']"
                >删除</el-button>

                <el-tooltip class="item" effect="dark" content="修改监管员" placement="top-start">
                  <el-button
                    size="mini"
                    type="text"
                    @click="supervisorEdit(scope.row)"
                    icon="el-icon-eleme"
                    v-hasPermi="['ffs:mortgage:supervision:edit']"
                    v-if="scope.row.superviseStatus == 2"
                  >监管员</el-button>
                </el-tooltip>

                <el-tooltip class="item" effect="dark" content="修改服务费" placement="top-start">
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-eleme"
                    @click="handleUpdateServiceFee(scope.row)"
                    v-if="scope.row.superviseStatus == 2"
                    v-hasPermi="['ffs:supervision:edit']"
                  >服务费</el-button>
                </el-tooltip>

                <el-tooltip class="item" effect="dark" content="修改紧急联系人" placement="top-start">
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-s-custom"
                    @click="contactsEdit(scope.row)"
                    v-hasPermi="['ffs:mortgage:supervision:edit']"
                    v-if="scope.row.superviseStatus == 2"
                  >联系人</el-button>
                </el-tooltip>

                <el-tooltip class="item" effect="dark" content="预警参数" placement="top-start">
                  <el-button
                    v-if="scope.row.superviseStatus != 3"
                    size="mini"
                    type="text"
                    icon="el-icon-s-custom"
                    @click="WarningEdit(scope.row)"
                    v-hasPermi="['ffs:supervision:edit']"
                  >预警参数</el-button>
                </el-tooltip>

                <el-tooltip class="item" effect="dark" content="上传视频" placement="top-start">
                  <el-button
                    v-if="scope.row.superviseStatus != 3"
                    size="mini"
                    type="text"
                    icon="el-icon-video-camera-solid"
                    @click="uploadVideoUrl(scope.row)"
                    v-hasPermi="['ffs:supervision:edit']"
                  >上传视频</el-button>
                </el-tooltip>

                    <el-button size="mini" type="text" v-show="scope.row.superviseStatus==3" icon="el-icon-eleme"
                               :disabled="(scope.row.deviceFlag==1)"
                               @click="deviceRemove(scope.row)" v-hasPermi="['ffs:supervision:mortgage:cleanDevice']">硬件拆除</el-button>
                  </el-col>
                    <el-button size="mini" type="text" v-show="scope.row.superviseStatus==3" icon="el-icon-eleme"
                               :disabled="(scope.row.superviseFeeFlag==1)"
                               @click="serviceFee(scope.row)" v-hasPermi="['ffs:supervision:mortgage:cleanSuperviseFee']">服务费结算</el-button>

                <div slot="reference" class="extend-btn btn_color_three">
                <!-- <el-button type="text">更多</el-button> -->
                <i class="el-icon-more"></i>
                </div>
            </el-popover>
            </template>
        </el-table-column>
        </el-table>
    </el-card>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <tablel-form
      :tablelFormData="tablelFormData"
      @close="close"
      v-if="tablelFormData.open"
      @refresh="refresh"
      @addSupervision="addSupervision"
    ></tablel-form>

    <add-form
      :addFormData="addFormData"
      @close="close"
      v-if="addFormData.open"
      @refresh="refresh"
      @closeAddBaseForm="closeAddBaseForm"
    />

    <details-form
      :detailsFormData="detailsFormData"
      @close="refresh"
      v-if="detailsFormData.open"
      @refresh="refresh"
    />

    <updata-supervisor
      :supervisorData="supervisorData"
      @close="close"
      v-if="supervisorData.open"
      @refresh="refresh"
    />

    <updata-contacts
      :contactsData="contactsData"
      @close="close"
      v-if="contactsData.open"
      @refresh="refresh"
    />

    <add-base-info
      :addBaseForm="addBaseForm"
      @closeAddBaseForm="closeAddBaseForm"
      v-if="addBaseForm.open"
      @refresh="refresh"
      @addSupervision="addSupervision"
    />

    <upload-supervision-file
      :uploadFileData="uploadFileData"
      @close="close"
      v-if="uploadFileData.open"
      @refresh="refresh"
    />

    <add-use-letter
      :addLetterData="addLetterData"
      @close="close"
      v-if="addLetterData.open"
      @refresh="refresh"
    />

    <useLetterl-log
      :useLetterData="useLetterData"
      @close="useLetterData.open=false"
      @adduseLetter="adduseLetter"
      v-if="useLetterData.open"
    />

    <update-service-fee
      :updateServiceFeeData="updateServiceFeeData"
      @close="close"
      v-if="updateServiceFeeData.open"
      @refresh="refresh"
    />
    <videoModel  :videoModel="videoModel" v-if="videoModel.open"  @close="close"></videoModel>
    <earlyWarning :earlyWarning="earlyWarning" v-if="earlyWarning.open" @close="close"       @refresh="refresh"></earlyWarning>
        <!-- 用户授权 -->
        <el-drawer
        title="用户授权"
        size="70%"
        :visible.sync="drawer.open"
        :destroy-on-close="true"
        >
     <selectUser :superviseIds="superviseIds"  @refurbish="refurbish" ></selectUser>
     </el-drawer>
     <!-- 延期 -->
     <postPone v-if="postPone.open" @close="close" :postPone="postPone" @refresh="refresh"></postPone>
        <!-- 视频维护 -->
        <offlineVideoUrlStr v-if="offLine.open" :offLine="offLine"  @close="close"  @refresh="refresh"></offlineVideoUrlStr>
        <!-- 监管单终止 -->
        <el-dialog
            class="delete"
            title="提示"
            :visible.sync="dialogVisible"
            width="450px"
            center="center"
            @close="handleClose">
            <div style="text-align: justify; line-height: 24px; margin-bottom: 20px;">{{ closeTitle }}</div>
            <el-form :model="ruleForm" ref="ruleForm" label-width="140px" :rules="rules" >
                <el-form-item label="终止原因" prop="closeReason">
                    <el-select v-model="ruleForm.closeReason" placeholder="请选择终止原因" style="width: 100%;" @change="closeCheck">
                        <el-option label="正常终止" value="1"></el-option>
                        <el-option label="异常终止（释放监管意向）" value="2"></el-option>
                        <el-option label="异常终止（不释放监管意向）" value="3"></el-option>

                    </el-select>
                </el-form-item>
              <el-form-item label="硬件是否已拆除" prop="deviceFlag">
                <el-radio-group v-model="ruleForm.deviceFlag">
                  <el-radio :label="1">是</el-radio>
                  <el-radio :label="0">否</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="服务费是否已结清" prop="superviseFeeFlag">
                <el-radio-group v-model="ruleForm.superviseFeeFlag">
                  <el-radio :label="1">是</el-radio>
                  <el-radio :label="0">否</el-radio>
                </el-radio-group>
              </el-form-item>
                <el-form-item label="备注" prop="closeDesc">
                    <el-input type="textarea" v-model="ruleForm.closeDesc" placeholder="请输入备注"   maxlength="200" show-word-limit></el-input>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="submitForm">确 定</el-button>
            </span>
        </el-dialog>

    </div>

</template>

<script>
import {
    superviseList,
    superviseInfo,
    inspectionDelete,
    superviseClose,
    superviseService,
    exportSuperviseList
} from "@/api/ffs/farmingSupervisionSheet/livingSupervisionApi";
import selectUser from '@/views/ffs/supervisionSheet/livingSupervision/components/selectUser.vue'
import tablelForm from "./components/tablelForm";
import addForm from "./components/addForm/index.vue";
import detailsForm from "./components/detailsForm/index.vue";
import updataSupervisor from "@/views/ffs/supervisionSheet/livingSupervision/components/updataSupervisor.vue";
import updataContacts from "@/views/ffs/supervisionSheet/livingSupervision/components/updataContacts.vue";
import addBaseInfo from "@/views/ffs/supervisionSheet/livingSupervision/components/addBaseInfo.vue";
import uploadSupervisionFile from "./components/uploadSupervisionFile.vue";
import addUseLetter from "@/views/ffs/farmingMortgage/supervision/components/addUseLetter.vue"; //追加用信金额
import useLetterlLog from "@/views/ffs/farmingMortgage/supervision/components/useLetterlLog.vue"; //追加用信金额记录列表
import updateServiceFee from "@/views/ffs/supervisionSheet/livingSupervision/components/updateServiceFee.vue";
import {
    settlementWay,
    superviseStatus,
    serviceFeeType
} from "@/views/ffs/supervisionSheet/livingSupervision/utils/formatVal.js";
import videoModel from '@/views/ffs/supervisionSheet/livingSupervision/components/videoModel.vue'
import { checkPermi } from "@/utils/permission.js";
import earlyWarning from "@/views/ffs/supervisionSheet/livingSupervision/components/earlyWarning.vue"
import offlineVideoUrlStr from "./components/offlineVideoUrlStr.vue"
import postPone from '@/views/ffs/supervisionSheet/livingSupervision/components/postPone/index.vue'
import {exportExcel} from "@/utils/east"
import { tableUi } from "@/utils/mixin/tableUi.js";
import { superviseEdit } from "@/api/ffs/farmingSupervisionSheet/livingSupervisionApi";
export default {
    mixins: [tableUi],
    name: "livingSupervisionIndex",
    components: {
        tablelForm,
        addForm,
        detailsForm,
        updataSupervisor,
        updataContacts,
        addBaseInfo,
        uploadSupervisionFile,
        addUseLetter,
        useLetterlLog,
        updateServiceFee,
        videoModel,
        earlyWarning,
        selectUser,
        offlineVideoUrlStr,
        postPone
    },
    data() {
        return {
            postPone:{
                open:false
            },
            toggleSearchStatus:false,
            offLine: {
                open: false
            },
        drawer:{
        open:false
      },
      tablelFormData: {
        open: false,
        info: "",
        title: "",
      },
      videoModel:{
        open:false,
        offlineVideoUrl:[],
        onlineVideoUrl:[]
      },
      earlyWarning:{
        open:false,
        obj:{}
      },
      useLetterData: {
        open: false,
        title: "用信记录",
      },
      addLetterData: {
        open: false,
        title: "追加用信金额",
        info: {},
      },
      contactsData: {
        open: false,
        editInfo: {},
      },
      uploadFileData: {
        open: false,
        editInfo: {},
      },

      supervisorData: {
        open: false,
        editInfo: {},
      },
      addFormData: {
        open: false,
        id: "",
      },

      updateServiceFeeData: {
        open: false,
        info: "",
        title: "修改监管单服务费",
      },
      addBaseForm: {
        open: false,
        id: "",
      },
      detailsFormData: {
        open: false,
        title: "",
        superviseId: "",
        applyId: ''
      },
      dialogVisible:false,
      closeTitle:'',
      ruleForm:{
        closeReason:'',
        closeDesc:'',
        deviceFlag: null,
        superviseFeeFlag: null
      },
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 表格数据
      list: [],
      columnDom:false,
      superviseIds:[],
      // 查询参数
      queryParams: {
        operateType:'',
        pageNum: 1,
        pageSize: 20,
        queryForm: "",
        superviseId: "",
        applyPhone: "",
        superviseStatus: "",
        bankName: "",
        contractNo: "",
        loanOfficerName: "",
        loanOfficerPhone: "",
        supervisorName: "",
        supervisorPhone: "",
        pastorName: "",
         serviceFeeType:"",
        superviseType: "5", //1活体，2仓单
        fromAppFlag:'1',
        applyName:'',
        areaDataRange:'',
        supervisePeriod : "",
        deviceFlag: '',
        superviseFeeFlag: ''
      },
      superviseTime: [],
      supervisePeriodList: [],
      dateRange: "",
      rules:{
        closeReason: [
            { required: true, message: '请选择终止原因', trigger: ['change','blur'] }
          ],
          closeDesc: [
            { required: false, message: '请输入备注', trigger: ['change','blur'] }
          ],
        deviceFlag:[{ required: true, message: '请选择硬件是否已拆除', trigger: ['change', 'blur']}],
        superviseFeeFlag: [{ required: true, message: '请选择服务费是否已结清', trigger: ['change', 'blur']}]
      }
    };
  },
  async created() {
    this.columnDom=this.handelColumn()
    if(this.$route.query?.sourceType==1) {
      this.queryParams.superviseStatus='3'
      this.queryParams.deviceFlag = 0
    } else if(this.$route.query?.sourceType==2) {
      this.queryParams.superviseStatus='3'
      this.queryParams.superviseFeeFlag = 0
    }
    this.getList();
  },
  computed: {
        //处理按钮颜色
        handelBtn(){
        return (row)=>{
            if(row.offlineVideoUrl.length>0||row.onlineVideoUrl.length>0){
                return true
            }else{
                return false
            }
        }
    },
        //处理状态颜色
     handelColor(){
        return (value)=>{
            if(value==1){
                return '#5672FA'
            }if(value==2){
                return '#12AE63'
            }if(value==3){
                return '#9DA1A8'
            }
        }
    },
    handelText(){
        return (row)=>{
            let text=''
            if(row.expireFlag==1&&row.exceedFlag!=1){
                text=`请注意，${row.expireDay}天后到期！`
            }
            if(row.exceedFlag==1&&row.expireFlag!=1){
                text=`请注意，已超出监管时间${row.exceedDay}天`
            }
            return text
        }
    },
    haveMenu() {
      return (permissionArr, statusArr, superviseStatus) => {
        const permission = checkPermi(permissionArr);
        if (!permission) {
          return permission;
        }
        return statusArr.find((item) => item == superviseStatus);
      };
    },
    settlementWayName() {
      return (row, com, val) => {
        return settlementWay(val);
      };
    },
     serviceFeeTypeVal() {
      return (row, com, val) => {
        return serviceFeeType(val);
      };
    },
    superviseStatusName() {
      return ( val) => {
        return superviseStatus(val);
      };
    },

    superviseDate() {
      return (row, com, val) => {
        if (!row.superviseStart) return "-";
        return row.superviseStart + "至" + row.superviseEnd;
      };
    },

    superviseServiceFeeToast() {
      return (row, com, val) => {
        return row.superviseAmount * row.superviseServiceRate * 100;
      };
    },
  },
  methods: {
    sortBy(a,b,key){
        let at=a[key]
        let bt=b[key]
        if(key=='createTime'){
            return  at-bt
        }else{
            return parseFloat(at) - parseFloat(bt)
        }
    },
        // 延期
    putOff(row){
        this.postPone.open=true
        this.postPone.sourceType = 2
        this.postPone.superviseId=row.superviseId
        this.postPone.superviseType="5"
        this.postPone.info=row
    },
    editSuperviseFile(params){
      console.log(params)
      superviseEdit(params).then(res => {
        console.log(res)
        if(res.code ==200) {
          this.$message({
            type: 'success',
            message: '操作成功!'
          });
          this.getList();
        }
      })
    },
    // 硬件拆除
    deviceRemove(row){
      console.log(row)
      this.$confirm('请确认所有硬件均已拆除完毕', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let params = {
          deviceFlag: 1,
          superviseId: row.superviseId
        }
        console.log(params)
        this.editSuperviseFile(params)
        this.getList();
      }).catch(() => {});
    },
    // 服务费结算
    serviceFee(row) {
      console.log(row)
      this.$confirm('请确认服务费已结清', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let params = {
          superviseFeeFlag: 1,
          superviseId: row.superviseId
        }
        console.log(params)
        this.editSuperviseFile(params)
        this.getList();
      }).catch(() => {});
    },
    handelColumn(){
     let  permissions = this.$store.getters && this.$store.getters.permissions
     return  permissions.some(item=>{
        if(item==='ffs:supervision:empower'){
             return true
        }else{
            return false
        }
     })
    },
        //导出
    exportList(){
        let obj=this.queryParams
        const filename = "仓单质押监管信息";
        exportExcel(exportSuperviseList,obj,filename)
    },
    //上传视频
    uploadVideoUrl(row){
        this.offLine.open=true
        this.offLine.obj={
            url:row.offlineVideoUrlStr,
            superviseId:row.superviseId
        }
    },
     //选择
     handleSelectionChange(list){
        this.superviseIds=list.map(item=>item.superviseId)
    },
    batchAddUser(){
        if(this.superviseIds.length<=0){
            this.$message.warning('请先选择监管单！')
            return
        }
        this.drawer.open=true
    },
    refurbish(){
        this.$refs.multipleTable.clearSelection();
        this.getList()
        this.superviseIds=[]
        this.drawer.open=false
    },
    //修改权限
    // purviewEdit(id){
    //     this.superviseIds=[]
    //     this.superviseIds.push(id)
    //     this.drawer.open=true
    // },
        //预警维护
        WarningEdit(row){
        this.earlyWarning.open=true
        let earlySendUser=[]
        let earlySendUserStr=[]
        if(row.earlySendUser){
            earlySendUser=row.earlySendUser.split(',')
        }
        if(row.earlySendUserStr){
            earlySendUserStr=JSON.parse(row.earlySendUserStr)
        }
        this.earlyWarning.obj={
            earlySendUser:earlySendUser,
            earlySendUserStr:earlySendUserStr,
            superviseId:row.superviseId,
            earlyFlag:row.earlyFlag,
            warningValue: row.warningValue,
        }
    },
    //刷新页面
    refresh() {
      this.detailsFormData.open = false;
      this.getList();
    },

    /** 查询列表 */
    getList() {
      this.loading = true;
      superviseList(this.queryParams).then((res) => {
        this.loading = false;
        if (res.code == 200) {
          this.list = res.result.list || [];
          this.total = Number(res.result.total || 0);
        } else {
          this.$message.error(res.message);
        }
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      if (this.dateRange) {
        this.queryParams.createStartTime = this.dateRange[0] + " 00:00:00";
        this.queryParams.createEndTime = this.dateRange[1] + " 23:59:59";
      } else {
        this.queryParams.createStartTime = "";
        this.queryParams.createEndTime = "";
      }
      if(this.superviseTime && this.superviseTime.length != 0) {
        this.queryParams.superviseStart = this.superviseTime[0] + " 00:00:00";
        this.queryParams.superviseEnd = this.superviseTime[1] + " 23:59:59";
      } else {
        this.queryParams.superviseStart = ''
        this.queryParams.superviseEnd = ''
      }

      if(this.supervisePeriodList?.length != 0) {
        this.queryParams.supervisePeriod = this.supervisePeriodList.join(',')
      } else {
        this.queryParams.supervisePeriod = ''
      }

      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.startTime = "";
      this.queryParams.endTime = "";
      this.dateRange = "";
      this.superviseTime = []
      this.supervisePeriodList = []
      this.queryParams.deviceFlag = ''
      this.queryParams.superviseFeeFlag = ''
      this.queryParams.superviseStatus=''
      this.handleQuery();
    },
    supervisorVideo(offlineVideoUrl,onlineVideoUrl){
        this.videoModel.open=true
        this.videoModel.offlineVideoUrl=offlineVideoUrl||[]
        this.videoModel.onlineVideoUrl=onlineVideoUrl||[]
    },
    //关闭弹框
    close() {
      this.tablelFormData.open = false;
      this.addFormData.open = false;
      this.detailsFormData.open = false;
      this.supervisorData.open = false;
      this.contactsData.open = false;
      this.uploadFileData.open = false;
      this.addLetterData.open = false;
       this.updateServiceFeeData.open = false;
       this.videoModel.open=false
       this.earlyWarning.open=false
       this.offLine.open=false
       this.postPone.open=false
    },

    //关闭弹框
    closeAddBaseForm() {
      this.addBaseForm.open = false;
    },

    /** 创建监管单 */
    addSupervision(data) {
      this.addFormData.info = data;
      this.tablelFormData.open = false;
      this.addFormData.open = true;
      this.addFormData.title = "创建仓单监管单";
    },

    //打开选择新增活体监管单
    insertData() {
      this.tablelFormData.open = true;
      this.tablelFormData.title = "选择意向单";
    },

    /** 无意向单创建监管单 */
    addSupervisionNoIntention() {
      this.addBaseForm.open = true;
      this.addBaseForm.superviseType = 5; //监管类型：1活体，2仓单
      this.addBaseForm.title = "填写调研信息";
      this.addBaseForm.btnTxt = "创建仓单监管单";
    },

    //详情
    handDetails(superviseId, applyId) {
      console.log(applyId)
      superviseInfo({ ids: [superviseId] }).then((res) => {
        if (res.code == 200) {
          this.detailsFormData.info = res.result || {};
          this.detailsFormData.superviseId = superviseId;
          this.detailsFormData.applyId = applyId
          this.detailsFormData.title = "仓单监管单详情";
          this.detailsFormData.open = true;
        }
      });
    },

    adduseLetter(rowData) {
      this.addLetterData.open = true;
      this.addLetterData.info = rowData;
    },

    //编辑
    handleEdit(rowData) {
      superviseInfo({ ids: [rowData.superviseId] }).then((res) => {
        if (res.code == 200) {
          const result = res.result;
          result.payerCode = result.payerCode?.toString();
          result.operation = "edit";
          this.addFormData.info = result;
          this.addFormData.open = true;
          this.addFormData.title = "编辑";
        }
      });
    },

    //编辑查看文件
    editFiles(row) {
      const fileList = {
        intentionListId: row.intentionListId,
        superviseId: row.superviseId,
        fileIntentionList: row.fileIntentionList || "", //意向表附件
        fileCreditInvestigation: row.fileCreditInvestigation || "", //调研表附件
        regulatoryScheme: row.regulatoryScheme || "", //监管方案附件
        fileTripleAgreement: row.fileTripleAgreement || "", //三方协议文件
        fileOther: row.fileOther || "", //其他文件
        fileLoan: row.fileLoan || "", //贷款单文件
        fileLeaseContract: row.fileLeaseContract || "", //租赁合同文件
      };
      this.uploadFileData.superviseStatus = row.superviseStatus;
      this.uploadFileData.fileList = fileList;
      this.uploadFileData.title = "仓单监管单附件";
      this.uploadFileData.open = true;

    },

    // 修改监管员
    supervisorEdit(rowData) {
      this.supervisorData.title = "修改监管员";
      const info = {
        bankId: rowData.bankId,
        superviseId: rowData.superviseId,
        loanOfficerId: rowData.loanOfficerId,
        loanOfficerName: rowData.loanOfficerName,
        loanOfficerPhone: rowData.loanOfficerPhone,
        pastorId: rowData.pastorId,
        pastorName: rowData.pastorName,
        pastorPhone: rowData.pastorPhone,
        supervisorId: rowData.supervisorId,
        supervisorName: rowData.supervisorName,
        supervisorPhone: rowData.supervisorPhone,
      };
      this.supervisorData.editInfo = info;
      this.supervisorData.open = true;
    },
     handleUpdateServiceFee(rowData) {
      this.updateServiceFeeData.info = rowData;
      this.updateServiceFeeData.open = true;
      this.updateServiceFeeData.title = "修改仓单监管单服务费";
    },
    // 修改紧急联系人
    contactsEdit(rowData) {
      this.contactsData.title = "修改紧急联系人";
      const info = {
        superviseId: rowData.superviseId,
        subjectLinkmanName: rowData.subjectLinkmanName || "",
        subjectLinkmanPhone: rowData.subjectLinkmanPhone || "",
        bankLinkmanName: rowData.bankLinkmanName || "",
        bankLinkmanPhone: rowData.bankLinkmanPhone || "",
        superviseLinkmanName: rowData.superviseLinkmanName || "",
        superviseLinkmanPhone: rowData.superviseLinkmanPhone || "",
      };
      this.contactsData.editInfo = info;
      this.contactsData.open = true;
    },


    closeCheck(){
        if(this.ruleForm.closeReason!=1){
            this.rules.closeDesc=[   { required: true, message: '请输入备注', trigger: ['change','blur'] }]
        }else{
            this.rules.closeDesc=[   { required: false, message: '请输入备注', trigger: ['change','blur'] }]
        }
    },
    handleClose() {
        this.$refs['ruleForm'].resetFields();
        this.dialogVisible=false
    },

    submitForm() {
      console.log(this.ruleForm)
        this.$refs['ruleForm'].validate((valid) => {
          if (valid) {
            superviseClose(this.ruleForm).then(res=>{
                if(res.code==200){
                    this.refresh();
                    this.$message.success('操作成功')
                    this.handleClose()
                }
            })

          }
        });
      },
    //监管单的关闭
    settleFeed(row) {
      let title ="当前监管单已完成监管。关闭后所监管的商品禁止出入库，监管单所有数据操作将会被禁止，确定要终止吗？";
      let title1=''
      let date = new Date();
      let nowTime=  date.getFullYear() + "-" + (date.getMonth() + 1) + "-" + (date.getDate()+1)
      let nowDate = new Date(nowTime).getTime();
      let endDate = new Date(row.superviseEnd).getTime();
      if (nowDate < endDate) {
        title='关闭后所监管的商品禁止出入库，监管单所有数据操作将会被禁止，确定要终止吗？'
       let obj=this.getTimeDiff(row.superviseStart,nowTime) //实际监管时间
       let obj1=this.getTimeDiff(row.superviseStart,row.superviseEnd)  //总共监管时间
       title1=`当前监管单未到达监管结束时间，预计监管${obj1.month>0?obj1.year*12+obj1.month+'个月':''}${obj1.day>0?obj1.day+'天':''}，
       实际监管${obj.year>0?obj.day+'年':''}${obj.month>0?obj.month+'个月':''}${obj.day>0?obj.day+'天':''}，
       监管服务费按${obj.month>6?obj.year+1:(obj.year>0?obj.year+"年":'')}${obj.month>6?'':((obj.month!=0||obj.day!=0)?'6个月':'')}收取。`
      }
      this.closeTitle=title1+title
      this.dialogVisible=true
      this.closeCheck()
      this.ruleForm.superviseId=row.superviseId

    },
    //计算两时间的差值
    getTimeDiff(value1,value2 ){
        let flag = [1, 3, 5, 7, 8, 10, 12, 4, 6, 9, 11, 2];
        let start =new Date(value1)
        let end =new Date(value2)
        let year = end.getFullYear() - start.getFullYear();
        let month = end.getMonth() - start.getMonth();
        let day = end.getDate() - start.getDate();
        console.log(end.getDate(),'开始',start.getDate(),'结束',day);
        if (month < 0) {
            year--;
            month = end.getMonth() + (12 - start.getMonth());
        }
        if (day < 0) {
            month--;
            let index = flag.findIndex((temp) => {
                return temp === start.getMonth() + 1
            });
            let monthLength;
            if (index <= 6) {
                monthLength = 31;
            } else if (index > 6 && index <= 10) {
                monthLength = 30;
            } else {
                monthLength = 28;
            }
            day = end.getDate() + (monthLength - start.getDate());
        }
        return {year:year,month:month,day:day}
    },
    useLetterDig(rowData) {
      this.useLetterData.rowData = rowData;
      this.useLetterData.open = true;
      this.useLetterData.type = "list";
    },

    //删除
    handleDelete(superviseId) {
      this.$confirm("确定删除吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          inspectionDelete({ ids: [superviseId] }).then((res) => {
            if (res.code == 200) {
              this.refresh();
              this.$message({
                type: "success",
                message: "删除成功!",
              });
            }
          });
        })
        .catch(() => {});
    },
  },
};
</script>
<style scoped lang="scss">
.delete{
    display: flex;
    justify-content: center;
    align-items: center;
}
.extend-btn {
  color: #1890ff;
  display: inline-block;
  .el-icon-arrow-down {
    margin-left: -10px;
  }
}
.el-button {
  margin-right: 10px;
  margin-left: 0;
}
.delayOperateType{
&-img{
    display: inline-block;
    width: 26px;
    height: 16px;
    margin-right: 5px;
    vertical-align: text-top;

}
}
</style>
<style>
.my-popover {
  width: 212px !important;
  .el-button{
    margin-left: 10px !important;
  }
}
</style>

<template>
  <div>
    <el-dialog
      :title="addFormData.title"
      :visible.sync="addFormData.open"
      width="1200px"
      :close-on-click-modal="false"
      @close="close"
      class="fieldList"
    >
      <div class="head-title">
        <el-steps :active="stepsActive" finish-status="success" align-center>
          <el-step title="选择监管关系"></el-step>
          <el-step title="监管信息上传"></el-step>
          <el-step title="监管信息设置"></el-step>
        </el-steps>
      </div>

      <span slot="footer" class="dialog-footer" v-show="!addFormData.disable">
        <el-button @click="close">取消</el-button>
        <el-button type="primary" @click="submitForm">提交</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getRecordList, deleteDataSource } from "@/api/ffs/reportDataApi";
import { insertBankReport, getTemplateList } from "@/api/ffs/reportDataApi";
export default {
  name: "livingSupervaddForm",
  props: {
    addFormData: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      form: {
        fileName: "",
        datetime: "",
      },
      stepsActive: 0,
      rules: {
        datetime: [
          { required: true, message: "报表日期不能为空", trigger: "blur" },
        ],
        fileName: [
          { required: true, message: "报表日期不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {},
  methods: {
    close() {
      this.$emit("close");
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["ruleForm"].validate((valid) => {
        if (valid) {
          this.$modal.loading("正在计算中，数据量较大，请耐心等待...");
          this.form.datetime = this.form.datetime + "-01";
          insertBankReport(this.form)
            .then((response) => {
              this.$modal.closeLoading();
              if (response.code == 200) {
                this.$modal.msgSuccess("报表生成成功，请在列表中点击下载。");
                this.open = false;
                this.$emit("refresh");
                this.$emit("close");
              }
            })
            .catch(() => {
              this.$modal.closeLoading();
            });
        }
      });
    },
  },
};
</script>

<style lang="scss">
.fieldList {
  .el-dialog__header {
    background-color: #f4f4f4;
  }

  .el-dialog__footer {
    text-align: center;
  }

  .selectWidth {
    width: 100%;
  }
}
</style>

<template>
  <div>
    <el-row :gutter="10" class="mb8">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="100px">
        <el-row class="form_row">
         <el-col class="form_col">
        <el-form-item label="日志状态：" prop="status">
          <el-select v-model="queryParams.status" clearable class="selectWidth">
            <el-option label="正常" value="0" />
            <el-option label="异常" value="1" />
          </el-select>
        </el-form-item>

        <el-form-item label="提交人：" prop="createUserName">
          <el-input v-model="queryParams.createUserName" placeholder="请输入提交人" clearable @keyup.enter.native="handleQuery"/>
        </el-form-item>

        <el-form-item label="提交时间：">
          <el-date-picker
          style="width: 215px"
            v-model="time"
            value-format="yyyy-MM-dd hh:mm:ss"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
        </el-form-item>
        </el-col>
        </el-row>
        <el-row style="margin-left: 100px;">
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
        </el-row>
      </el-form>
    </el-row>

    <el-table :data="list" stripe style="width: 100%;margin-top:10px" v-loading="loading" border>
      <el-table-column type="index" label="序号" width="50"></el-table-column>
      <el-table-column prop="status" label="状态" :formatter="statusName" width="100"></el-table-column>
      <el-table-column prop="createUserName" label="提交人" width="120"></el-table-column>
      <el-table-column prop="createTime" label="提交时间" width="180"></el-table-column>
      <el-table-column prop="remark" label="备注"></el-table-column>
      <el-table-column label="操作" align="center" width="100">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-info"
            @click="handDetails(scope.row.logId)"
          >详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <superviseLogInfo
      :superviseLogData="superviseLogData"
      @close="close"
      v-if="superviseLogData.open"
    />
  </div>
</template>

<script>
import { superviseLogPage } from "@/api/ffs/farmingMortgage/supervision";
import superviseLogInfo from "./superviseLogInfo.vue";
export default {
  name: "detailsGoodsInfo",
  components: {
    superviseLogInfo,
  },
  props: {
    info: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      superviseLogData: {
        open: false,
        id: "",
        title: "",
      },
      queryParams: {
        status: "",
        createUserName: "",
        superviseStart: "",
        superviseEnd: "",
        pageNum: 1,
        pageSize: 10,
      },
      form: {},

      time: undefined,

      // 遮罩层
      loading: false,
      // 总条数
      total: 0,
      // 表格数据
      list: [],
      livestockCategoryList: [],
      livestockVarietiesList: [],
      livestock: [],
    };
  },
  created() {
    this.queryParams.superviseId = this.info.superviseId;
    this.form =  this.info;
    this.getList();
  },

  computed: {
    statusName() {
      return (row, com, val) => {
        if (val == 0) {
          return "正常";
        } else {
          return "异常";
        }
      };
    },
  },
  methods: {
    /** 查询列表 监管日志列表 */
    getList() {
      superviseLogPage(this.queryParams).then((res) => {
        this.loading = false;
        if (res.code == 200) {
          this.list = res.result.list || [];
          this.total = Number(res.result.total || 0);
        } else {
          this.$message.error(res.message);
        }
      });
    },

    //详情
    handDetails(logId) {
      this.superviseLogData.logId = logId;
      this.superviseLogData.title = "监管日志详情";
      this.superviseLogData.open = true;
    },

    //打开出入库详情
    openOutEnterHoseDig(superviseId, eq) {
      let title = "出库记录";
      if (eq == 1) {
        title = "入库记录";
      }
      this.superviseLogData.superviseId = superviseId;
      this.superviseLogData.title = title;
      this.superviseLogData.open = true;
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.pageNum = 1;

      if (this.time) {
        this.queryParams.superviseStart = this.time[0];
        this.queryParams.superviseEnd = this.time[1];
      } else {
        this.queryParams.superviseStart = "";
        this.queryParams.superviseEnd = "";
      }

      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
        this.time=undefined
      this.resetForm("queryForm");
      this.queryParams.startTime = "";
      this.queryParams.endTime = "";

      this.handleQuery();
    },

    //关闭弹框
    close() {
      this.superviseLogData.open = false;
      this.$emit("close");
    },
  },
};
</script>

<style lang="scss">
.fieldList {
  .el-dialog__header {
    background-color: #f4f4f4;
  }

  .el-dialog__footer {
    text-align: center;
  }

  .selectWidth {
    width: 100%;
  }
}
</style>

<template>
  <div>
    <el-dialog
      :title="outEnterHoseData.title"
      :visible.sync="outEnterHoseData.open"
      width="1200px"
      :close-on-click-modal="false"
      @close="close"
      append-to-body
      class="fieldList"
    >
      <el-descriptions :column="3" border>
        <el-descriptions-item label="质押品类型">{{outEnterHoseData.info.mortgageTypeName}}</el-descriptions-item>
        <el-descriptions-item label="商品品类">{{outEnterHoseData.info.commodityCategoryName}}</el-descriptions-item>
        <el-descriptions-item label="加工方式">{{outEnterHoseData.info.processWayName}}</el-descriptions-item>
        <el-descriptions-item label="商品类型">{{outEnterHoseData.info.commodityTypeName}}</el-descriptions-item>
        <el-descriptions-item label="商品编码">{{outEnterHoseData.info.commodityNo}}</el-descriptions-item>
        <el-descriptions-item label="商品名称">{{outEnterHoseData.info.commodityName}}</el-descriptions-item>
        <el-descriptions-item label="计量单位">{{outEnterHoseData.info.commodityUnitName}}</el-descriptions-item>
        <el-descriptions-item label="商品规格">{{getCommoditySpecificationsName(outEnterHoseData.info)}}</el-descriptions-item>
        <el-descriptions-item label="质押数量">{{outEnterHoseData.info.mortgageNum}}</el-descriptions-item>
        <el-descriptions-item label="质押重量（千克）">{{outEnterHoseData.info.mortgageWeight}}</el-descriptions-item>
        <el-descriptions-item label="质押货值（元）">{{outEnterHoseData.info.mortgageTotalPrice}}</el-descriptions-item>
      </el-descriptions>

      <div class="cttt">
        <span class="xttname">{{motionTypeName}}明细</span>
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
          <el-form-item label="货区名称：" prop="positionName">
            <el-input v-model="queryParams.positionName" placeholder="请输入货区名称" clearable />
          </el-form-item>

          <el-form-item :label="motionTypeName+'时间：'">
            <el-date-picker
              v-model="dateRange"
              style="width: 240px"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            ></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <el-table :data="tableData" border show-summary v-loading="loading">
        <el-table-column label="序号" type="index" fixed width="50"></el-table-column>
        <el-table-column prop="positionName" label="货区"></el-table-column>
        <el-table-column prop="num" sortable :label="motionTypeName+'数量'"></el-table-column>
        <el-table-column prop="totalWeight" sortable :label="motionTypeName+'重量（千克）'"></el-table-column>
        <el-table-column prop="totalPrice" sortable :label="motionTypeName+'货值（元）'"></el-table-column>
        <el-table-column prop="createName" label="操作人"></el-table-column>
        <el-table-column prop="createTime" :label="motionTypeName+'时间'"></el-table-column>
      </el-table>

      <span slot="footer" class="dialog-footer">
        <el-button type="info" @click="close">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { superviseDetailPage } from "@/api/ffs/farmingMortgage/supervision";
import {commoditySpecificationsName} from "./utils.js"
export default {
  name: "outEnterDetails",
  props: {
    outEnterHoseData: {
      type: Object,
      default: () => ({
        title: "",
        open: false,
        info: {},
      }),
    },
  },
  data() {
    return {
      loading: false,
      queryParams: {
        motionType: "", //motionType   1 入库 2 出库
        positionName: "", //货区名称
        commodityId: "",
        startTime: "",
        endTime: "",
      },
      dateRange: [],

      // 遮罩层
      loading: true,
      // 总条数
      total: 3,
      // 表格数据
      tableData: [],
    };
  },
  created() {
    this.queryParams.superviseId = this.outEnterHoseData.info.superviseId;
    this.queryParams.motionType = this.outEnterHoseData.motionType;
    this.queryParams.commodityId = this.outEnterHoseData.info.commodityId;
    this.getList();
  },
  computed: {
    motionTypeName() {
      return this.outEnterHoseData.motionType == 1 ? "入库" : "出库";
    },

    getCommoditySpecificationsName() {
      return (rows) => {
        return commoditySpecificationsName(rows)
      };
    },
  },
  methods: {
    //列表查询
    getList() {
      superviseDetailPage(this.queryParams).then((res) => {
        if (res.code == 200) {
          this.tableData = res.result.list;
          this.total = Number(res.result.total);
          this.loading = false;
        }
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.pageNum = 1;
      if (this.dateRange.length != 0) {
        this.queryParams.startTime = this.dateRange[0] + " 00:00:00";
        this.queryParams.endTime = this.dateRange[1] + " 23:59:59";
      } else {
        this.queryParams.startTime = "";
        this.queryParams.endTime = "";
      }
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.queryParams.startTime = "";
      this.queryParams.endTime = "";
      this.handleQuery();
    },
    close() {
      this.$emit("close");
    },
  },
};
</script>

<style lang="scss">
.cttt {
  display: flex;
  padding-top: 50px;
}
.xttname {
  margin-right: 30px;
  font-size: 16px;
  color: #333;
  margin-top: 5px;
  font-weight: bold;
}
</style>

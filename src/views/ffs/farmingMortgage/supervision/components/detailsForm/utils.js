const  commoditySpecificationsName= function(rows) {
    let num = 0; //基础单位是kg  改成和commodityUnitName单位匹配的commoditySpecifications数量
    if (rows.commodityUnitName == "克") {
      num = rows.commoditySpecifications *1000;
    }
    if (rows.commodityUnitName.includes("千克")) {
      num = rows.commoditySpecifications;
    }
    if (rows.commodityUnitName.includes("吨")) {
      num = rows.commoditySpecifications /1000;
    }
    return num + rows.commodityUnitName + "/" + rows.inventoryUnitName

};

export {
  commoditySpecificationsName
}
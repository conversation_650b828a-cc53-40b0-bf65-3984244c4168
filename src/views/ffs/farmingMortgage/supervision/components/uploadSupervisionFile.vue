<template>
  <div>
    <el-dialog
      :title="uploadFileData.title"
      :visible.sync="uploadFileData.open"
      :close-on-click-modal="false"
      @close="close"
      width="80%"
      class="fieldList filebox"
    >
      <div class="fileconter">
        <el-form :model="form" status-icon :rules="rules" ref="ruleForm" label-position="top">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="贷款单：" prop="fileLoan">
                <file-upload
                  :fileType="fileType"
                  :fileSize="100"
                  :limit="1"
                  v-model="form.fileLoan"
                  :isShowTip="false"
                  expandName="贷款单"
                  :delBtn="uploadFileData.superviseStatus!=3"
                  :disabled="uploadFileData.superviseStatus==3"
                >
                  <el-button type="primary" plain size="mini">
                    <div class="loadname">上传贷款单</div>
                    <i class="el-icon-upload loadicon"></i>
                  </el-button>
                </file-upload>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="租赁合同：" prop="fileLeaseContract">
                <file-upload
                  :fileType="fileType"
                  :fileSize="100"
                  :limit="1"
                  v-model="form.fileLeaseContract"
                  :isShowTip="false"
                  expandName="租赁合同"
                  :delBtn="uploadFileData.superviseStatus!=3"
                  :disabled="uploadFileData.superviseStatus==3"
                >
                  <el-button type="primary" plain size="mini">
                    <div class="loadname">上传租赁合同</div>
                    <i class="el-icon-upload loadicon"></i>
                  </el-button>
                </file-upload>
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item label="监管方案：" prop="regulatoryScheme">
                <file-upload
                  :fileType="fileType"
                  :fileSize="100"
                  :limit="1"
                  v-model="form.regulatoryScheme"
                  :isShowTip="false"
                  expandName="监管方案"
                  :delBtn="uploadFileData.superviseStatus!=3"
                  :disabled="uploadFileData.superviseStatus==3"
                >
                  <el-button type="primary" plain size="mini">
                    <div class="loadname">上传监管方案</div>
                    <i class="el-icon-upload loadicon"></i>
                  </el-button>
                </file-upload>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="三方协议：" prop="fileTripleAgreement">
                <file-upload
                  :fileType="fileType"
                  :fileSize="100"
                  :limit="1"
                  v-model="form.fileTripleAgreement"
                  :isShowTip="false"
                  expandName="三方协议"
                  :delBtn="uploadFileData.superviseStatus!=3"
                  :disabled="uploadFileData.superviseStatus==3"
                >
                  <el-button type="primary" plain size="mini">
                    <div class="loadname">上传三方协议</div>
                    <i class="el-icon-upload loadicon"></i>
                  </el-button>
                </file-upload>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="其它：" prop="fileOther">
                <file-upload
                  :fileType="fileType"
                  :fileSize="100"
                  :limit="1"
                  v-model="form.fileOther"
                  :isShowTip="false"
                  expandName="其它附件"
                  :delBtn="uploadFileData.superviseStatus!=3"
                  :disabled="uploadFileData.superviseStatus==3"
                >
                  <el-button type="primary" plain size="mini">
                    <div class="loadname">上传其它文件</div>
                    <i class="el-icon-upload loadicon"></i>
                  </el-button>
                </file-upload>
              </el-form-item>
            </el-col>

            <el-col :span="6" v-if="!uploadFileData.fileList.intentionListId">
              <el-form-item label="意向单：" prop="fileIntentionList">
                <file-upload
                  :fileType="fileType"
                  :fileSize="100"
                  :limit="1"
                  v-model="form.fileIntentionList"
                  :isShowTip="false"
                  expandName="意向单"
                  :delBtn="uploadFileData.superviseStatus!=3"
                  :disabled="uploadFileData.superviseStatus==3"
                >
                  <el-button type="primary" plain size="mini">
                    <div class="loadname">上传意向单</div>
                    <i class="el-icon-upload loadicon"></i>
                  </el-button>
                </file-upload>
              </el-form-item>
            </el-col>

            <el-col :span="6" v-if="!uploadFileData.fileList.intentionListId">
              <el-form-item label="调研表：" prop="fileCreditInvestigation">
                <file-upload
                  :fileType="fileType"
                  :fileSize="100"
                  :limit="1"
                  v-model="form.fileCreditInvestigation"
                  :isShowTip="false"
                  expandName="调研表"
                  :delBtn="uploadFileData.superviseStatus!=3"
                  :disabled="uploadFileData.superviseStatus==3"
                >
                  <el-button type="primary" plain size="mini">
                    <div class="loadname">上传调研表</div>
                    <i class="el-icon-upload loadicon"></i>
                  </el-button>
                </file-upload>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <span class="qiangti">所有大小不超过100MB 格式为 doc/pdf/png/jpg的文件</span>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="close">取消</el-button>
        <el-button
          type="primary"
          @click="submitForm"
          uploadFileData.open
          v-if="uploadFileData.superviseStatus!=3"
        >提交</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { editSuperviseFile } from "@/api/ffs/farmingSupervisionSheet/livingSupervisionApi.js";
export default {
  name: "uploadSupervisionFile",
  props: {
    uploadFileData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      fileType: ["doc", "pdf", "png", "jpg", "jpeg"],
      form: {},
      rules: {},
    };
  },
  created() {
    this.form = this.uploadFileData.fileList;
  },
  computed: {
    labelName() {
      return (val) => {
        if (!val) return;
        if (val.corprateName) return val.corprateName;
        if (val.nickName) return val.nickName;
        if (val.userName) return val.userName;
        if (val.phonenumber) return val.phonenumber;
      };
    },
  },
  methods: {
    close() {
      this.$emit("close");
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["ruleForm"].validate((valid) => {
        if (valid) {
          this.$modal.loading("正在提交中，请耐心等待...");
          editSuperviseFile(this.form)
            .then((response) => {
              this.$modal.closeLoading();
              if (response.code == 200) {
                this.$modal.msgSuccess("提交成功");
                this.open = false;
                this.$emit("refresh");
                this.$emit("close");
              }
            })
            .catch(() => {
              this.$modal.closeLoading();
            });
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.fieldList {
  .el-dialog__header {
    background-color: #f4f4f4;
  }

  .el-dialog__footer {
    text-align: center;
  }

  .selectWidth {
    width: 100%;
  }
  .loadicon {
    font-size: 18px;
    color: #1983e6;
    margin-left: 10px;
    vertical-align: text-bottom;
  }
  .loadicon:hover {
    color: #fff !important;
  }
  .fileconter {
    padding-left: 40px;
  }
}
.loadname {
  display: inline-block;
  width: 120px;
}
</style>

<style  lang="scss">
.filebox {
  .filelistbx {
    width: 180px !important;
  }
}
</style>

<!--  -->
<template>
  <div class="setmain">
    <el-descriptions title="被监管方" :column="3">
      <el-descriptions-item label="会员名称">{{infoData.subjectName||infoData.applyName}}</el-descriptions-item>
      <el-descriptions-item label="联系电话">{{infoData.subjectPhone||infoData.applyPhone}}</el-descriptions-item>
      <el-descriptions-item
        label="身份证号"
        v-if="subjectUserInfo.corprateIdNo"
      >{{subjectUserInfo.corprateIdNo}}</el-descriptions-item>
    </el-descriptions>
    <div v-if="loanOfficerUserInfo.companyName!=''">
      <el-descriptions title="委托方信息" class="mtop" :column="3">
        <el-descriptions-item label="企业名称">{{loanOfficerUserInfo.companyName}}</el-descriptions-item>
        <el-descriptions-item label="企业类型">银行</el-descriptions-item>
        <el-descriptions-item label="营业执照编号">{{loanOfficerUserInfo.businessLicenseNo}}</el-descriptions-item>
        <el-descriptions-item
          label="地址"
        >{{loanOfficerUserInfo.provinceName}}{{loanOfficerUserInfo.cityName}}{{loanOfficerUserInfo.countyName}}{{loanOfficerUserInfo.detailAddress}}</el-descriptions-item>
        <el-descriptions-item label="法人">{{loanOfficerUserInfo.realName}}</el-descriptions-item>
        <el-descriptions-item label="法人电话">{{loanOfficerUserInfo.phoneNumber}}</el-descriptions-item>
        <el-descriptions-item label="身份证号">{{loanOfficerUserInfo.idCard}}</el-descriptions-item>
        <el-descriptions-item label="负责人姓名">{{loanOfficerUserInfo.corprateName||'--'}}</el-descriptions-item>
        <el-descriptions-item label="负责人电话">{{loanOfficerUserInfo.adminPhonenumber||'--'}}</el-descriptions-item>
      </el-descriptions>
    </div>

    <el-descriptions title="调研信息" class="mtop" :column="3">
      <el-descriptions-item label="调研信息" v-if="infoData.intentionListId">
        <span class="wbrdw" @click="lookCreditInvestigation">查看详情></span>
      </el-descriptions-item>
      <el-descriptions-item label="监管方案" v-if="infoData.regulatoryScheme">
        <span class="loadpc">
          <i class="el-icon-folder"></i>
          监管方案.{{regulatorySchemeName(infoData.regulatoryScheme)}}
        </span>
        <a :href="infoData.regulatoryScheme">
          <span class="wbrdw">查看详情></span>
        </a>
      </el-descriptions-item>

      <el-descriptions-item label="意向单附件" v-if="infoData.fileIntentionList">
        <span class="loadpc">
          <i class="el-icon-folder"></i>
          意向单附件.{{regulatorySchemeName(infoData.fileIntentionList)}}
        </span>

        <a :href="infoData.fileIntentionList">
          <span class="wbrdw">查看详情></span>
        </a>
      </el-descriptions-item>

      <el-descriptions-item label="调研表附件" v-if="infoData.fileCreditInvestigation">
        <span class="loadpc">
          <i class="el-icon-folder"></i>
          调研表附件.{{regulatorySchemeName(infoData.fileCreditInvestigation)}}
        </span>

        <a :href="infoData.fileCreditInvestigation">
          <span class="wbrdw">查看详情></span>
        </a>
      </el-descriptions-item>
    </el-descriptions>

    <el-descriptions title="被监管方家庭住址" class="mtop" :column="3"></el-descriptions>
    <el-form ref="form" :model="form" :rules="rules" label-width="110px">
      <el-form-item label="详细地址：" prop="address">
        <div style="margin-top:-10px">
          <map-control @position="mapPosition" :formData="formData" />
        </div>
      </el-form-item>
    </el-form>
    <model-form v-if="dialog.open" :dialog="dialog"></model-form>
  </div>
</template>

<script>
import MapControl from "./MapControl.vue";
import { areaData } from "@/utils/mixin/area.js";
import { searchUser } from "@/api/system/user.js"; // 查询搜索用户  可以手机号、昵称等等信息搜索
import { getEnterpriseInfo } from "@/api/ffs/farmingSupervisionSheet/livingSupervisionApi"; // 搜索委托方
import modelForm from "@/views/ffs/supervisionSheet/supervisionApply/components/modelForm.vue";
export default {
  mixins: [areaData],
  name: "insstepOen",
  components: { MapControl, modelForm },
  props: {
    infoData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      bankList: [],
      dialog: {
        open: false,
        title: "",
        id: "",
      },
      formData: {},
      form: {
        superviseId: "", //监管单id
        applyPhone: "", //被监管人手机号
        creditInvestigationId: "", //调研信息id
        intentionListId: "", //意向单id
        provinceId: "", //省id
        provinceName: "", //省名称
        cityId: "", //市id
        cityName: "", //市名称
        countyId: "", //区县id
        countyName: "", //区县名称
        address: "", //详细地址
        addressLoglat: "", //被监管方经纬度
        citySourceaArr: [],
        oenOk: false,
      },
      // 表单校验
      rules: {
        citySourceaArr: [
          { required: true, message: "请选择住址省市区", trigger: "blur" },
        ],
        address: [
          { required: true, message: "请填写详细地址", trigger: "blur" },
        ],
      },
      subjectUserInfo: {
        corprateIdNo: "",
      },
      loanOfficerUserInfo: {
        companyName: "",
      },
    };
  },
  created() {
    if (this.infoData.operation == "edit") {
      this.$store.commit("SET_ADD_FROM", this.infoData);
      this.form = this.$store.getters.supervision.addFrom;
      const { provinceName, cityName, countyName, address, addressLoglat } =
        this.infoData;
      this.form.provinceName = provinceName;
      this.form.cityName = cityName;
      this.form.countyName = countyName;
      this.form.address = address;
      this.form.addressLoglat = addressLoglat;
      this.formData = {
        address,
        addrPoint: {
          lng: addressLoglat?.split(",")[0],
          lat: addressLoglat?.split(",")[1],
        },
      };
    } else {
      this.form = this.$store.getters.supervision.addFrom;
      this.form.applyName = this.infoData.applyName;
      this.form.applyPhone = this.infoData.applyPhone;
      this.form.subjectType = this.infoData.subjectType;
      this.form.regulatoryScheme = this.infoData.regulatoryScheme;
      this.form.bankId = this.infoData.bankId;
      this.form.bankName = this.infoData.bankName;
      this.form.applyId = this.infoData.subjectUserId;
      this.form.loanOfficerPhone = this.infoData.loanOfficerPhone;
    }
    if (!this.infoData.intentionListId) {
      this.form.fileIntentionList = this.infoData.fileIntentionList;
      this.form.fileCreditInvestigation = this.infoData.fileCreditInvestigation;
    } else {
      this.form.creditInvestigationId = this.infoData.creditInvestigationId;
      this.form.intentionListId = this.infoData.intentionListId;
    }
    this.initData();
  },
  computed: {
    regulatorySchemeName() {
      return (val) => {
        if (!val) return "";
        return val.slice(val.lastIndexOf(".") + 1);
      };
    },
  },
  mounted() {},
  methods: {
    //提交验证
    submitOen() {
      let oenOk = false;
      this.$refs["form"].validate((valid) => {
        if (!valid) return false;
        if (this.form.addressLoglat == "") {
          this.$message({
            message: "请选择详细地址",
            type: "error",
          });
          return false;
        }
        this.form.oenOk = valid;
        oenOk = valid;
      });
      return oenOk;
    },
    clickIpot() {
      if (this.form.provinceId == "") {
        this.form.address = "";
        this.$message({
          message: "请先选择住址省市区",
          type: "error",
        });
        return;
      }
      this.getLonLat();
    },
    async initData() {
       // 获取委托方信息
      getEnterpriseInfo({
        tenantId:this.infoData.bankId
      }).then((res) => {
        if (res.code == 200 && res.result) {
          this.loanOfficerUserInfo = res.result;
        }
      });

      // 获取被监管方用户信息
      searchUser({
        phonenumber: this.infoData.subjectPhone || this.infoData.applyPhone,
      }).then((res) => {
        // console.log("获取被监管方用户信息:", res);
        if (res.code == 200 && res.result.length) {
          this.subjectUserInfo = res.result[0];
        }
      });
    },
    //去调研信息
    lookCreditInvestigation() {
      this.dialog.open = true;
      this.dialog.id = this.infoData.intentionListId;
    },
    // 获取地图选择的位置
    mapPosition(po) {
      //     console.log("获取地图选择的位置po：", po);
      this.form.provinceName = po.position.provinceName;
      this.form.cityName = po.position.cityName;
      this.form.countyName = po.position.countyName;
      this.form.address = po.position.address;
      this.form.addressLoglat = po.position.addressLoglat;
    },
  },
};
</script>
<style  scoped lang="scss">
@import url("./index.scss");
</style>

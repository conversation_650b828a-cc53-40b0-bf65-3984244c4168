<template>
  <div class="app-container">
        <el-autocomplete
          v-model="form.address"
          style="width:100%;"
          popper-class="autoAddressClass"
          :fetch-suggestions="querySearchAsync"
          :trigger-on-focus="false"
          placeholder="详细地址"
          clearable
          @select="handleSelect"
          :scroll-wheel-zoom='true'
          :double-click-zoom='true'
        >
          <template slot-scope="{ item }">
            <i class="el-icon-search fl mgr10" />
            <div style="overflow:hidden;">
              <div class="title">{{ item.title }}</div>
              <span class="address ellipsis">{{ item.address }}</span>
            </div>
          </template>
        </el-autocomplete>
    
        <div id="map-container" style="width:100%;height:500px;" />
     
      <!-- <el-form-item>
        <el-button type="primary" @click="onSubmit">提交</el-button>
        <el-button>取消</el-button>
      </el-form-item>
    </el-form> -->
  </div>
</template>

<script>
import loadBMap from "@/utils/loadBMap.js";
export default {
        props:{
            formData:{
                typeof:Object,
                default:()=>{}
            }    
        },
  data() {
    return {
      form: {
        address: "", // 详细地址
        addrPoint: {
          // 详细地址经纬度
          lng: 0,
          lat: 0,
        },
      },
      map: "", // 地图实例
      mk: "", // Marker实例
      point:'',
      locationPoint: null,
    };
  },
  async mounted() {
    await loadBMap("oW2UEhdth2tRbEE4FUpF9E5YVDCIPYih"); // 加载引入BMap
   
    if(this.formData.addrPoint){
         this.initMap(this.formData.addrPoint.lng,this.formData.addrPoint.lat);
    }else{
         this.initMap(116.05438847633755, 43.93942350921009);
    }
    
  },
  watch:{
        formData:{
           immediate: true,
           handler:function(val) {
                this.form.address = val.address;
                this.form.addrPoint = val.point;
           }
        },
  },
  
  methods: {
    // 初始化地图
    initMap(lng,lat) {
      var that = this;

      // 1、挂载地图
      this.map = new BMap.Map("map-container", { enableMapClick: false });
      var point = new BMap.Point(lng,lat);
      this.map.centerAndZoom(point, 18);

      // 3、设置图像标注并绑定拖拽标注结束后事件
      this.mk = new BMap.Marker(point, { enableDragging: true });
      this.map.addOverlay(this.mk);
      this.mk.addEventListener("dragend", function (e) {
        that.getAddrByPoint(e.point);
      });

      // 4、添加（右上角）平移缩放控件
      this.map.addControl(
        new BMap.NavigationControl({
          anchor: BMAP_ANCHOR_TOP_RIGHT,
          type: BMAP_NAVIGATION_CONTROL_SMALL,
        })
      );

      // 5、添加（左下角）定位控件
      var geolocationControl = new BMap.GeolocationControl({
        anchor: BMAP_ANCHOR_BOTTOM_LEFT,
      });
      geolocationControl.addEventListener("locationSuccess", function (e) {
        that.getAddrByPoint(e.point);
      });
      geolocationControl.addEventListener("locationError", function (e) {
        // alert(e.message);
      });
      this.map.addControl(geolocationControl);

      // 6、浏览器定位
    //   this.geolocation();

      // 7、绑定点击地图任意点事件
      this.map.addEventListener("click", function (e) {
        that.getAddrByPoint(e.point);
      });
    },
    // 获取两点间的距离
    getDistancs(pointA, pointB) {
      return this.map.getDistance(pointA, pointB).toFixed(2);
    },
    // 浏览器定位函数
    geolocation() {
         var that = this
      var geolocation = new BMap.Geolocation();
      geolocation.getCurrentPosition(
        function(res) {
          if (this.getStatus() == BMAP_STATUS_SUCCESS) {
            that.getAddrByPoint(res.point);
            that.locationPoint = res.point;
          } else {
            //     alert("failed" + this.getStatus());
            that.locationPoint = new BMap.Point(113.3324436, 23.1315381);
          }
        },
        { enableHighAccuracy: true }
      );
    },
    // 2、逆地址解析函数
    getAddrByPoint(point) {
      var geco = new BMap.Geocoder();
      geco.getLocation(point, (res) => {
        if(!res) return;
        this.mk.setPosition(point);
        this.map.panTo(point);
        this.form.address = res.address;
        this.form.addrPoint = point;
        this.form.position = {
          provinceName: res.addressComponents.province,
          cityName: res.addressComponents.city,
          countyName: res.addressComponents.district,
          address: res.address, //详细地址
          addressLoglat: point.lng + "," + point.lat,
        };
        this.$emit("position", this.form);
      });
    },
    // 8-1、地址搜索
    querySearchAsync(str, cb) {
      var options = {
        onSearchComplete: function (res) {
          var s = [];
          if (local.getStatus() == BMAP_STATUS_SUCCESS) {
            for (var i = 0; i < res.getCurrentNumPois(); i++) {
              s.push(res.getPoi(i));
            }
            cb(s);
          } else {
            cb(s);
          }
        },
      };
      var local = new BMap.LocalSearch(this.map, options);
      local.search(str);
    },
    // 8-2、选择地址
    handleSelect(item) {
      this.form.address = item.address + item.title;
      this.form.addrPoint = item.point;
      let myGeo = new BMap.Geocoder();
      var that=this
     // 根据坐标得到地址描述    下面输入坐标。
     myGeo.getLocation(new BMap.Point(item.point.lng, item.point.lat), function (result) {
            if (result) {
               console.log(result);
               that.form.position = {
               provinceName: result.addressComponents.province,
               cityName: result.addressComponents.city,
               countyName: result.addressComponents.district,
               address: item.address + item.title, //详细地址
               addressLoglat: item.point.lng + "," + item.point.lat,
             };
           }else{
            that.form.position = {
                   provinceName: item.province,
                   cityName: item.city,
                   countyName: "",
                   address: item.address + item.title, //详细地址
                   addressLoglat: item.point.lng + "," + item.point.lat,
               };
           } 
           that.$emit("position", that.form);
           that.map.clearOverlays();
           that.mk = new BMap.Marker(item.point);
           that.map.addOverlay(that.mk);
           that.map.panTo(item.point);

       });
    
    },
    onSubmit() {
      console.log(this.form);
    },
  },
};
</script>

<style lang="scss" scoped>
.autoAddressClass {
  li {
    i.el-icon-search {
      margin-top: 11px;
    }
    .mgr10 {
      margin-right: 10px;
    }
    .title {
      text-overflow: ellipsis;
      overflow: hidden;
    }
    .address {
      line-height: 1;
      font-size: 12px;
      color: #b4b4b4;
      margin-bottom: 5px;
    }
  }
}
</style>


<!--  -->
<template>
  <div class="main">
    <el-form ref="form" :model="form" :rules="rules" label-width="120px">
      <div class="iputiirt">监管详情</div>
      <el-row type="flex" justify="space-between">
        <el-col :span="11">
          <el-form-item label="合同编号：" prop="contractNo">
            <el-input
              v-model.trim="form.contractNo"
              placeholder="请输入合同编号"
              maxlength="30"
              clearable
              :disabled="disabled"
            />
          </el-form-item>
        </el-col>
        <el-col :span="11">
          <el-form-item label="授信金额：" prop="superviseAmount">
            <el-input
              type="number"
              v-model.trim="form.superviseAmount"
              placeholder="请输入授信金额"
              maxlength="30"
              clearable
              :disabled="disabled"
              @blur="changeSuperviseAmount"
              oninput="if(value.length>10)value=value.slice(0,10)"
            >
              <template slot="append">万</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row type="flex" justify="space-between">
        <el-col :span="11">
          <el-form-item label="监管周期：" prop="superviseLimit">
            <el-select
              v-model="form.superviseLimit"
              placeholder="请选择监管周期"
              class="inputWidth"
              @change="changeTerm"
            >
              <el-option v-for="item in 36 " :key="item.item" :label="item+'个月'" :value="item"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="11">
          <el-form-item label="起始日期：" prop="superviseStart">
            <el-date-picker
              class="inputWidth"
              v-model="form.superviseStart"
              type="date"
              placeholder="选择起始日期"
              @change="changeTerm"
            ></el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row type="flex" justify="space-between">
        <el-col :span="11">
          <el-form-item label="监管时间：" prop="placeName">
            <el-input
              v-model="form.superviseEnd"
              placeholder="请输入监管时间"
              maxlength="30"
              clearable
              disabled
            />
          </el-form-item>
        </el-col>
        <el-col :span="11">
          <el-form-item label="服务费率：" prop="superviseServiceRate">
            <el-input
              v-model.trim="form.superviseServiceRate"
              placeholder="请输入服务费率"
              maxlength="30"
              clearable
              type="number"
              oninput="if(value.length>10)value=value.slice(0,10)"
              @blur="changeSuperviseServiceRate"
            >
              <template slot="append">%</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row type="flex" justify="space-between">
        <el-col :span="11">
          <el-form-item label="用信金额：" prop="superviseAmountReceive">
            <el-input
              v-model.trim="form.superviseAmountReceive"
              placeholder="请输入用信金额"
              maxlength="30"
              clearable
              :disabled="disabled"
              @blur="superviseAmountReceiveAmountBlur"
              type="number"
              oninput="if(value.length>10)value=value.slice(0,10)"
            >
              <template slot="append">万</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="11">
          <el-form-item label="付款方类型：" prop="payerCode">
            <el-select v-model="form.payerCode" placeholder="请选择" clearable class="inputWidth">
              <el-option
                v-for="dict in dict.type.ffs_supervise_payer"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row type="flex" justify="space-between">
        <el-col :span="11">
          <el-form-item label="服务费类型：" prop="serviceFeeType">
            <el-radio v-model="form.serviceFeeType" :label="1">非包干</el-radio>
            <el-radio v-model="form.serviceFeeType" :label="0">包干</el-radio>
          </el-form-item>
        </el-col>
        <el-col :span="11">
          <el-form-item label="日服务费：" prop="dayServiceFee">
            <el-input
              type="number"
              v-model.trim="form.dayServiceFee"
              placeholder="请输入日服务费"
              maxlength="30"
              clearable
              :disabled="disabled"
              @blur="changeDayServiceFee"
              oninput="if(value.length>10)value=value.slice(0,10)"
            >
              <template slot="append">元/天</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row type="flex" justify="space-between">
      <el-col :span="11">
          <el-form-item label="是否预警：" prop="earlyFlag">
            <el-radio :label="1" v-model.number="form.earlyFlag">是</el-radio>
            <el-radio :label="0" v-model.number="form.earlyFlag">否</el-radio>
          </el-form-item>
        </el-col>
        <el-col :span="11">
            <el-form-item label="预警接收人：" prop="earlySendUser" >
              <el-select
                v-model="form.earlySendUser"
                multiple
                filterable
                remote
                reserve-keyword
                placeholder="请输入"
                :remote-method="remoteMethod"
                class="selectWidth"
                @remove-tag="removeItem"
              >
                <el-option
                  v-for="item in selectList"
                  :key="item.userId"
                  :label="labelName(item)"
                  :value="item.userId"
                  @click.native="selectUser(item)"
                ></el-option>
              </el-select>
            </el-form-item>
        </el-col>
        </el-row>
      <el-row type="flex" justify="space-between">
        <el-col :span="11">
          <el-form-item label="结算方式：" prop="settlementWay">
            <el-radio
              v-model.number="form.settlementWay"
              :label="settlementWay.value"
              v-for="settlementWay in settlementWayList"
              :key="settlementWay.value"
            >{{settlementWay.name}}</el-radio>
            <div class="qiangti">注：年服务费=授信金额*费率；半年服务费=年服务费/2；季度服务费=年服务费/4</div>
          </el-form-item>
        </el-col>
        <el-col :span="11">
          <el-form-item label="总服务费：">
            <el-input
              v-model.trim="(form.superviseAmountReceive*form.superviseServiceRate*100).toFixed(2)"
              placeholder="请输入总服务费"
              maxlength="30"
              clearable
              disabled
              type="number"
              oninput="if(value.length>10)value=value.slice(0,10)"
            >
              <template slot="append">元</template>
            </el-input>
            <div class="qiangti">注：总服务费为监管周期预计总服务费，总服务费=用信金额*100*服务费率</div>
          </el-form-item>
        </el-col>
      </el-row>
      <!-- <el-row type="flex" justify="space-between">
      <el-col :span="11">
            <el-form-item label="监管方式：" prop="superviseWay">
                <el-select v-model="form.superviseWay" placeholder="请选择" clearable class="inputWidth">
                <el-option
                    v-for="dict in dict.type.ffs_supervise_way"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value*1"
                />
                </el-select>
            </el-form-item>
        </el-col>
        </el-row> -->
      <div class="iputiirt nkfdj">
        <span class="marrt20">监管文件</span>
        <span class="qiangti">(大小不超过100MB 格式为 doc/pdf/png/jpg的文件)</span>
      </div>
      <el-row>
        <el-col :span="8">
          <el-form-item label="备注说明：" prop="fileRemark">
            <el-input type="textarea" v-model="form.fileRemark" placeholder="请输入备注说明" ></el-input>
          </el-form-item>
          </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item label="三方协议：" prop="fileTripleAgreement">
            <file-upload
              :fileType="['doc', 'pdf','png', 'jpg', 'jpeg']"
              :fileSize="100"
              :limit="1"
              v-model="form.fileTripleAgreement"
              :isShowTip="false"
              expandName="三方协议"
            >
              <el-button type="primary" size="mini">
                上传三方协议
                <i class="el-icon-upload loadicon"></i>
              </el-button>
            </file-upload>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="贷款单：" prop="fileLoan">
            <file-upload
              :fileType="['doc', 'pdf','png', 'jpg', 'jpeg']"
              :fileSize="100"
              :limit="1"
              v-model="form.fileLoan"
              :isShowTip="false"
              expandName="贷款单"
            >
              <el-button type="primary" size="mini">
                上传贷款单
                <i class="el-icon-upload loadicon"></i>
              </el-button>
            </file-upload>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="租赁合同：" prop="fileLeaseContract">
            <file-upload
              :fileType="['doc', 'pdf','png', 'jpg', 'jpeg']"
              :fileSize="100"
              :limit="1"
              v-model="form.fileLeaseContract"
              :isShowTip="false"
              expandName="租赁合同"
            >
              <el-button type="primary" size="mini">
                上传租赁合同
                <i class="el-icon-upload loadicon"></i>
              </el-button>
            </file-upload>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="其它：" prop="fileOther">
            <file-upload
              :fileType="['doc', 'pdf','png', 'jpg', 'jpeg']"
              :fileSize="100"
              :limit="1"
              v-model="form.fileOther"
              :isShowTip="false"
              expandName="其它附件"
            >
              <el-button type="primary" size="mini">
                上传其它文件
                <i class="el-icon-upload loadicon"></i>
              </el-button>
            </file-upload>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row class="filerow">
        <el-form-item label="上传视频" prop="offlineVideoUrlStr">
            <uploadVideo :type="2" v-model="form.offlineVideoUrlStr"></uploadVideo>
          </el-form-item>
      </el-row>
    </el-form>
  </div>
</template>

<script>
import { parseTime, DateAdd, dateAddDays, toFixed2 } from "@/utils/east.js";
import { settlementWayList } from "@/views/ffs/supervisionSheet/livingSupervision/utils/formatVal.js";
import { searchUser } from "@/api/system/user.js";
import uploadVideo from "@/components/uploadVideo/index.vue"
var dayjs = require("dayjs");
export default {
  dicts: ["ffs_supervise_payer","ffs_supervise_way"],
  name: "insstepTwo",
  components:{
    uploadVideo
  },
  props: {
    infoData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    // 默认价格
    const twoPoint = (rule, value, callback) => {
      if (/^\d+\.?\d{0,2}$/.test(value)) {
        if (
          value.indexOf(".") == "-1" &&
          value.length > 1 &&
          value.slice(0, 1) == "0"
        ) {
          return callback(new Error("最多包含两位⼩数的正数且不能为以0开头"));
        }
        callback();
      } else {
        return callback(new Error("最多包含两位⼩数的正数且不能为以0开头"));
      }
    };
    return {
      disabled: false,
      selectList:[],
      form: {
        contractNo: "", //合同编号
        superviseAmount: "", // 授信金额（万分
        superviseAmountReceive: "", // 用信金额（万分）
        superviseLimit: "", //监管周期（月）
        superviseStart: "", //监管开始时间
        superviseEnd: "", //监管结束时间
        superviseServiceRate: "", //总服务费率
        superviseServiceFee: "", //监管服务费（分）
        settlementWay: "", //结算方式：1年付，2半年付，3季付
        fileTripleAgreement: "", //三方协议文件
        fileOther: "", //其他文件
        fileLoan: "", //贷款单文件
        fileLeaseContract: "", //租赁合同文件
        fileRemark:'',
        payerCode: null, //付款方类型
        serviceFeeType: 1, //服务费类型 0，包干；1，非包干
        dayServiceFee: "", //日监管服务费
        earlySendUser:[],//预警用户id
        earlySendUserStr:[],//预警用户
        earlyFlag: '',//0 不预警 1 要预警
        offlineVideoUrlStr:'',
        // superviseWay:''//监管方式
      },
      settlementWayList: settlementWayList(),
      // 表单校验
      rules: {
        // superviseWay:[
        // { required: true, message: "请选择监管方式", trigger: "blur" },
        // ],
        contractNo: [
          { required: true, message: "请填写合同编号", trigger: "blur" },
        ],
        superviseAmount: [
          { required: true, message: "请填写授信金额", trigger: "blur" },
          {
            validator: twoPoint,
            message: "请填写正确有效的授信金额",
            trigger: "blur",
          },
        ],
        superviseAmountReceive: [
          { required: true, message: "请填写用信金额", trigger: "blur" },
          {
            validator: twoPoint,
            message: "请填写正确有效的用信金额",
            trigger: "blur",
          },
        ],
        superviseLimit: [
          { required: true, message: "请选择监管时间", trigger: "blur" },
        ],
        superviseStart: [
          { required: true, message: "请选择起始日期", trigger: "blur" },
        ],
        superviseServiceRate: [
          { required: true, message: "请填写总服务费", trigger: "blur" },
          {
            validator: twoPoint,
            message: "请填写正确有效的总服务费",
            trigger: "blur",
          },
        ],
        superviseServiceFee: [
          { required: true, message: "请填写监管服务费", trigger: "blur" },
          {
            validator: twoPoint,
            message: "请填写正确有效的监管服务费",
            trigger: "blur",
          },
        ],
        serviceFeeType: [
          { required: true, message: "请选择服务费类型", trigger: "blur" },
        ],
        dayServiceFee: [
          { required: true, message: "请输入日服务费", trigger: "change" },
          {
            validator: twoPoint,
            message: "请填写正确有效的日服务费",
            trigger: "change",
          },
        ],
        payerCode: [
          { required: true, message: "请选择付款方类型", trigger: "blur" },
        ],
        fileTripleAgreement: [
          { required: false, message: "请上传三方协议", trigger: "blur" },
        ],
      },
    };
  },

  computed: {
    labelName() {
      return (val) => {
        if (!val) return;
        if (val.corprateName) return val.corprateName;
        if (val.nickName) return val.nickName;
        if (val.userName) return val.userName;
        if (val.phonenumber) return val.phonenumber;
      };
    }
  },
  created() {
    if (this.infoData.operation == "edit") {
        if(this.infoData.earlySendUser){
            this.infoData.earlySendUser=this.infoData.earlySendUser.split(',')
        }
        if(this.infoData.earlySendUserStr){
            this.infoData.earlySendUserStr=JSON.parse(this.infoData.earlySendUserStr)
            this.selectList=this.infoData.earlySendUserStr
        }
      this.$store.commit("SET_ADD_FROM", this.infoData);
    }
    this.form = this.$store.getters.supervision.addFrom;
  },
  mounted() {},
  methods: {
    removeItem(val){
      let  newArr= this.form.earlySendUserStr.filter(function(obj){ 
             return val !== obj.userId;
        })
        this.form.earlySendUserStr=newArr
    },
    selectUser(item){
        console.log(item);
         let obj={
                  userName:item.corprateName||item.nickName||item.userName||item.phonenumber,
                  userId:item.userId
            }
            console.log(this.form.earlySendUserStr);
        this.form.earlySendUserStr.push(obj)
  
    },
    remoteMethod(data) {
      searchUser({ phonenumber: data ,userType:'00'}).then(async (res) => {
        if (res.code == 200) {
            this.selectList = res.result || [];
        }
      });
    },
    //提交验证
    submitTwo() {
      let twoOk = false;
      this.form.superviseServiceFee =
        toFixed2(this.form.superviseAmountReceive * this.form.superviseServiceRate * 100);
      this.form.superviseStart = parseTime(
        this.form.superviseStart,
        "{y}-{m}-{d}"
      );
      this.$refs["form"].validate((valid) => {
        if(!this.form.fileRemark&&!(this.form.fileTripleAgreement||this.form.fileOther||this.form.fileLoan||this.form.fileLeaseContract||this.form.offlineVideoUrlStr)){
            this.$message.error('监管文件为空，请填写备注说明！')
            return 
        }
        if (!valid) return false;
        this.form.twoOk = valid;
        twoOk = valid;
        return true;
      });
      return twoOk;
    },
    changeTerm() {
      const sourceForm = this.form;
      if (sourceForm.superviseLimit && sourceForm.superviseStart) {
        sourceForm.superviseEnd = dayjs(sourceForm.superviseStart)
          .add(sourceForm.superviseLimit, "month").format("YYYY-MM-DD");
      }
      this.autuoDayServiceFee();
    },

    //     用信金额
    superviseAmountReceiveAmountBlur(val) {
      if (this.form.superviseAmount == "") {
        this.$message({
          message: "请先填写授信金额",
          type: "error",
        });
        this.form.superviseAmountReceive = "";
        return;
      }
      const blurVal = val.target.value * 1;
      if (blurVal > this.form.superviseAmount) {
        this.form.superviseAmountReceive = this.form.superviseAmount;
        this.$message({
          message: "用信金额不能大于授信金额",
          type: "error",
        });
      }
      this.autuoDayServiceFee();
    },
    // 日服务费金额=服务费总额/监管天数
    autuoDayServiceFee() {
      const dataform = this.form;
      if (
        dataform.superviseAmountReceive != "" &&
        dataform.superviseStart != "" &&
        dataform.superviseLimit != "" &&
        dataform.superviseServiceRate != ""
      ) {
        const stday = parseTime(dataform.superviseStart, "{y}-{m}-{d}");
        const maxDay = dayjs(dataform.superviseEnd).diff(stday, "day");
        const superviseServiceFee = dataform.superviseAmountReceive * dataform.superviseServiceRate * 100;
        const maxDayServiceFee = toFixed2(
          ((superviseServiceFee * 1) / (maxDay)) * 1
        );
        dataform.dayServiceFee = maxDayServiceFee;
      }
    },
    changeDayServiceFee(val) {
      const dataform = this.form;
      if (dataform.superviseAmount == "") {
        this.$message({
          message: "请先填写授信金额",
          type: "error",
        });
        return;
      }
      if (dataform.superviseServiceRate == "") {
        this.$message({
          message: "请先填写服务费率",
          type: "error",
        });
        return;
      }

      if (dataform.superviseLimit == "") {
        this.$message({
          message: "请先填选择监管周期",
          type: "error",
        });
        return;
      }

      if (dataform.superviseStart == "") {
        this.$message({
          message: "请先填选择起始日期",
          type: "error",
        });
        return;
      }
      const stday = parseTime(dataform.superviseStart, "{y}-{m}-{d}");
      const maxDay = dayjs(dataform.superviseEnd).diff(stday, "day");
      const superviseServiceFee =
        dataform.superviseAmount * dataform.superviseServiceRate * 100;
      const maxDayServiceFee = toFixed2(
        ((superviseServiceFee * 1) / maxDay) * 1
      );
      const blurVal = val.target.value * 1;

      if (blurVal > maxDayServiceFee) {
        this.form.dayServiceFee = maxDayServiceFee;
        this.$message({
          message: "最大日服务费不能大于：" + maxDayServiceFee,
          type: "error",
        });
        return;
      }

      
      // this.autuoDayServiceFee()
    },

    changeSuperviseAmount(val) {
      const blurVal = val.target.value * 1;
      if (blurVal == "") {
        return;
      }

      if (blurVal < this.form.superviseAmountReceive) {
        this.form.superviseAmount = "";
        this.$message({
          message: "授信金额不能小于用信金额",
          type: "error",
        });
      }
    //   this.autuoDayServiceFee();
    },
    changeSuperviseServiceRate(val) {
      const blurVal = val.target.value * 1;
      if (blurVal > 2) {
        this.$message({
          message: "服务费率不能大于2",
          type: "error",
        });
        this.form.superviseServiceRate = "";
        return;
      }
      if (blurVal <0) {
        this.$message({
          message: "服务费率不能小于0",
          type: "error",
        });
        this.form.superviseServiceRate = "";
        return;
      }
      this.autuoDayServiceFee();
    },
  },
};
</script>
<style  scoped>
.loadicon {
  font-size: 18px;
  color: #fff;
  margin-left: 10px;
  vertical-align: text-bottom;
}
</style>

<template>
  <div>
    <el-dialog
      :title="selectCargoAreaFormData.title"
      :visible.sync="selectCargoAreaFormData.open"
      width="1050px"
      :close-on-click-modal="false"
      @close="close"
      class="fieldList"
      append-to-body
    >
      <el-row :gutter="10" class="mb8" type="flex" justify="center" v-loading="loading">
        <el-form :model="queryParams" ref="queryForm" :inline="true">
          <el-form-item label="选择仓库：">
            <el-select
              v-model="queryParams.storehouseId"
              placeholder="请选择仓库"
              style="width:750px"
              @change="changeStorehouse"
            >
              <el-option
                v-for="item in storehouseList"
                :key="item.storehouseId"
                :label="item.storehouseName"
                :value="item.storehouseId"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </el-row>

      <el-checkbox-group v-model="checkList">
        <el-checkbox
          v-for="item in list"
          :key="item.positionId"
          :label="item.positionId"
          :value="item.positionName"
          border
        >{{item.positionName}}</el-checkbox>
      </el-checkbox-group>

      <el-row class="mb8" v-show="list.length==0" type="flex" justify="center">请先在上面选择一个仓库，再选择货区吧！</el-row>

      <span slot="footer" class="dialog-footer" v-show="!selectCargoAreaFormData.disable">
        <el-button @click="close">取消</el-button>
        <el-button type="primary" @click="submitForm">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { storeHousList, positionList } from "@/api/ffs/farmingMortgage/storeManage";
export default {
  name: "selectCargoArea",
  props: {
    selectCargoAreaFormData: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      queryParams: {
        storehouseId: "",
      },
      storehouseList: [],
      // 遮罩层
      loading: true,
      list: [],
      checkList: [],
    };
  },
  created() {
    const { storehouseId, checkList } = this.selectCargoAreaFormData;
    if (storehouseId) {
      this.queryParams.storehouseId = storehouseId;
      this.checkList = checkList;
    }
    this.getList();
  },

  methods: {
    /** 查询仓库列表 */
    getList() {
      storeHousList({
        pageNum: 1,
        pageSize: 9999,
      }).then((res) => {
        this.loading = false;
        if (res.code == 200) {
          this.storehouseList = res.result.list || [];
          const { storehouseId } = this.selectCargoAreaFormData;
          if (storehouseId) {
            this.queryParams.storehouseId = storehouseId;
            this.getStoreHousePositionList();
          }
        } else {
          this.$message.error(res.message);
        }
      });
    },

    /** 根据仓库查询货区列表 */
    getStoreHousePositionList() {
      positionList({
        pageNum: 1,
        pageSize: 9999,
        storehouseId: this.queryParams.storehouseId,
      }).then((res) => {
        if (res.code == 200) {
          this.list = res.result.list || [];
          const { checkList } = this.selectCargoAreaFormData;
          if (checkList && checkList.length != 0) {
            this.checkList = checkList;
          }
        } else {
          this.$message.error(res.message);
        }
      });
    },
    changeStorehouse(id) {
      this.getStoreHousePositionList();
    },
    submitForm() {
      if (this.queryParams.storehouseId == "") {
        this.$message({
          type: "error",
          message: "请选择仓库",
        });
        return;
      }
      if (this.checkList.length == 0) {
        this.$message({
          type: "error",
          message: "请选择货区",
        });
        return;
      }

      const storehouseInfo = this.storehouseList.filter((item) => {
        if (item.storehouseId == this.queryParams.storehouseId) {
          return item;
        }
      });

      const positionList = [];
      this.checkList.forEach((positionId) => {
        this.list.forEach((nm) => {
          if (positionId == nm.positionId) {
            nm.ownerName = storehouseInfo[0].ownerName;
            positionList.push(nm);
          }
        });
      });

      this.$emit("cargoAreaRefresh", storehouseInfo[0], positionList);
      this.$emit("close");
    },

    //关闭弹框
    close() {
      this.$emit("close");
    },
  },
};
</script>

<style lang="scss">
.fieldList {
  .el-dialog__header {
    background-color: #f4f4f4;
  }

  .el-dialog__footer {
    text-align: center;
  }

  .selectWidth {
    width: 100%;
  }
}
.head-title {
  font-size: 16px;
  color: #999;
  margin-bottom: 20px;
}
.head-title span {
  color: red;
}
.selectWidth {
  width: 100%;
}
</style>

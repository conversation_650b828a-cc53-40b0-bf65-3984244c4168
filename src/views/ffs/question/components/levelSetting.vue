<template>
  <div>
    <el-dialog
      :title="levelSettingData.title"
      :visible.sync="levelSettingData.open"
      width="700px"
      :close-on-click-modal="false"
      @close="close"
      class="fieldList"
    >
      <el-form :model="form" status-icon ref="ruleForm" label-width="130px">
        <template v-for="(item,index) in form.params">
          <el-form-item
            :label="item.levelDesc+' 级（'+levelVal[index]+'）'"
            class="priceFormItem"
            :prop="'params.' + index + '.minScore'"
            :rules="{ required: true, message: item.levelDesc+'级最高和最低不能为空', trigger: 'blur'}"
          >
            <el-input
              v-model.number="item.minScore"
              :placeholder="item.levelDesc+'级最低分'"
              style="width: 240px"
              clearable
              type="number"
              oninput="if(value.length>2)value=value.slice(0,2)"
              @change="scoreCheck(item,index,'min')"
              :readonly="item.disb[0]"
            />分 -
            <el-input
              v-model.number="item.maxScore"
              :placeholder="item.levelDesc+'级最高分'"
              style="width: 240px"
              type="number"
              clearable
              :readonly="item.disb[1]"
              @change="scoreCheck(item,index,'max')"
            />分
          </el-form-item>
        </template>
      </el-form>
      <div class="woentxt">各区段分值不包含最低分，包含最高分</div>
      <span slot="footer" class="dialog-footer" v-show="!levelSettingData.disable">
        <el-button @click="close">取消</el-button>
        <el-button type="primary" @click="submitForm">提交</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { addLevelBatch } from "@/api/ffs/riskManageApi";
export default {
  name: "levelSetting",
  props: {
    levelSettingData: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      form: {
        params: [
          { minScore: "", maxScore: 100, levelDesc: "A", disb: [false, true] },
          { minScore: "", maxScore: "", levelDesc: "B", disb: [false, false] },
          { minScore: "", maxScore: "", levelDesc: "C", disb: [false, false] },
          { minScore: "0", maxScore: "", levelDesc: "D", disb: [true, false] },
        ],
      },
      levelVal: ["优质", "良好", "普通", "较差"],
    };
  },
  created() {
    let { list } = this.levelSettingData;

    if (list.length != 0) {
      list.reverse();
      list.forEach((element) => {
        element.disb = [false, false];
      });
      list[0].disb = [false, true];
      list[3].disb = [true, false];
      this.form.params = list;
    }
  },
  methods: {
    close() {
      this.$emit("close");
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["ruleForm"].validate((valid) => {
        if (valid) {
          let isEmpty = "";
          this.form.params.forEach((item) => {
            if (
              item.minScore.toString() == "" ||
              item.maxScore.toString() == ""
            ) {
              isEmpty = item;
            }
          });
          if (isEmpty != "") {
            this.$message({
              type: "error",
              message: isEmpty.levelDesc + "级最低分和最高分均不能为空",
            });
            return;
          }
          this.$modal.loading("提交中...");

          addLevelBatch({ riskRatingLevels: this.form.params })
            .then((response) => {
              this.$modal.closeLoading();
              if (response.code == 200) {
                this.$modal.msgSuccess("提交成功");
                this.$emit("refresh");
                this.$emit("close");
              }
            })
            .catch(() => {
              this.$modal.closeLoading();
            });
        }
      });
    },
    scoreCheck(item, index, type) {
      // if (index == 0) return;
      const params = this.form.params;

      //上一级别操作级别的最小值
      let preMinValue;

      if (index == 0) {
        preMinValue = "";
      } else {
        //上一级别操作级别的最小值
        preMinValue = params[index - 1].minScore;
      }

      //当前操作级别的最小值
      let minValue = params[index].minScore;
      //当前操作级别的最大值
      let maxValue = params[index].maxScore;

      //下一级别操作级别的最大值
      let nextMaxValue;

      if (index == 3) {
        nextMaxValue = "";
      } else {
        //上一级别操作级别的最大值
        nextMaxValue = params[index + 1].maxScore;
      }
      if (type == "min") {
        //先和自己的最大值比，如果最大值为空，跳过
        if (maxValue && minValue >= maxValue) {
          this.$message({
            type: "error",
            message:
              params[index].levelDesc +
              "级最低分不能大于" +
              params[index].levelDesc +
              "级最高分",
          });
          params[index].minScore = "";
          return;
        }
        //和自己下一级的最大值比，如果下一级最大值为空，跳过
        if (nextMaxValue && minValue < nextMaxValue) {
          this.$message({
            type: "error",
            message:
              params[index].levelDesc +
              "级最低分必须等于" +
              params[index + 1].levelDesc +
              "级最高分",
          });
          params[index].minScore = "";
          return;
        } else if (index < 3) {
          params[index + 1].maxScore = minValue;
        }
      } else if (type == "max") {
        //先和自己的最小值比，如果最小值为空，跳过
        if (minValue && minValue >= maxValue) {
          this.$message({
            type: "error",
            message:
              params[index].levelDesc +
              "级最高分不能小于" +
              params[index].levelDesc +
              "级最低分",
          });
          params[index].maxScore = "";
          return;
        }
        //和自己上一级的最小值比，如果上一级最小值为空，跳过
        if (preMinValue && preMinValue != maxValue) {
          this.$message({
            type: "error",
            message:
              params[index].levelDesc +
              "级最高分必须等于" +
              params[index - 1].levelDesc +
              "级最低分",
          });
          params[index].maxScore = "";
          return;
        } else if (index > 0) {
          params[index - 1].minScore = maxValue;
        }
      }
    },
  },
};
</script>

<style lang="scss">
.fieldList {
  .el-dialog__header {
    background-color: #f4f4f4;
  }

  .el-dialog__footer {
    text-align: center;
  }

  .selectWidth {
    width: 100%;
  }
  .woentxt {
    color: red;
    text-align: center;
    font-size: 10px;
  }
}
</style>

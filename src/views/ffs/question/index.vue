<template>
  <div class="app-container">
    <el-row :gutter="10" class="mb8">
      <div class="cur-tit">当前：{{questionData.query.title}}</div>
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
        <el-form-item label="发布状态：" prop="questionStatus">
          <el-select v-model="queryParams.questionStatus" clearable>
            <el-option label="全部" value />
            <el-option label="未发布" value="0" />
            <el-option label="已发布" value="1" />
          </el-select>
        </el-form-item>

        <el-form-item label="修改人：" prop="updateByName">
          <el-input v-model="queryParams.updateByName" placeholder="请输入修改人" clearable />
        </el-form-item>

        <el-form-item label="修改时间">
          <el-date-picker
            v-model="updateDateRange"
            style="width: 240px"
            value-format="yyyy-MM-dd"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
        </el-form-item>

        <el-form-item label="创建人：" prop="createByName">
          <el-input v-model="queryParams.createByName" placeholder="请输入创建人" clearable />
        </el-form-item>

        <el-form-item label="创建时间">
          
          <el-date-picker
            v-model="createDateRange"
            style="width: 240px"
            value-format="yyyy-MM-dd"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-row>
    <div class="menubx">
      <div class="tirbx">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="insertData"  v-hasPermi="['ffs:question:add']">新增题目</el-button>
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleLevel"  v-hasPermi="['ffs:question:level']">等级设置</el-button>
      </div>

      <div class="tisfn">
        <span class="tisfmund">总分值：{{list.length?list[0].score:0}}</span>
        <span class="tisfndisp">(总分值等于已发布项目最高分值总和)</span>
      </div>
    </div>

    <el-table v-loading="loading" :data="list" style="width: 100%">
      <el-table-column label="序号" type="index"></el-table-column fixed>
      <el-table-column label="状态" prop="questionStatus" :formatter="statusName" width="70" ></el-table-column>
      <el-table-column label="内容" prop="questionContent" height='70px' show-overflow-tooltip></el-table-column>
      <el-table-column label="修改人" prop="updateByName" width="110"  fixed="right"></el-table-column>
      <el-table-column label="修改时间" prop="updateTime" width="150"  fixed="right"></el-table-column>
      <el-table-column label="创建人" prop="createByName" width="110"  fixed="right"></el-table-column>
      <el-table-column label="创建时间" prop="createTime" width="150"  fixed="right"></el-table-column>
      <el-table-column label="操作" align="center" width="250"  fixed="right">
        <template slot-scope="scope">
          <el-button
            icon="el-icon-edit"
            size="mini"
            type="text"
            @click="handleEdit(scope.row)"
             v-if="scope.row.questionStatus==0"
             v-hasPermi="['ffs:question:edit']"
          >编辑</el-button>
          <el-button
            icon="el-icon-delete"
            size="mini"
            type="text"
            v-if="scope.row.questionStatus==0"
            @click="handleDelete(scope.row.riskQuestionId)"
             v-hasPermi="['ffs:question:delete']"
          >删除</el-button>

          <el-button
            icon="el-icon-upload2"
            size="mini"
            type="text"
            v-if="scope.row.questionStatus==0"
            v-hasPermi="['ffs:question:edit']"
            @click="handleUpdateStatus(scope.row,'1','发布')"
          >发布</el-button>

          <el-button
            icon="el-icon-download"
            size="mini"
            type="text"
            v-if="scope.row.questionStatus==1"
            v-hasPermi="['ffs:question:edit']"
            @click="handleUpdateStatus(scope.row,'0','取消发布')"
          >取消发布</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <add-question :questionData="questionData" @close="close" v-if="questionData.open" @refresh="refresh" />
    <level-setting
      :levelSettingData="levelSettingData"
      @close="close"
      v-if="levelSettingData.open"
      @refresh="refresh"
    ></level-setting>
  </div>
</template>

<script>
import {getQuestionList, getLevel,updateQuestion,delQuestion} from "@/api/ffs/riskManageApi";
import addQuestion from "./components/addQuestion";
import levelSetting from "./components/levelSetting";
import { getDicts } from "@/api/system/dict/data.js";
export default {
  components: {
    addQuestion,
    levelSetting,
  },
  data() {
    return {
      questionData: {
        open: false,
        id: "",
        query:{}
      },
      levelSettingData: {
        open: false,
        title: "等级设置",
      },
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 表格数据
      list: [],
      // 查询参数
      queryParams: {
          pageNum: 1,
          pageSize: 10,
          questionStatus:"",//题目状态：0未发布1已发布",
          updateByName:"",//修改人
          createByName:"",//创建人
          createStart:"",
          createEnd:"",
          updateStart:"",
          updateEnd:"",
          riskQuestionTypeId:""
      },
      updateDateRange: [],
      createDateRange: [],
    };
  },
  async created() {
    const {superviseType} =this.$route.query;
    if(!superviseType){
      this.$message({
          type: "error",
          message: "进入该页面必须携带参数superviseType",
        });
        return
    };
  
    this.queryParams.riskQuestionTypeId=superviseType;
     //请求审核状态数据字典数据
      let superviseTypeDic = await getDicts('ffs_supervise_type');
      if (superviseTypeDic) {
          let result = superviseTypeDic.data || [];
          for(const it of result) {
            if(it.dictValue==superviseType){
              this.questionData.query={
                superviseType,
                title:it.dictLabel
              }
            }
          }
      }
    this.getList();
  },
  computed: {
    statusName() {
      return (row, com, val) => {
        if (val == 1) {
          return "已发布";
        } else if (val == 0) {
          return "未发布";
        } 
      };
    },
  },
  methods: {
    //刷新页面
    refresh() {
      this.getList();
    },

    /** 查询列表 */
    getList() {
      this.loading = true;
      getQuestionList(this.queryParams).then((res) => {
        this.loading = false;
        if (res.code == 200) {
          this.list = res.result.list || [];
          this.total = Number(res.result.total || 0);
        } else {
          this.$message.error(res.message);
        }
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.pageNum = 1;

      if (this.createDateRange&&this.createDateRange.length!=0) {
        this.queryParams.createStart = this.createDateRange[0] + " 00:00:00";
        this.queryParams.createEnd = this.createDateRange[1] + " 23:59:59";
      } else {
        this.queryParams.createStart = "";
        this.queryParams.createEnd = "";
      }

      
      if (this.updateDateRange&&this.updateDateRange.length!=0) {
        this.queryParams.updateStart = this.updateDateRange[0]+ " 00:00:00";
        this.queryParams.updateEnd = this.updateDateRange[1] + " 23:59:59";
      } else {
        this.queryParams.updateStart = "";
        this.queryParams.updateEnd = "";
      }

      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.updateDateRange = [];
      this.createDateRange= [];
      this.resetForm("queryForm");
      this.handleQuery();
    },

    //关闭弹框
    close() {
      this.questionData.open = false;
      this.levelSettingData.open = false;
    },

    //新增题目
    insertData() {
      this.questionData.info = '';
      this.questionData.open = true;
      this.questionData.title = "新增题目";
    },
    //等级设置
    handleLevel() {
      getLevel({}).then(res=>{
        this.levelSettingData.list = res.result.list;
        this.levelSettingData.open = true;
      })
     
    },

    //打开编辑
    handleEdit(row) {
      this.questionData.info = row;
      this.questionData.open = true;
      this.questionData.title = "编辑题目"; 
    },
    //编辑状态
    handleUpdateStatus(row,questionStatus,handleLable){
        this.$modal.loading("处理中...");
        const params=JSON.parse(JSON.stringify(row));
        params.questionStatus=questionStatus;
       updateQuestion(params).then((res) => {
        this.$modal.closeLoading();
            if (res.code == 200) {
              this.refresh();
              this.$message({
                type: "success",
                message: handleLable+"成功!",
              });
            }
          }).catch(() =>  this.$modal.closeLoading())
    },

    //删除
    handleDelete(riskQuestionId) {
      this.$confirm("确定删除吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          delQuestion({ id: riskQuestionId }).then((res) => {
            if (res.code == 200) {
              this.refresh();
              this.$message({
                type: "success",
                message: "删除成功!",
              });
            }
          })
        })
        .catch(() => {});
    },
  },
};
</script>
<style scoped>
.menubx {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}
.tisfn {
  margin-left: 10px;
  display: flex;
  align-items: center;
}
.tisfndisp {
  font-size: 12px;
  color: #999;
  margin-left: 6px;
}
.tisfmund {
  font-size: 16px;
  color: #666;
}
.cur-tit{
  margin-bottom: 10px;
  color: #666;
}
</style>


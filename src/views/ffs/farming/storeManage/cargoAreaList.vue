<template>
  <div class="app-container">
    <el-card style="padding: 10px 0" shadow="never">
      <div class="clearfix">
        <el-button
          type="success"
          size="mini"
          icon="el-icon-arrow-left"
          @click="goback"
          >返回</el-button
        >
        <div class="header">
          <div class="header-title">
            <span style="margin-right: 30px">{{ title }}</span>
            <span>货区总数量：{{ total }}</span>
          </div>
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="addInfo"
            >新增货区</el-button
          >
        </div>
      </div>
      <el-table :data="tableData" style="width: 100%" border>
        <el-table-column type="index" label="序号" width="120" align="center" />
        <el-table-column prop="positionName" label="货区名称" align="center" />
        <el-table-column label="操作" align="center">
          <template slot-scope="scope">
            <el-button
              @click="editInfo(scope.row.positionId, scope.row)"
              type="text"
              class="btn_color_t"
              >编辑</el-button
            >
            <el-button
              type="text"
              @click="deleteInfo(scope.row.positionId)"
              class="btn_color_f"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <cargoAreaModel
      :dialogAdd="dialogAdd"
      v-if="dialogAdd.open"
      @close="close"
      @refresh="refresh"
      ref="cargoAreaModel"
    ></cargoAreaModel>
  </div>
</template>
<script>
import cargoAreaModel from "./components/cargoAreaModel.vue";
import { positionList, delPosition } from "@/api/ffs/farmingMortgage/storeManage";
export default {
  components: {
    cargoAreaModel,
  },

  data() {
    return {
      storehouseId: "",
      title: "",
      total: undefined,
      dialogAdd: {
        open: false,
        title: "",
        id: "",
        storehouseId: "",
      },
      tableData: [],
    };
  },
  computed: {},

  created() {
    this.storehouseId = this.$route.query.id;
    this.title = this.$route.query.title;
    this.dialogAdd.storehouseId = this.$route.query.id;
    this.getList();
  },

  methods: {
    getList() {
      positionList({ storehouseId: this.storehouseId }).then((res) => {
        if (res.code == 200) {
          this.tableData = res.result.list;
          this.total = res.result.total;
        }
      });
    },
    refresh() {
      this.getList();
    },
    //页面返回
    goback() {
      this.$tab.closeOpenPage();
      this.$router.go(-1);
    },
    addInfo() {
      this.dialogAdd.open = true;
      this.dialogAdd.title = "新增货区";
    },
    editInfo(id, row) {
      this.dialogAdd.open = true;
      this.dialogAdd.title = "编辑";
      this.dialogAdd.id = id;
      this.$nextTick(() => {
        this.$refs.cargoAreaModel.form = row;
      });
    },
    close() {
      this.dialogAdd.open = false;
      this.dialogAdd.id = "";
    },
    //删除
    deleteInfo(id) {
      this.$confirm("确定删除吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          delPosition({ ids: [id] }).then((res) => {
            if (res.code == 200) {
              this.getList();
              this.$message({
                type: "success",
                message: "删除成功!",
              });
            }
          });
        })
        .catch(() => {});
    },
  },
};
</script>
<style lang="scss" scoped>
.header {
  margin: 10px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  background: rgba(242, 242, 242, 1);
  padding: 20px 10px;
  &-title {
    font-size: 16px;
    font-weight: 700;
  }
}
</style>

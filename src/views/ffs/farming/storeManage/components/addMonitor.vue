<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="monitor.open"
      width="700px"
      :close-on-click-modal="false"
      @close="refuse"
      class="addMonitor"
      append-to-body
    >
      <el-form
        :model="form"
        :rules="rules"
        ref="ruleForm"
        label-width="120px"
        class="demo-ruleForm"
      >
        <el-form-item label="设备类型" prop="deviceType">
          <el-select
            v-model="form.deviceType"
            placeholder="请选择设备类型"
            class="inputWidth"
            :disabled="disabled"
          >
            <el-option
              v-for="dict in dict.type.hardware_video_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="萤石云账号"
          prop="ezvizId"
          v-if="form.deviceType == 3"
        >
          <el-select
            v-model="form.ezvizId"
            filterable
            placeholder="请选择萤石云账号"
            class="inputWidth"
            :disabled="disabled"
            remote
            :remote-method="remoteMethod"
            @change="selectOption"
          >
            <el-option
              v-for="item in options"
              :key="item.ezvizId"
              :label="item.userName + '/' + item.userPhone"
              :value="item.ezvizId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="设备编号" prop="deviceCode">
          <el-input
            v-model.trim="form.deviceCode"
            placeholder="请输入设备编号"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="通道编号"
          prop="channelCode"
          v-if="form.deviceType != 2"
        >
          <el-input
            v-model.trim="form.channelCode"
            placeholder="请输入通道号"
            oninput="value=value.replace(/[^\d]/g,'')"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="是否可转动"
          prop="rotateFlag"
          v-if="form.deviceType != 3"
        >
          <el-radio-group v-model="form.rotateFlag">
            <el-radio label="1">是</el-radio>
            <el-radio label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="安装地址" prop="mountLocation">
          <el-input
            v-model.trim="form.mountLocation"
            placeholder="请输入挂载地址"
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="refuse">关闭</el-button>
        <el-button type="primary" @click="submitForm">提交</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { areaData } from "@/utils/mixin/area.js";
import {
  addVideo,
  editVideo,
  cameraBusinessAdd,
  cameraBusinessEdit,
} from "@/api/ffs/farmingMortgage/storeManage";
import { getEzvizList } from "@/api/ffs/ezviz.js";
export default {
  dicts: ["hardware_video_type"],
  mixins: [areaData],
  props: {
    monitor: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      ownerFlag: 1, //1特殊账号，0普通账号
      disabled: false,
      title: "",
      options: [],
      storehouseName: "", //仓库名称
      form: {
        deviceType: "1",
        ezvizId: "",
        storehouseId: "",
        deviceCode: "",
        channelCode: undefined,
        mountLocation: "",
        rotateFlag: "",
      },
      rules: {
        deviceCode: [
          {
            required: true,
            message: "请填写摄像头的编号",
            trigger: "blur",
          },
        ],
        ezvizId: [
          {
            required: true,
            message: "请选择萤石云账号",
            trigger: "blur",
          },
        ],
        channelCode: [
          {
            required: true,
            message: "请填写摄像头对应的通道编号",
            trigger: "blur",
          },
        ],
        mountLocation: [
          {
            required: true,
            message: "请填写摄像头挂载地址",
            trigger: "blur",
          },
        ],
        rotateFlag: [
          {
            required: true,
            message: "请填写选择摄像头是否可转动",
            trigger: "change",
          },
        ],
      },
    };
  },
  created() {
    if (JSON.stringify(this.monitor.objItem) != "{}") {
      this.form = this.monitor.objItem;
      this.form.deviceType = this.form.deviceType
        ? this.form.deviceType.toString()
        : this.form.deviceType;
    }
    if (this.form.deviceType == 3 && this.form.ezvizId) {
      this.getInfo();
    }
  },
  methods: {
    //获取萤石云的列表
    getInfo() {
      getEzvizList({ ezvizId: this.form.ezvizId }).then((res) => {
        this.options = res.result.list;
      });
    },
    remoteMethod(value) {
      getEzvizList({ userName: value, pageNum: 1, pageSize: 10 }).then(
        (res) => {
          this.options = res.result.list;
        }
      );
    },
    selectOption(value) {
      this.options.map((item) => {
        if (value == item.ezvizId) {
          this.ownerFlag = item.ownerFlag;
        }
      });
    },
    submitForm() {
      console.log(this.form);
      this.$refs["ruleForm"].validate(async (valid) => {
        if (!valid) {
          return;
        }
        if (this.form.hasOwnProperty("videoId")) {
          this.edit();
        } else {
          if (this.form.deviceType == 3 && this.ownerFlag == 1) {
            let obj = {
              businessType: 2,
              businessId: this.form.storehouseId,
              deviceSerial: this.form.deviceCode,
              channelNo: this.form.channelCode,
              mountLocation: this.form.mountLocation,
              ezvizId: this.form.ezvizId,
              businessName: this.storehouseName,
              rotateFlag: this.form.rotateFlag,
            };
            cameraBusinessAdd(obj).then((res) => {
              if (res.code == 200) {
                this.$message({
                  type: "success",
                  message: "添加成功",
                });
                this.$emit("refresh");
                this.refuse();
              }
            });
          } else {
            addVideo(this.form).then((res) => {
              if (res.code == 200) {
                this.$message({
                  type: "success",
                  message: "添加成功",
                });
                this.$emit("refresh");
                this.refuse();
              }
            });
          }
        }
      });
    },
    edit() {
      if (this.form.deviceType == 3 && this.ownerFlag == 1) {
        let obj = {
          businessType: 2,
          businessId: this.$route.query.storehouseId,
          deviceSerial: this.form.deviceCode,
          channelNo: this.form.channelCode,
          mountLocation: this.form.mountLocation,
          ezvizId: this.form.ezvizId,
          cameraBusinessId: this.form.cameraBusinessId,
          businessName: this.storehouseName,
          rotateFlag: this.form.rotateFlag,
        };
        cameraBusinessEdit(obj).then((res) => {
          if (res.code == 200) {
            this.$message({
              type: "success",
              message: "编辑成功",
            });
            this.$emit("refresh");
            this.refuse();
          }
        });
      } else {
        editVideo(this.form).then((res) => {
          if (res.code == 200) {
            this.$message({
              type: "success",
              message: "编辑成功",
            });
            this.$emit("refresh");
            this.refuse();
          }
        });
      }
    },
    refuse() {
      this.$emit("close");
    },
  },
};
</script>

<style lang="scss">
.addMonitor {
  .el-dialog__header {
    background-color: #f4f4f4;
  }
  //   .el-dialog__body {
  //     padding-top: 10px;
  //   }
  //   .el-input__inner {
  //     border: none;
  //   }
  .el-dialog__footer {
    text-align: center;
  }

  .selectWidth {
    width: 100%;
  }
  &-info {
    background: #f4f4f4;
    width: 540px;
    margin: 0 0 22px 120px;
    border-radius: 8px;
    padding: 10px 14px;
    &-row {
      padding: 5px 0;
    }
  }
}

.inputWidth {
  width: 100%;
}
</style>

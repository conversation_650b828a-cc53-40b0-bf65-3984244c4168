<template>
  <div class="app-container">
    <el-card class="mb10 form_box" shadow="never" ref="formBox">
      <div slot="header" class="clearfix">
        <el-button
          type="success"
          size="mini"
          icon="el-icon-arrow-left"
          @click="goback"
          >返回</el-button
        >
      </div>
      <el-row :gutter="10" class="mb8">
        <el-form
          :model="queryParams"
          ref="queryForm"
          size="small"
          :inline="true"
        >
          <el-form-item label="设备编号" prop="equipmentCode">
            <el-input
              v-model="queryParams.equipmentCode"
              placeholder="请输入设备编号"
              clearable
            />
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleQuery"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
              >重置</el-button
            >
          </el-form-item>
        </el-form>
      </el-row>
    </el-card>
    <el-card shadow="never" class="list_table">
      <el-row class="mb8 form_btn">
        <el-col class="form_btn_col">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="addFrom"
            v-hasPermi="['ffs:video:add']"
            >新增</el-button
          >
        </el-col>
      </el-row>
      <!-- 表格数据 -->
      <el-table
        :data="tableData"
        stripe
        style="width: 100%"
        v-loading="loading"
        :height="tableHeight"
        border
      >
        <el-table-column type="index" label="序号" width="50"></el-table-column>
        <el-table-column prop="equipmentCode" label="设备编号" align="center" />
        <el-table-column prop="temperature" label="温度" />
        <el-table-column prop="humidity" label="湿度"></el-table-column>
        <el-table-column label="操作" fixed="right" width="140" align="center">
          <template slot-scope="scope">
            <el-button
              class="btn_color_t"
              @click="handelEdit(scope.row)"
              icon="el-icon-edit"
              size="mini"
              type="text"
              v-hasPermi="['ffs:video:edit']"
              >编辑</el-button
            >
            <el-button
              class="btn_color_f"
              @click="handeldel(scope.row)"
              icon="el-icon-delete"
              size="mini"
              type="text"
              v-hasPermi="['ffs:video:delete']"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>
    <addSensor
      v-if="dialogAdd.open"
      :dialogAdd="dialogAdd"
      @close="close"
      @refresh="refreshList"
      ref="addSensor"
    ></addSensor>
  </div>
</template>
<script>
import {
  equipmentList,
  equipmentEdit,
} from "@/api/ffs/farmingMortgage/storeManage";
import addSensor from "./components/addSensor.vue";
import { tableUi } from "@/utils/mixin/tableUi.js";
export default {
  mixins: [tableUi],
  components: {
    addSensor,
  },
  data() {
    return {
      dialogAdd: {
        open: false,
        title: "",
      },
      queryParams: {
        equipmentCode: "",
        businessType: "2",
        sourceId: "", //仓库id
        pageNum: 1,
        pageSize: 10,
      },
      loading: true,
      total: 0,
      tableData: [],
    };
  },
  computed: {},
  created() {
    this.queryParams.sourceId = this.$route.query.storehouseId;
    this.getList();
  },

  methods: {
    //列表查询
    getList() {
      equipmentList(this.queryParams).then((res) => {
        if (res.code == 200) {
          this.tableData = res.result.list;
          this.total = Number(res.result.total);
          this.loading = false;
        }
      });
    },

    //页面返回
    goback() {
      this.$tab.closeOpenPage();
      this.$router.go(-1);
    },
    reset() {
      this.resetForm("queryForm");
    },
    close() {
      this.dialogAdd.open = false;
      this.dialogAdd.id = "";
    },
    //重置
    resetQuery() {
      this.reset();
      this.handleQuery();
    },
    //刷新页面
    refreshList() {
      this.getList();
    },
    //搜索
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    addFrom() {
      this.dialogAdd.open = true;
      this.dialogAdd.title = "新增";
    },
    //编辑数据
    handelEdit(row) {
      this.dialogAdd.open = true;
      this.dialogAdd.title = "编辑";
      this.$nextTick(() => {
        console.log(row.superviseEquipmentId);
        this.$refs.addSensor.form.superviseEquipmentId =
          row.superviseEquipmentId;
        this.$refs.addSensor.form.equipmentType = row.equipmentType;
        this.$refs.addSensor.form.equipmentCode = row.equipmentCode;
      });
    },

    //删除
    handeldel(row) {
      row.delFlag = 1;
      this.$confirm("此操作将永久删除, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        equipmentEdit(row).then((res) => {
          if (res.code == 200) {
            this.$message({
              type: "success",
              message: "删除成功!",
            });
            this.getList();
          }
        });
      });
    },
  },
};
</script>

<style lang="scss" scoped></style>

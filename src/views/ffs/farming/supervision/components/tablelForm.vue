<template>
  <div>
    <el-dialog
      :title="tablelFormData.title"
      :visible.sync="tablelFormData.open"
      width="1300px"
      :close-on-click-modal="false"
      @close="close"
      class="fieldList"
    >
      <div class="head-title">
        请在以下已
        <span>“同意监管”</span>的意向单中选择创建，若被监管方无"同意监管"的意向单，请先完成监管意向申请。
        （
        <span>监管意向申请方法：</span>银行人员或运营人员登录融安保APP，底部导航点击“监管”，选择监管前，点击“监管申请”填写信息后，按流程处理即可。）
      </div>

      <el-row :gutter="10" class="mb8">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
          <el-form-item label="被监管方：" prop="subjectName">
            <el-input v-model="queryParams.subjectName" placeholder="请输入被监管方名称" clearable />
          </el-form-item>

          <el-form-item label="被监管方联系电话：" prop="subjectPhone">
            <el-input
              v-model="queryParams.subjectPhone"
              placeholder="请输入被监管方联系电话"
              type="number"
              oninput="if(value.length>11)value=value.slice(0,11)"
              clearable
            />
          </el-form-item>

          <el-form-item label="委托方：" prop="bankName">
            <el-input v-model="queryParams.bankName" placeholder="请输入委托方名称" clearable />
          </el-form-item>

          <el-form-item label="客户经理：" prop="loanOfficerName">
            <el-input v-model="queryParams.loanOfficerName" placeholder="请输入客户经理姓名" clearable />
          </el-form-item>

          <el-form-item label="客户经理联系电话：" prop="loanOfficerPhone">
            <el-input
              v-model="queryParams.loanOfficerPhone"
              placeholder="请输入客户经理联系电话"
              type="number"
              oninput="if(value.length>11)value=value.slice(0,11)"
              clearable
            />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </el-row>

      <el-table v-loading="loading" :data="tableData" border>
        <el-table-column label="序号" type="index"></el-table-column>
        <el-table-column label="监管申请单号" prop="intentionListId" width="175px"></el-table-column>
        <el-table-column label="监管类型" prop="superviseType" :formatter="superviseTypeName"></el-table-column>
        <el-table-column label="被监管方" prop="subjectName"></el-table-column>
        <el-table-column label="被监管方电话" prop="subjectPhone" width="110px"></el-table-column>
        <el-table-column label="委托方" prop="bankName" width="150px"></el-table-column>
        <el-table-column label="客户经理" prop="loanOfficerName"></el-table-column>
        <!--        <el-table-column label="信贷员电话" prop="loanOfficerPhone" width="110px"></el-table-column>-->
        <el-table-column label="监管状态" prop="superviseStatus" :formatter="superviseStatusName"></el-table-column>
        <el-table-column label="创建时间" prop="createTime" width="155px"></el-table-column>
        <el-table-column label="操作" align="center">
          <template slot-scope="scope">
            <el-button
              v-if="scope.row.superviseStatus==1"
              v-hasPermi="['ffs:farming:supervision:add']"
              size="mini"
              type="text"
              icon="el-icon-circle-plus"
              @click="handleAddd(scope.row)"
            >创建监管单</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />

      <span slot="footer" class="dialog-footer" v-show="!tablelFormData.disable">
        <el-button @click="close">取消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { investigationList } from "@/api/ffs/farmingSupervisionSheet/livingSupervisionApi";
import { searchUser } from "@/api/system/user.js"; // 查询搜索用户  可以手机号、昵称等等信息搜索
export default {
  name: "tablelFormAddModelForm",
  props: {
    tablelFormData: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      queryParams: {
        flag:'1',
        fileName: "",
        superviseType: "5",
        superviseStatus: "1,2",
        pageNum: 1,
        subjectName: "",
        subjectPhone: "",
        loanOfficerPhone: "",
        loanOfficerName: "",
        bankName: "",
        status: "6", //同意监管
      },

      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 表格数据
      tableData: [],
    };
  },
  created() {
    this.getList();
  },
  computed: {
    superviseTypeName() {
      return (row, com, val) => {
        if (val == 1) return "活体监管";
        if (val == 2) return "仓单监管";
      };
    },
    superviseStatusName() {
      return (row, com, val) => {
        if (val == 1) {
          return "待监管";
        } else if (val == 2) {
          return "维护中";
        } else if (val == 3) {
          return "监管中";
        } else if (val == 4) {
          return "监管完";
        }
      };
    },
  },
  methods: {
    /** 查询列表 意向单列表 */
    getList() {
      this.loading = true;
      investigationList(this.queryParams).then((res) => {
        this.loading = false;
        if (res.code == 200) {
          this.tableData = res.result.list;
          this.total = Number(res.result.total);
          this.loading = false;
        } else {
          this.$message.error(res.message);
        }
      });
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },

    //关闭弹框
    close() {
      this.$emit("close");
    },
    /** 打开创建监管单弹窗 */
    async handleAddd(data) {
      let subjectUser = await this.hasReallName(
        data.subjectPhone || data.applyPhone
      );

      const params = {
        bankId: data.bankId,
        creditInvestigationId: data.creditInvestigationId,
        intentionListId: data.intentionListId,
        bankName: data.bankName,
        regulatoryScheme: data.regulatoryScheme,
        loanOfficerName: data.loanOfficerName,
        loanOfficerPhone: data.loanOfficerPhone,
        subjectType: data.subjectType, //被监管方类型：1企业，2个人
        applyPhone: data.subjectPhone || data.applyPhone, //被监管方手机号
        applyName: data.subjectName || data.applyName, //被监管方名称
        operation: "add",
      };
      params.subjectUserId = subjectUser ? subjectUser.userId : "";
      this.$emit("addSupervision", params);
    },

    hasReallName(subjectPhone) {
      return new Promise((resolve, reject) => {
        searchUser({ phonenumber: subjectPhone }).then((res) => {
          if (res.code != 200) {
            resolve(false);
            return;
          }
          if (!res.result || res.result.length != 1) {
            resolve(false);
            return;
          }
          resolve(res.result[0] || {});
        });
      });
    },
  },
};
</script>

<style lang="scss">
.fieldList {
  .el-dialog__header {
    background-color: #f4f4f4;
  }

  .el-dialog__footer {
    text-align: center;
  }

  .selectWidth {
    width: 100%;
  }
}
.head-title {
  font-size: 16px;
  color: #999;
  margin-bottom: 20px;
}
.head-title span {
  color: red;
}
</style>

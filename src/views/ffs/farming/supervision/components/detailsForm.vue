<!--  -->
<template>
  <el-dialog
    :title="detailsFormData.title"
    :visible.sync="detailsFormData.open"
    width="1200px"
    :close-on-click-modal="false"
    @close="close"
    class="fieldList"
  >
    <div class="setmain">
      <el-descriptions title="被监管方" :column="3">
        <el-descriptions-item label="姓名">黄昊</el-descriptions-item>
        <el-descriptions-item label="联系电话">18100000000</el-descriptions-item>
        <el-descriptions-item label="身份证号">42*************16</el-descriptions-item>
        <el-descriptions-item label="家庭住址">湖北省武汉市江夏区藏龙岛杨桥湖大道</el-descriptions-item>
      </el-descriptions>
      <el-descriptions title="委托方信息" class="mtop" :column="3">
        <el-descriptions-item label="企业名称">内蒙古农商行</el-descriptions-item>
        <el-descriptions-item label="企业类型">银行</el-descriptions-item>
        <el-descriptions-item label="营业执照编号">4202201875903294416</el-descriptions-item>
        <el-descriptions-item label="地址">湖北省武汉市江夏区藏龙岛杨桥湖大道</el-descriptions-item>
        <el-descriptions-item label="法人">杨承运</el-descriptions-item>
        <el-descriptions-item label="法人电话">1788217585729</el-descriptions-item>
        <el-descriptions-item label="身份证号">13****************99</el-descriptions-item>
        <el-descriptions-item label="负责人姓名">沈东辉</el-descriptions-item>
        <el-descriptions-item label="负责人电话">1788217585729</el-descriptions-item>
        <el-descriptions-item label="详细地址">详细地址详细地址详细地址</el-descriptions-item>
      </el-descriptions>

      <el-descriptions title="服务费信息" class="mtop" :column="3">
        <el-descriptions-item label="服务费率（%）">1%</el-descriptions-item>
        <el-descriptions-item label="服务费金额（元）">10,000</el-descriptions-item>
        <el-descriptions-item label="结算方式">半年付</el-descriptions-item>
      </el-descriptions>

      <el-descriptions title="调研信息" class="mtop" :column="3">
        <el-descriptions-item label="调研信息">
          <span class="wbrdw">查看详情></span>
        </el-descriptions-item>
      </el-descriptions>

      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <div class="iputiirt nkfdj skeme">
          <span class="marrt20">监管文件</span>
          <span class="qiangti">(大小不超过100MB 格式为 doc/pdf/png/jpg的文件)</span>
        </div>
        <el-row class="filerow">
          <el-col :span="8">
            <el-form-item label="监管方案：" prop="appDoe2">
              <file-upload
                :fileType="['doc', 'pdf','png', 'jpg', 'jpeg']"
                :fileSize="100"
                :limit="1"
                v-model="form.appDoe2"
                :isShowTip="false"
              >
                <el-button type="primary" plain size="mini" class="lobtb">
                  <i class="el-icon-upload el-icon--right"></i>
                  上传监管方案
                </el-button>
              </file-upload>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="三方协议：" prop="appDoenloadUrl">
              <file-upload
                :fileType="['doc', 'pdf','png', 'jpg', 'jpeg']"
                :fileSize="100"
                :limit="1"
                v-model="form.appDoenloadUrl"
                :isShowTip="false"
              >
                <el-button type="primary" plain size="mini" class="lobtb">
                  <i class="el-icon-upload el-icon--right"></i>
                  上传三方协议
                </el-button>
              </file-upload>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="监管合同：" prop="appDoe2">
              <file-upload
                :fileType="['doc', 'pdf','png', 'jpg', 'jpeg']"
                :fileSize="100"
                :limit="1"
                v-model="form.appDoe2"
                :isShowTip="false"
              >
                <el-button type="primary" plain size="mini" class="lobtb">
                  <i class="el-icon-upload el-icon--right"></i>
                  上传监管合同
                </el-button>
              </file-upload>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row class="filerow">
          <el-col :span="8">
            <el-form-item label="防疫记录本：" prop="appDoe2">
              <file-upload
                :fileType="['doc', 'pdf','png', 'jpg', 'jpeg']"
                :fileSize="100"
                :limit="1"
                v-model="form.appDoe2"
                :isShowTip="false"
              >
                <el-button type="primary" plain size="mini" class="lobtb">
                  <i class="el-icon-upload el-icon--right"></i>
                  上传防疫记录本
                </el-button>
              </file-upload>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="保险单：" prop="appDoe2">
              <file-upload
                :fileType="['doc', 'pdf','png', 'jpg', 'jpeg']"
                :fileSize="100"
                :limit="1"
                v-model="form.appDoe2"
                :isShowTip="false"
              >
                <el-button type="primary" plain size="mini" class="lobtb">
                  <i class="el-icon-upload el-icon--right"></i>
                  上传保险单
                </el-button>
              </file-upload>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="嘎查证明：" prop="appDoe2">
              <file-upload
                :fileType="['doc', 'pdf','png', 'jpg', 'jpeg']"
                :fileSize="100"
                :limit="1"
                v-model="form.appDoe2"
                :isShowTip="false"
              >
                <el-button type="primary" plain size="mini" class="lobtb">
                  <i class="el-icon-upload el-icon--right"></i>
                  上传嘎查证明
                </el-button>
              </file-upload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <span slot="footer" class="dialog-footer">
      <el-button type="info" @click="close">取消</el-button>
      <el-button type="primary" @click="submitForm">提交</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { areaData } from "@/utils/mixin/area.js";
export default {
  mixins: [areaData],
  name: "detailsForm",
  props: {
    detailsFormData: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      form: {
        placeName: "",
        yeassd: "",
        dakdata: "",
        datefw: "",
        rangeDate: "",
        metho: "",
        appDoenloadUrl: "",
        appDoe2: "",
        city: "",
        latitude: "113.60656",
        longitude: "37.8534",
        detailedAddress: "",
        addressName: {
          name: "",
          nameArr: [],
          point: {
            lat: "",
            lng: "",
          },
        },
        sourceaArr: "",
        detailedAddress: "",
      },
      // 表单校验
      rules: {
        placeName: [
          { required: true, message: "请填写服务站", trigger: "blur" },
        ],
        yeassd: [
          { required: true, message: "请选择监管时间", trigger: "blur" },
        ],
        metho: [{ required: true, message: "请选择结算方式", trigger: "blur" }],
        appDoenloadUrl: [
          { required: true, message: "请上传三方协议", trigger: "blur" },
        ],
      },
    };
  },
  created() {},
  mounted() {},
  methods: {
    close() {
      this.$emit("close");
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["ruleForm"].validate((valid) => {
        if (valid) {
          this.$modal.loading("正在计算中，数据量较大，请耐心等待...");
          this.form.datetime = this.form.datetime + "-01";
          insertBankReport(this.form)
            .then((response) => {
              this.$modal.closeLoading();
              if (response.code == 200) {
                this.$modal.msgSuccess("报表生成成功，请在列表中点击下载。");
                this.open = false;
                this.$emit("refresh");
                this.$emit("close");
              }
            })
            .catch(() => {
              this.$modal.closeLoading();
            });
        }
      });
    },

    // 获取地图选择的位置
    mapPosition(point) {
      this.form.latitude = parseFloat(point.lat).toFixed(5);
      this.form.longitude = parseFloat(point.lng).toFixed(5);
    },
    getLonLat() {
      let mapEl = this.$refs.mapBtn;
      mapEl.getLonLat();
    },
    // 选择地址
    selectAddress(val) {
      if (!val.length) {
        return;
      }

      const names = this.$refs["myCascader"].getCheckedNodes()[0].pathLabels;
      this.form.provinceId = val[0];
      this.form.cityId = val[1];
      this.form.countyId = val[2];
      this.form.provinceName = names[0];
      this.form.cityName = names[1];
      this.form.countyName = names[2];
      var loc = {
        name: names[0] + names[1] + names[2],
        nameArr: names,
      };
      this.form.addressName = loc;
    },
  },
};
</script>
<style  scoped lang="scss">
@import url("./addForm/index.scss");
</style>

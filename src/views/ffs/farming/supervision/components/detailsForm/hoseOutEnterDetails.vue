<template>
  <div>
    <el-dialog
      :title="outEnterHoseData.title"
      :visible.sync="outEnterHoseData.open"
      width="1200px"
      :close-on-click-modal="false"
      @close="close"
      append-to-body
      class="fieldList"
    >
      <el-descriptions :column="3" border>
        <el-descriptions-item :label="motionTypeName+'单号'">{{outEnterHoseData.info.motionRecordNo}}</el-descriptions-item>
        <el-descriptions-item :label="motionTypeName+'方式'">直接{{motionTypeName}}</el-descriptions-item>
        <el-descriptions-item :label="motionTypeName+'货区'">{{outEnterHoseData.info.positionName}}</el-descriptions-item>
        <el-descriptions-item label="操作人">{{outEnterHoseData.info.createName}}</el-descriptions-item>
        <el-descriptions-item :label="motionTypeName+'时间'">{{outEnterHoseData.info.createTime}}</el-descriptions-item>
      </el-descriptions>

      <div class="cttt">
        <span class="xttname">商品信息</span>
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
          <el-form-item label="商品名称：" prop="commodityName">
            <el-input v-model="queryParams.commodityName" placeholder="请输入商品名称" clearable />
          </el-form-item>

          <el-form-item :label="motionTypeName+'时间：'">
            <el-date-picker
              v-model="dateRange"
              style="width: 240px"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            ></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <el-table
        :data="tableData"
        border
        show-summary
        v-loading="loading"
        :summary-method="getSummaries"
      >
        <el-table-column label="序号" type="index" fixed width="50"></el-table-column>
        <el-table-column prop="mortgageTypeName" label="质押品类型"></el-table-column>
        <el-table-column prop="commodityCategoryName" label="商品品类"></el-table-column>
        <el-table-column prop="processWayName" label="加工方式"></el-table-column>
        <el-table-column prop="commodityTypeName" label="商品类型"></el-table-column>
        <el-table-column prop="commodityNo" label="商品编码"></el-table-column>
        <el-table-column prop="commodityName" label="商品名称" width="160px"></el-table-column>
        <el-table-column
          prop="commoditySpecifications"
          label="商品规格"
          :formatter="getCommoditySpecificationsName"
          width="120px"
        ></el-table-column>

        <el-table-column prop="num" sortable :label="motionTypeName+'数量'"></el-table-column>
        <el-table-column prop="totalWeight" sortable :label="motionTypeName+'重量（千克）'"></el-table-column>
        <el-table-column prop="totalPrice" sortable :label="motionTypeName+'货值（元）'"></el-table-column>
        <el-table-column prop="createName" label="操作人"></el-table-column>
        <el-table-column prop="createTime" :label="motionTypeName+'时间'" width="160px"></el-table-column>
      </el-table>

      <span slot="footer" class="dialog-footer">
        <el-button type="info" @click="close">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { superviseDetailPage } from "@/api/ffs/farmingMortgage/supervision";
import {commoditySpecificationsName} from "./utils.js"
export default {
  name: "hoseOutEnterDetails",
  props: {
    outEnterHoseData: {
      type: Object,
      default: () => ({
        title: "",
        open: false,
        info: {},
      }),
    },
  },
  data() {
    return {
      loading: false,

      queryParams: {
        motionType: "", //motionType   1 入库 2 出库
        commodityName: "",
        motionRecordId: "",
        startTime: "",
        endTime: "",
        pageNum: 1,
        pageSize: 999999,
      },
      dateRange: [],

      // 遮罩层
      loading: true,
      // 总条数
      total: 3,
      // 表格数据
      tableData: [],
    };
  },
  created() {
    this.queryParams.motionRecordId = this.outEnterHoseData.info.motionRecordId;
    this.queryParams.motionType = this.outEnterHoseData.motionType;
    this.getList();
  },
  computed: {
     getCommoditySpecificationsName() {
      return (row, com, val) => {
        return commoditySpecificationsName(row)
      };
    },

    motionTypeName() {
      return this.outEnterHoseData.motionType == 1 ? "入库" : "出库";
    },
  },
  methods: {
    //列表查询
    getList() {
      superviseDetailPage(this.queryParams).then((res) => {
        if (res.code == 200) {
          this.tableData = res.result.list;
          this.total = Number(res.result.total);
          this.loading = false;
        }
      });
    },
    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = "合计";
          return;
        }

        if (index === 5 || index === 7 || index === 11) {
          return "";
        }

        const values = data.map((item) => Number(item[column.property]));
        if (!values.every((value) => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!isNaN(value)) {
              return prev + curr;
            } else {
              return prev;
            }
          }, 0);
          sums[index] += "";
        } else {
          sums[index] = "";
        }
      });
      return sums;
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.pageNum = 1;
      if (this.dateRange.length != 0) {
        this.queryParams.startTime = this.dateRange[0] + " 00:00:00";
        this.queryParams.endTime = this.dateRange[1] + " 23:59:59";
      } else {
        this.queryParams.startTime = "";
        this.queryParams.endTime = "";
      }
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.queryParams.startTime = "";
      this.queryParams.endTime = "";
      this.handleQuery();
    },
    close() {
      this.$emit("close");
    },
  },
};
</script>

<style lang="scss">
.cttt {
  display: flex;
  padding-top: 50px;
}
.xttname {
  margin-right: 30px;
  font-size: 16px;
  color: #333;
  margin-top: 5px;
  font-weight: bold;
}
</style>



<template>
  <div>
    <!-- :summary-method="getSummaries" -->
    <el-table v-loading="loading" :data="list"   border  >
      <el-table-column label="序号" type="index" fixed  width="50px"></el-table-column align="center" >
      <el-table-column label="质押货值(万)" prop="mortgageTotalAmount" width="110px" align="center"></el-table-column>
      <el-table-column label="每天服务费金额(元)" prop="dayServiceFee" align="center" width="150px"></el-table-column>
      <el-table-column label="监管天数" prop="supervisionDays"  width="85px" align="center"></el-table-column>
      <el-table-column label="预计收入(元)" prop="estimatedRevenue"  width="110px" align="center"></el-table-column>
      <el-table-column label="服务费率(%)" prop="superviseServiceRate"  width="100px" align="center"></el-table-column>
      <el-table-column label="开始时间" prop="effectTime"  width="100px" align="center"></el-table-column>
      <el-table-column label="结束时间" prop="closeTime"   width="100px" align="center"></el-table-column>
      <el-table-column label="操作人" prop="operatorName" align="center"></el-table-column>
      <el-table-column label="操作时间" prop="updateTime"  width="155px" align="center"></el-table-column>
    </el-table>
  </div>
</template>

<script>
import { bySupervisionFeeList } from "@/api/ffs/farmingSupervisionSheet/livingSupervisionApi";
export default {
  name: "superviseServiceTable",
  props: {
    info: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      queryParams: {
        operationType: "1",
        pageNum: 1,
        pageSize: 99999,
      },

      inspectionInfomData: {
        open: false,
        id: "",
      },

      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 表格数据
      list: [],
    };
  },
  created() {},
  watch:{
        info:{
           immediate: true,
           handler:function(val) {
              if (val) {
                this.queryParams.superviseId = val.superviseId;
                this.getList();
              }
           }
        },
  },


  computed: {

  },

  methods: {
    /** 查询列表 */
    getList() {
      this.loading = false;
      bySupervisionFeeList(this.queryParams).then((res) => {
        this.loading = false;
        if (res.code == 200) {
          console.log('服务费信息：',res);

          this.list = res.result.list || [];
          this.total = Number(res.result.total || 0);
        } else {
          this.$message.error(res.message);
        }
      });
    },

     getSummaries(param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = "合计";
          return;
        }

        if (index === 5 || index === 7 || index === 11) {
          return "";
        }

        const values = data.map((item) => Number(item[column.property]));
        if (!values.every((value) => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!isNaN(value)) {
              return prev + curr;
            } else {
              return prev;
            }
          }, 0);
          sums[index] += "";
        } else {
          sums[index] = "";
        }
      });
      return sums;
    },
  },
};
</script>

<style lang="scss">
.fieldList {
  .el-dialog__header {
    background-color: #f4f4f4;
  }

  .el-dialog__footer {
    text-align: center;
  }

  .selectWidth {
    width: 100%;
  }
}
</style>

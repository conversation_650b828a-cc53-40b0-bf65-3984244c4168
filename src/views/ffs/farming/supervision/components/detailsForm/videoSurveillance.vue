<!--  -->
<template>
  <div class="main">
    <monitor :headBar="false" :storehouseId="storehouseId" v-if="storehouseId" />
  </div>
</template>

<script>
import monitor from "@/views/ffs/mortgage/storeManage/monitorList.vue";
export default {
  name: "videoSurveillance",
  props: {
    info: {
      type: Object,
      default: () => {},
    },
  },
  components: {
    monitor,
  },
  data() {
    return {
      storehouseId: "",
    };
  },
  watch: {},
  created() {
    const ids = [];
    const storehouseList = this.info.storehouseList || [];
    storehouseList.forEach((element) => {
      ids.push(element.storehouseId);
    });
    if (storehouseList.length) {
      this.storehouseId = storehouseList[0].storehouseId;
    }
  },
  mounted() {},
};
</script>
<style lang="scss" scoped>
/* @import url(); 引入css类 */
</style>
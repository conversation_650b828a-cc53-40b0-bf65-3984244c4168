<!--  -->
<template>
    <div class="main">
      <el-table :data="tableData" style="width: 100%" border>
          <el-table-column type="index" label="序号" width="50px" align="center"></el-table-column>
        <el-table-column prop="date" label="续单原因" width="100" align="center">
            <template slot-scope="scope">{{handelType(scope.row.operateType)}}</template>
        </el-table-column>
        <el-table-column prop="mortgageTotalAmount" label="质押货值(万元)" width="120" align="right">
          <template slot-scope="scope">{{ scope.row.mortgageTotalAmount/1000000 }}</template>
        </el-table-column>
        <el-table-column prop="effectTime" label="起始时间" align="center" min-width="70"></el-table-column>
        <el-table-column prop="closeTime" label="结束时间" align="center" min-width="70"></el-table-column>
        <el-table-column prop="mark" label="备注" min-width="100" show-overflow-tooltip></el-table-column>
        <el-table-column prop="createUserName" label="操作人" width="110" show-overflow-tooltip></el-table-column>
        <el-table-column prop="createTime" label="操作时间" align="center" width="160"></el-table-column>
        <el-table-column  label="操作" align="center" width="100">
            <template slot-scope="scope">
                <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-eleme"
                    v-show="handelFile(scope.row)"
                    @click="clickView(scope.row)"
                >查看附件</el-button>
            </template>
        </el-table-column>
      </el-table>
      <fileView ref="fileView"></fileView>
    </div>
  </template>
  <script>
  import { queryDelayRecord } from "@/api/ffs/farmingSupervisionSheet/livingSupervisionApi";
  import fileView from  "@/views/ffs/supervisionSheet/livingSupervision/components/detailsForm/fileView.vue"
  export default {
    components:{
    fileView
  },
    props: {
      superviseId: {
        type: String,
        default: "",
      },
    },
    data() {
      return {
        list:[
        {name:'展期',value:1},
        {name:'续贷',value:2},
        {name:'续约',value:3},
      ],
        tableData: [],
      };
    },
    created() {
      this.getList();
    },
    computed:{
    handelType(){
        return (value)=>{
            let name=''
            this.list.map(item=>{
                if(value==item.value){
                    name=item.name
                }

            })
            return name
        }
    },
    handelFile(){
        return (row)=>{
            if(row.livestockFileIntentionList||row.livestockFileCreditInvestigation||row.livestockFileRegulatoryScheme||row.livestockFileTripleAgreement
            ||row.livestockFileSuperviseContract||row.livestockFileVaccineRecord||row.livestockFileInsurance||row.livestockFileGazhaTestify||row.fileLoan||row.fileLeaseContract||row.fileOther){
                return true
            }else{
                return false
            }
        }
    }
  },
    methods: {
           //查看附件
    clickView(row){
        this.$nextTick(()=>{
            this.$refs.fileView.open=true
            Object.keys(this.$refs.fileView.form).forEach(key=>{
                this.$refs.fileView.form[key]=row[key]
            })
        });
    },
      getList() {
        queryDelayRecord({superviseId:this.superviseId}).then((res) => {
          if (res.code == 200) {
            this.tableData = res.result||[];
          }
        });
      },
    },
  };
  </script>
  <style lang="scss" scoped>
  /* @import url(); 引入css类 */
  </style>

<template>
  <div>
    <el-row :gutter="10" class="mb8">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="130px">
        <el-row class="form_row">
         <el-col class="form_col">
        <el-form-item label="质押品类型：" prop="mortgageType">
          <el-select v-model="queryParams.mortgageType" clearable class="selectWidth">
            <el-option
              v-for="dict in dict.type.ffs_mortgage_type"
              :key="dict.label"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="商品品类：" prop="commodityCategory">
          <el-select v-model="queryParams.commodityCategory" clearable class="selectWidth">
            <el-option
              v-for="dict in dict.type.ffs_commodity_category"
              :key="dict.label"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="加工方式：" prop="processWay">
          <el-select v-model="queryParams.processWay" clearable class="selectWidth">
            <el-option
              v-for="dict in dict.type.ffs_machining_type"
              :key="dict.label"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="商品类型：" prop="commodityType">
          <el-select v-model="queryParams.commodityType" clearable class="selectWidth">
            <el-option
              v-for="dict in dict.type.ffs_commodity_type"
              :key="dict.label"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="货区名称：" prop="positionId">
          <el-select v-model="queryParams.positionId" clearable class="selectWidth">
            <el-option
              v-for="huqu in info.storehouseList"
              :key="huqu.positionId"
              :label="huqu.positionName"
              :value="huqu.positionId"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="商品编码：" prop="commodityNo">
          <el-input v-model="queryParams.commodityNo" placeholder="请输入商品编码" clearable @keyup.enter.native="handleQuery"/>
        </el-form-item>
        <el-form-item label="商品名称：" prop="commodityName">
          <el-input v-model="queryParams.commodityName" placeholder="请输入商品名称" clearable @keyup.enter.native="handleQuery"/>
        </el-form-item>
        </el-col>
        </el-row>
        <el-row style="margin-left: 130px;">
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
        <template >
                <el-button type="text" @click="packUp" >
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <i
                  :class="{ 'el-icon-arrow-down': !toggleSearchStatus, 'el-icon-arrow-up': toggleSearchStatus }"
                ></i>
              </el-button>
              </template>
        </el-row>
      </el-form>
    </el-row>

    <el-row type="flex">
      <el-col :span="4">商品数量：{{statistics.calcTotalCommodity||0}}</el-col>
      <el-col :span="4">库存总数量：{{statistics.calcTtotalNum||"0.00"}}</el-col>
      <el-col :span="4">库存总重量：{{statistics.calcTtotalWeight||"0.00"}}吨</el-col>
      <el-col :span="4">库存总货值：{{statistics.calcTtotalPrice||"0.00"}}万元</el-col>
    </el-row>

    <el-table :data="list" stripe style="width: 100%;margin-top:10px" v-loading="loading" border>
      <el-table-column type="index" label="序号" width="50"></el-table-column>
      <el-table-column prop="mortgageTypeName" label="质押类型"></el-table-column>
      <el-table-column prop="commodityCategoryName" label="商品品类" width="80"></el-table-column>
      <el-table-column prop="positionName" label="货区"></el-table-column>
      <el-table-column prop="processWayName" label="加工方式" width="78"></el-table-column>
      <el-table-column prop="commodityTypeName" label="商品类型"></el-table-column>
      <el-table-column prop="commodityNo" label="商品编码"></el-table-column>
      <el-table-column prop="commodityName" label="商品名称" width="120"></el-table-column>
      <el-table-column prop="commodityUnitName" label="计量单位"></el-table-column>
      <el-table-column prop="inventoryUnitName" label="库存单位"></el-table-column>
      <el-table-column
        prop="commoditySpecifications"
        label="商品规格"
        :formatter="getCommoditySpecificationsName"
        width="120"
      ></el-table-column>
      <el-table-column prop="calcNum" label="库存数量" width="50"></el-table-column>
      <el-table-column prop="calcWeight" label="库存重量（吨）"></el-table-column>
      <el-table-column prop="calcPrice" label="库存货值（万元）"></el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <goodsOutEnterHoseDetails
      :outEnterHoseData="outEnterHoseData"
      @close="close"
      v-if="outEnterHoseData.open"
    />
  </div>
</template>

<script>
import { countByParam } from "@/api/ffs/farmingMortgage/supervision";
import goodsOutEnterHoseDetails from "./goodsOutEnterHoseDetails"; //商品出入库详情
import { commoditySpecificationsName } from "./utils.js";
import { searchUi } from "@/utils/mixin/searchUi.js";
export default {
  name: "stockInfo",
  mixins: [searchUi],
  dicts: [
    "ffs_mortgage_type",
    "ffs_commodity_category",
    "ffs_machining_type",
    "ffs_commodity_type",
  ],
  components: {
    goodsOutEnterHoseDetails,
  },
  props: {
    info: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      outEnterHoseData: {
        open: false,
        id: "",
        title: "",
      },
      queryParams: {
        mortgageType: undefined,
        searchValue: undefined,
        processWay: undefined,
        commodityCategory: undefined,
        mortgageType: undefined,
        commodityType: undefined,
        commodityNo: "",
        positionId: "",
        commodityName: "",
        pageNum: 1,
        pageSize: 10,
        createStart: "",
        createEnd: "",
        countType: "3",
      },
      form: {},

      time: undefined,

      // 遮罩层
      loading: false,
      // 总条数
      total: 0,
      // 表格数据
      list: [],

      statistics: {
        calcTtotalNum: "0", //当前货区商品总数
        calcTtotalWeight: "0.00", //当前货区商品总重（吨）
        calcTtotalPrice: "0.00", //当前货区商品总货值（万）
        calcCommodity: "0", //当前货区商品库存数量
        calcTotalCommodity: "0", //商品种类数量
      },
    };
  },
  created() {
    this.queryParams.superviseId = this.info.superviseId;
    this.form = this.info;
    this.getList();
  },

  computed: {
    getCommoditySpecificationsName() {
      return (row, com, val) => {
        return commoditySpecificationsName(row);
      };
    },
  },
  methods: {
    /** 根据监管单 库存查询 */
    getList() {
      countByParam(this.queryParams).then((res) => {
        this.loading = false;
        if (res.code == 200) {
          this.list = res.result.list || [];
          this.total = Number(res.result.total || 0);
          this.statistics = res.result.params || {};
        } else {
          this.$message.error(res.message);
        }
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      if (this.time && this.time.length > 0) {
        this.queryParams.createStart = this.time[0];
        this.queryParams.createEnd = this.time[1];
      } else {
        this.queryParams.createStart = "";
        this.queryParams.createEnd = "";
      }
      this.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.time = [];
      this.queryParams.createStart = "";
      this.queryParams.createEnd = "";
      this.resetForm("queryForm");
      this.handleQuery();
    },

    //关闭弹框
    close() {
      this.outEnterHoseData.open = false;
      this.$emit("close");
    },
  },
};
</script>

<style lang="scss">
.fieldList {
  .el-dialog__header {
    background-color: #f4f4f4;
  }

  .el-dialog__footer {
    text-align: center;
  }

  .selectWidth {
    width: 100%;
  }
}
</style>

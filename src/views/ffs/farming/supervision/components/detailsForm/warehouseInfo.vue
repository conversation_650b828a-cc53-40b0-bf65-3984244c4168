<template>
  <div>
    <div >
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="120px">
        <el-row class="form_row">
         <el-col class="form_col">
        <el-form-item :label="motionTypeName+'单号：'" prop="motionRecordNo">
          <el-input v-model="queryParams.motionRecordNo" placeholder="请输入单号" clearable @keyup.enter.native="handleQuery"/>
        </el-form-item>

        <!-- <el-form-item label="入库方式：" prop="storehouseNo">
          <el-select v-model="queryParams.storehouseNo" clearable class="selectWidth">
            <el-option label="全部" value />
            <el-option label="直接入库" value="0" />
          </el-select>
        </el-form-item>-->

        <el-form-item :label="motionTypeName+'货区：'" prop="positionId">
          <el-select v-model="queryParams.positionId" clearable class="selectWidth">
            <el-option label="全部" value />
            <el-option
              v-for="item in  info.storehouseList"
              :label="item.positionName"
              :value="item.positionId"
              :key="item.positionId"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="操作人：" prop="createName">
          <el-input v-model="queryParams.createName" placeholder="请输入操作人" clearable @keyup.enter.native="handleQuery"/>
        </el-form-item>

        <el-form-item :label="motionTypeName+'时间：'">
          <el-date-picker
            v-model="dateRange"
            style="width: 215px"
            value-format="yyyy-MM-dd"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
        </el-form-item>
        </el-col>
        </el-row>
        <el-row style="margin-left: 120px;">
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          <template >
                <el-button type="text" @click="packUp" >
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <i
                  :class="{ 'el-icon-arrow-down': !toggleSearchStatus, 'el-icon-arrow-up': toggleSearchStatus }"
                ></i>
              </el-button>
              </template>
        </el-form-item>
        </el-row>
      </el-form>
    </div>

    <el-table :data="tableData" v-loading="loading" border>
      <el-table-column label="序号" type="index" fixed width="50"></el-table-column>
      <el-table-column prop="motionRecordNo" :label="motionTypeName+'单号'" width="185"></el-table-column>
      <el-table-column prop="dfdsd" :label="motionTypeName+'方式'" :formatter="housingMethod"></el-table-column>
      <el-table-column prop="positionName" :label="motionTypeName+'货区'"></el-table-column>
      <el-table-column prop="motionTotalNum" sortable :label="motionTypeName+'数量'"></el-table-column>
      <el-table-column
        prop="motionTotalWeight"
        sortable
        :label="motionTypeName+'重量(吨)'"
        width="155"
      ></el-table-column>
      <el-table-column
        prop="motionTotalPrice"
        sortable
        :label="motionTypeName+'货值(万元)'"
        width="155"
      ></el-table-column>
      <el-table-column prop="createName" label="操作人"></el-table-column>
      <el-table-column prop="createTime" :label="motionTypeName+'时间'" width="155"></el-table-column>
      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-info"
            @click="openOutEnterHoseDig(scope.row)"
          >详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <hoseOutEnterDetails
      :outEnterHoseData="outEnterHoseData"
      @close="close"
      v-if="outEnterHoseData.open"
    />
  </div>
</template>

<script>
import { outentRecordPage } from "@/api/ffs/farmingMortgage/supervision";
import hoseOutEnterDetails from "./hoseOutEnterDetails"; //出入库详情
import { searchUi } from "@/utils/mixin/searchUi.js";
export default {
  name: "enterWarehouse",
  mixins: [searchUi],
  components: {
    hoseOutEnterDetails,
  },
  props: {
    info: {
      type: Object,
      default: () => ({
        storehouseList: [],
      }),
    },
    motionType: {
      type: Number,
      default: 1,
    },
  },
  data() {
    return {
      outEnterHoseData: {
        open: false,
        id: "",
        title: "",
      },
      queryParams: {
        motionRecordNo: "", //单号
        motionType: "2", //1入库，2出库
        positionId: "", //货区名称
        createName: "", //操作人
        startTime: "",
        endTime: "",
        superviseId: "",
        pageNum: 1,
        pageSize: 20,
      },
      dateRange: [],

      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 表格数据
      tableData: [],
    };
  },
  created() {
    this.queryParams.superviseId = this.info.superviseId;
    this.queryParams.motionType = this.motionType;
    this.getList();
  },
  computed: {
    housingMethod() {
      return () => {
        return "直接入库";
      };
    },
    motionTypeName() {
      return this.motionType == 1 ? "入库" : "出库";
    },
  },
  methods: {
    //列表查询
    getList() {
      outentRecordPage(this.queryParams).then((res) => {
        if (res.code == 200) {
          this.tableData = res.result.list || [];
          this.total = Number(res.result.total);
          this.loading = false;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.pageNum = 1;
      if (this.dateRange.length>0) {
        this.queryParams.startTime = this.dateRange[0]+' 00:00:00';
        this.queryParams.endTime = this.dateRange[1]+' 23:59:59';
      } else {
        this.queryParams.startTime = "";
        this.queryParams.endTime = "";
      }
      this.getList();
    },

    //打开出入库详情 motionType   1 入库 2 出库
    openOutEnterHoseDig(rowInfo) {
      this.outEnterHoseData.motionType = this.motionType;
      this.outEnterHoseData.info = rowInfo;
      this.outEnterHoseData.title =
        this.motionType == 1 ? "入库详情" : "出库详情";
      this.outEnterHoseData.open = true;
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.queryParams.startTime = "";
      this.queryParams.endTime = "";
      this.handleQuery();
    },
    close() {
      this.$emit("close");
      this.outEnterHoseData.open = false;
    },
  },
};
</script>

<style lang="scss">
.cttt {
  display: flex;
}
.xttname {
  margin-right: 30px;
  font-size: 16px;
  color: #333;
  margin-top: 5px;
  font-weight: bold;
}
</style>

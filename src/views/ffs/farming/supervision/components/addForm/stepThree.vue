<template>
  <div class="main">
    <el-form ref="form" :model="form" :rules="rules" label-width="120px">
      <div class="iputiirt">监管顾问</div>
      <el-row type="flex" justify="space-between">
        <el-col :span="11">
          <el-form-item label="监管员：" prop="supervisorName">
            <el-select
              clearable
              v-model="form.supervisorName"
              filterable
              remote
              reserve-keyword
              placeholder="请输入监管员名称/手机号码"
              :remote-method="remoteMethod"
              class="selectWidth"
              @change="onChange"
            >
              <el-option
                v-for="item in selectList"
                :key="item.userId"
                :label="labelName(item)"
                :value="item"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="11">
          <el-form-item label="客户经理：" prop="loanOfficerName">
            <el-select v-model="form.loanOfficerName" clearable @change="onConfirmsLoanOfficer">
              <el-option
                v-for="it in showshowLoanList"
                :key="it.userId"
                :label="it.corprateName||it.nickName||it.userName"
                :value="it"
              />
            </el-select>
            <span
              style="margin-left:10px"
              v-if="investigationInfo.pastorName"
            >畜牧师：{{investigationInfo.pastorName}}</span>
          </el-form-item>
        </el-col>
      </el-row>

      <div class="iputiirt">
        <span class="marrt20">被监管货区</span>
        <el-button type="primary" size="mini" class="lobtb" @click="selectCargoAreaShow">
          <i class="el-icon-thumb el-icon--right"></i>
          选择被监管货区
        </el-button>
        <el-button type="primary" size="mini" class="lobtb" @click="hardware" v-show="storehouseInfo.storehouseId">
          <i class="el-icon-thumb el-icon--right"></i>
          查看硬件设备
        </el-button>
      </div>

      <div class="smidf" v-if="form.storehouseList?.length">所属仓库：{{storehouseInfo.storehouseName}}</div>
      <div class="smidfmid" v-if="form.storehouseList?.length">仓库地址：{{storehouseInfo.address}}</div>
      <el-table :data="form.storehouseList" class="mattops" border>
        <el-table-column label="序号" type="index" width="50"></el-table-column>
        <el-table-column label="货区名称" prop="positionName"></el-table-column>
        <el-table-column label="操作" width="180" align="center">
          <template slot-scope="scope">
            <el-button
              icon="el-icon-delete"
              size="mini"
              type="text"
              @click="deleteCargoArea(scope.$index)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="iputiirt mattops">
        <span class="marrt20">质押商品</span>
        <el-button type="primary" size="mini" class="lobtb" @click="selectGoodsShow">
          <i class="el-icon-thumb el-icon--right"></i>
          选择质押商品
        </el-button>
      </div>

      <div class="smidfieym">
        <div class="tableHeight">
          <!-- <div style="height:100px;background:red" v-for="item in 50">8888888</div> -->
          <el-table :data="form.commodityList" border>
            <el-table-column label="序号" type="index" fixed width="50px" ></el-table-column>
            <el-table-column label="商品品类" prop="commodityCategoryName" width="90px"></el-table-column>
            <el-table-column label="商品名称" prop="commodityName" width="120px"></el-table-column>
            <el-table-column
              label="商品规格"
              prop="commodityUnitName"
              :formatter="unitName"
              width="110px"
              fixed
            ></el-table-column>
            <el-table-column label="商品编码" prop="commodityNo" width="100px"></el-table-column>
            <el-table-column label="当前市场单价(元)" prop="marketPrice" width="125px">
              <template slot-scope="scope">
                <el-input
                  v-model="scope.row.marketPrice"
                  @blur="marketPriceCheck(scope.row)"
                  type="number"
                  placeholder="市场单价"
                ></el-input>
              </template>
            </el-table-column>
            <el-table-column label="质押率%" prop="mortgageRate" width="135px">
              <template slot-scope="scope">
                <el-input
                  v-model="scope.row.mortgageRate"
                  @blur="mortgageRateCheck(scope.row)"
                  type="number"
                  placeholder="质押率"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column label="质押单价(元)" prop="mortgagePrice" width="135px"></el-table-column>

            <el-table-column label="质押数" prop="mortgageNum" width="135px" fixed="right">
              <template slot-scope="scope">
                <el-input
                  v-model.number="scope.row.mortgageNum"
                  @blur="mortgageNumCheck(scope.row)"
                  type="number"
                  placeholder="质押数量"
                >
                  <template slot="append">件</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column label="质押重量(千克)" prop="mortgageWeight" fixed="right" width="120px"></el-table-column>
            <el-table-column label="质押货值(元)" prop="mortgageTotalPrice" fixed="right" width="120px"></el-table-column>
            <el-table-column label="操作" fixed="right">
              <template slot-scope="scope">
                <el-button
                  icon="el-icon-delete"
                  size="mini"
                  type="text"
                  @click="handleDelete(scope.$index)"
                >删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="tablebew" v-if="form.commodityList?.length">
          <div>质押数量合计：{{mortgageNumTotal}}</div>
          <div>质押重量合计：{{mortgageWeightTotal}}吨</div>
          <div>质押货值合计：{{mortgageTotalPriceTotal}}元</div>
        </div>
      </div>
    </el-form>

    <select-goods
      :selectGoodsFormData="selectGoodsFormData"
      @close="close"
      v-if="selectGoodsFormData.open"
      @refresh="refresh"
    ></select-goods>

    <select-cargo-area
      :selectCargoAreaFormData="selectCargoAreaFormData"
      @close="close"
      v-if="selectCargoAreaFormData.open"
      @cargoAreaRefresh="cargoAreaRefresh"
    ></select-cargo-area>
    <!-- 硬件设备 -->
    <el-dialog
      title="硬件设备"
      :visible.sync="hardwareOpen"
      width="1000px"
      :modal-append-to-body="false"
      :append-to-body="true"
      :close-on-click-modal="false"
      @close="close"
    >
    <monitorList :storehouseId="storehouseInfo.storehouseId"></monitorList>
    </el-dialog>
  </div>
</template>

<script>
import { searchUser } from "@/api/system/user.js"; // 查询搜索用户  可以手机号、昵称等等信息搜索
import {
  byinvestigationId,
  loanOfficerList,
  superviseLivestockList,
} from "@/api/ffs/farmingSupervisionSheet/livingSupervisionApi";
import {
  storehouseListBuySuperviseId,
  commodityListBuySuperviseId,
} from "@/api/ffs/farmingMortgage/supervision";

import selectGoods from "./selectGoods.vue";
import selectCargoArea from "./selectCargoArea.vue";
import monitorList from '@/views/ffs/mortgage/storeManage/monitorList'
import { toFixed2 } from "@/utils/east.js";
export default {
  name: "insstepThree",
  components: {
    selectGoods,
    selectCargoArea,
    monitorList
  },
  props: {
    infoData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
        hardwareOpen:false,
      selectGoodsFormData: {
        open: false,
        id: "",
        commodityList: [],
      },
      selectCargoAreaFormData: {
        open: false,
        id: "",
        checkList: [],
      },
      disabled: false,
      form: {
        supervisorId: "", //监管员id
        supervisorName: "", //监管员名称
        supervisorPhone: "", //监管员联系方式
        loanOfficerId: "", //信贷员id
        loanOfficerName: "", //信贷员名称
        loanOfficerPhone: "", //信贷员手机号
        storehouseList: [], //货区列表
        commodityList: [], //商品信息
      },
      satisticsList: [], //监管单关联活畜统计列表
      tableList: [],
      storehouseInfo: {},

      // 表单校验
      rules: {
        loanOfficerName: [
          { required: true, message: "请选择客户经理", trigger: "blur" },
        ],
        supervisorName: [
          { required: true, message: "请选择监管员", trigger: "blur" },
        ],
      },
      investigationInfo: { pastorName: "" },
      selectList: [],
      showshowLoanList: [],
    };
  },
  created() {
    if (this.infoData.operation == "edit") {
      this.$store.commit("SET_ADD_FROM", this.infoData);
      this.getStorehouseListBuySuperviseId(this.infoData.superviseId);
      this.getCommodityListBuySuperviseId(this.infoData.superviseId);
    }
    this.initData();
    this.form = this.$store.getters.supervision.addFrom;
  },
  computed: {
    labelName() {
      return (val) => {
        if (!val) return;
        if (val.corprateName) return val.corprateName;
        if (val.nickName) return val.nickName;
        if (val.userName) return val.userName;
        if (val.phonenumber) return val.phonenumber;
      };
    },
    unitName() {
      return (row, com, val) => {
        return (
          row.commoditySpecifications +
          row.commodityUnitName +
          "/" +
          row.inventoryUnitName
        );
      };
    },

    mortgageNumTotal() {
      let num = 0;
      this.form.commodityList.forEach((item) => {
        num += item.mortgageNum * 1;
      });
      return num;
    },
    mortgageWeightTotal() {
      let num = 0;
      this.form.commodityList.forEach((item) => {
        num += item.mortgageWeight * 1;
      });
      return toFixed2(num / 1000);
    },
    mortgageTotalPriceTotal() {
      let num = 0;
      this.form.commodityList.forEach((item) => {
        num += item.mortgageTotalPrice * 1;
      });
      return toFixed2(num);
    },
  },

  mounted() {},

  methods: {
    //查看硬件设备
    hardware(){
        this.hardwareOpen=true
        console.log( this.storehouseInfo.storehouseId)
    },
    //删除商品
    handleDelete(index) {
      this.form.commodityList.splice(index, 1);
    },
    //删除货区
    deleteCargoArea(index) {
      this.form.storehouseList.splice(index, 1);
    },
    //提交验证
    submitThree() {
      if (this.form.storehouseList.length == 0) {
        this.$message({
          message: "请选择被监管货区",
          type: "error",
        });
        return false;
      }
      if (this.form.commodityList.length == 0) {
        this.$message({
          message: "请选择抵押商品",
          type: "error",
        });
        return false;
      }
      let insuranceAmountok = true;
      this.form.commodityList.forEach((item) => {
        if (
          item.marketPrice <= 0 ||
          item.mortgageRate <= 0 ||
          item.mortgageNum < 0
        ) {
          insuranceAmountok = false;
        }
      });
      if (!insuranceAmountok) {
        this.$message({
          message: "请填写 当前市场价格 | 质押率 | 质押数量",
          type: "error",
        });
        return false;
      }

      let threeOk = false;
      this.$refs["form"].validate((valid) => {
        if (!valid) return false;
        this.form.threeOk = valid;
        threeOk = valid;
        return true;
      });
      return threeOk;
    },
    marketPriceCheck(val) {
      this.$modal.loading("校验数据中...");
      if (val.marketPrice <= 0) {
        val.marketPrice = "";
        this.$message({
          message: "请输入正确有效的市场价格",
          type: "error",
        });
        this.$modal.closeLoading();
        return;
      }
      this.$modal.closeLoading();
      val.marketPrice = toFixed2(val.marketPrice);
      val.mortgagePrice = toFixed2(val.marketPrice * val.mortgageRate * 0.01);
      val.mortgageTotalPrice = toFixed2(val.mortgagePrice * val.mortgageNum);
    },

    mortgageRateCheck(val) {
      this.$modal.loading("校验数据中...");
      if (val.mortgageRate <= 0) {
        val.mortgageRate = "";
        this.$message({
          message: "请输入正确有效的质押率",
          type: "error",
        });
        this.$modal.closeLoading();
        return;
      }
      // console.log("val.mortgageRate：", val.mortgageRate); mortgagePrice

      if (val.mortgageRate > 100) {
        val.mortgageRate = 100;
        this.$message({
          message: "质押率最大不能超过100",
          type: "error",
        });
        this.$modal.closeLoading();
        return;
      }
      this.$modal.closeLoading();
      val.mortgageRate = toFixed2(val.mortgageRate);
      val.mortgagePrice = toFixed2(val.marketPrice * val.mortgageRate * 0.01);
      val.mortgageTotalPrice = toFixed2(val.mortgageRate * val.mortgageNum);
    },

    mortgageNumCheck(val) {
      this.$modal.loading("校验数据中...");
      if (val.mortgageNum < 0 || val.mortgageNum.toString().includes(".")) {
        val.mortgageNum = "";
        this.$message({
          message: "请输入正确有效的质押数量",
          type: "error",
        });
        this.$modal.closeLoading();
        return;
      }
      this.$modal.closeLoading();
      val.mortgageWeight = toFixed2(
        val.mortgageNum * getCommoditySpecifications(val)
      );
      val.mortgageTotalPrice = toFixed2(val.mortgagePrice * val.mortgageNum);
      function getCommoditySpecifications(val) {
        let num = 0; //转换为kg
        if (val.commodityUnitName == "克") {
          num = val.commoditySpecifications / 1000;
        }
        if (val.commodityUnitName.includes("千克")) {
          num = val.commoditySpecifications;
        }
        if (val.commodityUnitName.includes("吨")) {
          num = val.commoditySpecifications * 1000;
        }
        return num * 1;
      }
    },

    switchShow(val) {
      this.$modal.loading("校验数据中...");
      if (val.insuranceAmount <= 0) {
        val.insuranceAmount = "";
        this.$message({
          message: "请输入正确有效的保险金额",
          type: "error",
        });
        this.$modal.closeLoading();
        return;
      }
      this.$modal.closeLoading();
      let tempVal = parseFloat(val.insuranceAmount).toFixed(3);
      val.insuranceAmount = tempVal.substring(0, tempVal.length - 1);
      val.insuranceTotalAmount = val.insuranceAmount * val.superviseTotal;
    },

    //远程搜索
    remoteMethod(data) {
      searchUser({ phonenumber: data, userType: "00" }).then((res) => {
        if (res.code == 200) {
          this.selectList = res.result;
        }
      });
    },
    // 确定选择监管员
    onChange(val) {
      this.form.supervisorId = val.userId;
      this.form.supervisorName = val.nickName;
      this.form.supervisorPhone = val.phonenumber;
      this.form = this.$store.getters.supervision.addFrom;
    },
    //确认信贷员
    onConfirmsLoanOfficer(val) {
      this.form.loanOfficerId = val.userId;
      this.form.loanOfficerName = val.nickName;
      this.form.loanOfficerPhone = val.phonenumber;
      this.form = this.$store.getters.supervision.addFrom;
    },
    async initData() {
      // 获取调研单详情
      if (this.infoData.intentionListId) {
        byinvestigationId({ ids: [this.infoData.intentionListId] }).then(
          (res) => {
            if (res.code == 200) {
              this.investigationInfo = res.result;
            }
          }
        );
      }

      // 信贷员列表
      let loanOffres = await loanOfficerList({
        tenantId: this.infoData.bankId,
        pageNum: 1,
        pageSize: 200,
      });
      if (loanOffres.code == "200") {
        const result = loanOffres.result.list;
        this.showshowLoanList = result;
      }
    },

    //关闭弹框
    close() {
      this.selectGoodsFormData.open = false;
      this.selectCargoAreaFormData.open = false;
    },

    //选择货区
    cargoAreaRefresh(storehouseInfo, storehouseList) {
      //       console.log("选择货区storehouseInfo:", storehouseInfo);
      //       console.log("选择货区storehouseList:",  storehouseList);
      this.form.storehouseList = storehouseList || [];
      this.storehouseInfo = storehouseInfo || {};
    },
    refresh(commodityList) {
      let list = commodityList || [];
      this.form.commodityList = [...list];
    },
    selectCargoAreaShow() {
      const checkListIds = [];
      this.selectCargoAreaFormData.open = true;
      this.selectCargoAreaFormData.title = "选择被监管货区";
      console.log(this.form)
      this.form.storehouseList.forEach((item) => {
        checkListIds.push(item.positionId);
      });
      this.selectCargoAreaFormData.checkList = checkListIds;
      this.selectCargoAreaFormData.storehouseId =
        this.storehouseInfo.storehouseId;
    },
    selectGoodsShow() {
      // if (this.form.storehouseList.length == 0) {
      //   this.$confirm("请先选择货区", "提示", {
      //     confirmButtonText: "确定",
      //     showCancelButton: false,
      //     type: "warning",
      //   }).then(() => {});

      //   return;
      // }
      this.selectGoodsFormData.open = true;
      this.selectGoodsFormData.title = "选择抵押商品";
      this.selectGoodsFormData.commodityList = this.removeDuplication(
        this.form.commodityList
      );
    },

    /** //根据仓单监管单获取绑定的货区 */
    getStorehouseListBuySuperviseId(superviseId) {
      storehouseListBuySuperviseId({
        superviseId,
        pageNum: 1,
        pageSize: 9999,
      }).then((res) => {
        if (res.code == 200) {
          const result = res.result?.list|| [];
          console.log( result,"cccc")
          if (result.length) {
            let storehouseInfo = {
              storehouseName: result[0].storehouseName,
              address: result[0].storehouseAddress,
              storehouseId: result[0].storehouseId,
            };
            let storehouseList = result;
            this.cargoAreaRefresh(storehouseInfo, storehouseList);
          }
        } else {
          this.$message.error(res.message);
        }
      });
    },

    //去重
    removeDuplication(list) {
      let obj = {};
      let peon = list.reduce((cur, next) => {
        obj[next.commodityId]
          ? ""
          : (obj[next.commodityId] = true && cur.push(next));

        return cur;
      }, []);
      return peon;
    },
    /** //根据仓单监管单获取绑定的商品 */
    getCommodityListBuySuperviseId(superviseId) {
      commodityListBuySuperviseId({
        superviseId,
        pageNum: 1,
        pageSize: 9999,
      }).then((res) => {
        if (res.code == 200) {
          const commodityList = res.result.list || [];
          this.refresh(commodityList);
        } else {
          this.$message.error(res.message);
        }
      });
    },
  },
};
</script>
<style  scoped>
/* @import url(); 引入css类 */
.mattops {
  margin-top: 20px;
}
/deep/ .el-input-group__append {
  padding: 0 2px !important;
}
.tableHeight {
  max-height: 600px;
  overflow-y: scroll;
}
</style>

<template>
  <div>
    <el-dialog
      :title="selectGoodsFormData.title"
      :visible.sync="selectGoodsFormData.open"
      width="1050px"
      :close-on-click-modal="false"
      @close="close"
      class="fieldList"
      append-to-body
      custom-class="mydialog"
    >
      <el-card>
        <div slot="header" class="clearfix"></div>
        <el-row :gutter="10" class="mb8">
          <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
            <el-form-item label="质押品类型：" prop="mortgageType">
              <el-select v-model="queryParams.mortgageType" clearable class="selectWidth">
                <el-option
                  v-for="dict in dict.type.ffs_mortgage_type"
                  :key="dict.label"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="商品品类：" prop="commodityCategory">
              <el-select v-model="queryParams.commodityCategory" clearable class="selectWidth">
                <el-option
                  v-for="dict in dict.type.ffs_commodity_category"
                  :key="dict.label"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="加工方式：" prop="processWay">
              <el-select v-model="queryParams.processWay" clearable class="selectWidth">
                <el-option
                  v-for="dict in dict.type.ffs_machining_type"
                  :key="dict.label"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="商品类型：" prop="commodityType">
              <el-select v-model="queryParams.commodityType" clearable class="selectWidth">
                <el-option
                  v-for="dict in dict.type.ffs_commodity_type"
                  :key="dict.label"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="商品编码/名称：" prop="searchValue">
              <el-input v-model="queryParams.searchValue" placeholder="请输入商品编码/名称" clearable />
            </el-form-item>
            <el-form-item label="创建人：" prop="createUserName">
              <el-input v-model="queryParams.createUserName" placeholder="请输入创建人姓名" clearable />
            </el-form-item>
            <el-form-item label="创建时间：">
              <el-date-picker
                v-model="time"
                value-format="yyyy-MM-dd HH:mm:ss"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              ></el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-row>

        <!-- 表格数据 -->
        <el-table
          :data="list"
          stripe
          style="width: 100%"
          v-loading="loading"
          ref="multipleTable"
          :row-key="(row) => row.commodityId"
          @selection-change="tableSelectionChange"
          @select="tableToggleRowSelection"
          border
        >
          <el-table-column type="selection" :reserve-selection="true"></el-table-column>
          <el-table-column type="index" label="序号"></el-table-column>
          <el-table-column prop="mortgageTypeName" label="质押品类型" />
          <el-table-column prop="commodityCategoryName" label="商品品类"></el-table-column>
          <el-table-column prop="processWayName" label="加工方式"></el-table-column>
          <el-table-column prop="commodityTypeName" label="商品类型"></el-table-column>
          <el-table-column prop="commodityName" label="商品名称"></el-table-column>
          <el-table-column prop="commodityNo" label="商品编码"></el-table-column>
          <el-table-column prop="commodityUnitName" label="计量单位"></el-table-column>
          <el-table-column prop="inventoryUnitName" label="库存单位"></el-table-column>
          <el-table-column prop="commoditySpecifications" label="商品规格"></el-table-column>
          <!-- <el-table-column prop="createTime" label="创建时间"></el-table-column> -->
          <!-- <el-table-column label="操作" align="center" fixed="right" width="150px">
          <template
            slot-scope="scope"
          >{{scope.row.status}}--{{scope.row.commodityId}}</template>
          </el-table-column> -->
        </el-table>
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-card>
      <div class="tablebewtosa skeme">
        <div>商品总数量：{{total}}</div>
        <div>已选质押数量：{{getSelectCommodityLength}}</div>
      </div>
      <span slot="footer" class="dialog-footer" v-show="!selectGoodsFormData.disable">
        <el-button @click="close">取消</el-button>
        <el-button type="primary" @click="submitForm">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { commodityList } from "@/api/ffs//farmingMortgage/commodityManage";
export default {
  dicts: [
    "ffs_mortgage_type",
    "ffs_commodity_category",
    "ffs_machining_type",
    "ffs_commodity_type",
  ],
  name: "selectAnimalf",
  props: {
    selectGoodsFormData: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      queryParams: {
        mortgageType: undefined,
        searchValue: undefined,
        processWay: undefined,
        commodityCategory: undefined,
        commodityType: undefined,
        pageNum: 1,
        pageSize: 10,
        createStart: "",
        createEnd: "",
      },

      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 表格数据
      list: [],
      time: "",
      pastureList: [],
      commodityList: [],
      selectSource: [],
    };
  },
  created() {
    this.getList();
  },
  computed: {
    getSelectCommodityLength() {
      let submitCommodity = this.selectGoodsFormData.commodityList.concat(
        this.commodityList
      );

      submitCommodity = this.removeDuplication(submitCommodity);
      console.log('submitCommodity：',submitCommodity);

      return submitCommodity.length;
    },
    selectEnable() {
      return (row, rowIndex) => {
        if (row.status == 1) {
          return true;
        } else {
          return false;
        }
      };
    },
  },
  methods: {
    /** 查询活畜列表 */
    getList() {
      commodityList(this.queryParams).then((res) => {
        this.loading = false;
        if (res.code == 200) {
          //   console.log("查询活畜列表:", res);
          const result = res.result.list || [];
          result.forEach((item) => {
            item.marketPrice = ""; //当前市场价格
            item.mortgageRate = ""; //质押率
            item.mortgagePrice = ""; //质押单价
            item.mortgageNum = ""; // 质押数量
            item.mortgageWeight = ""; //质押重量（kg）
            item.mortgageTotalPrice = ""; //质押货值
          });
          this.list = result;
          this.total = Number(res.result.total || 0);
          this.selectGoodsFormData.commodityList.forEach((key) => {
            res.result.list.forEach((row) => {
              if (row.commodityId == (key.commodityId || key)) {
                row.marketPrice = key.marketPrice;
                row.mortgageRate = key.mortgageRate;
                row.mortgagePrice = key.mortgagePrice;
                row.mortgageNum = key.mortgageNum;
                row.mortgageWeight = key.mortgageWeight;
                row.mortgageTotalPrice = key.mortgageTotalPrice;
                this.$refs.multipleTable.toggleRowSelection(row, true);
              }
            });
          });
        } else {
          this.$message.error(res.message);
        }
      });
    },

    //保存选择的商品
    submitForm() {
      let submitCommodity = this.selectGoodsFormData.commodityList.concat(
        this.commodityList
      );
      submitCommodity = this.removeDuplication(submitCommodity);

      if (submitCommodity.length == 0) {
        this.$message({
          message: "请选择商品",
          type: "error",
        });
        return;
      }
      this.$emit("refresh", submitCommodity);
      this.$emit("close");
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      if (this.time && this.time.length > 0) {
        this.queryParams.createStart = this.time[0];
        this.queryParams.createEnd = this.time[1];
      } else {
        this.queryParams.createStart = "";
        this.queryParams.createEnd = "";
      }
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.time = "";
      this.resetForm("queryForm");
      this.handleQuery();
    },
    //去重
    removeDuplication(list) {
      let obj = {};
      let peon = list.reduce((cur, next) => {
        obj[next.commodityId]
          ? ""
          : (obj[next.commodityId] = true && cur.push(next));

        return cur;
      }, []);
      return peon;
    },
    //关闭弹框
    close() {
      this.$emit("close");
    },
    /** 打开创建监管单弹窗 */
    handleAddd() {
      this.$emit("addSupervision");
    },
    // 表格选择事件
    tableSelectionChange(val) {
      console.log(val);
      this.commodityList = val;
    },

    // 表格取消选择事件
    tableToggleRowSelection(row, selected) {
      let isCkeck = row.length && row.indexOf(selected) !== -1;
      if (!isCkeck || isCkeck == 0) {
        const index = this.selectGoodsFormData.commodityList.findIndex(
          (item) => (item.commodityId || item) === selected.commodityId
        );
        if (index >= 0) {
          this.selectGoodsFormData.commodityList.splice(index, 1);
        }
      }
    },
  },
};
</script>

<style lang="scss">
.fieldList {
  .el-dialog__header {
    background-color: #f4f4f4;
  }

  .el-dialog__footer {
    text-align: center;
  }

  .selectWidth {
    width: 100%;
  }
}
.head-title {
  font-size: 16px;
  color: #999;
  margin-bottom: 20px;
}
.head-title span {
  color: red;
}
// .mydialog{
//   height: 60vh;
// }
</style>

<template>
  <div>
    <el-dialog
      :title="useLetterData.title"
      :visible.sync="useLetterData.open"
      :close-on-click-modal="false"
      @close="close"
      class="fieldList filebox"
      append-to-body
    >
      <div class="btnxvs" v-if="useLetterData.type=='list'">
        <el-button
          type="primary"
          icon="el-icon-s-finance"
          v-if="useLetterData.rowData.superviseStatus == 2"
          v-hasPermi="['mortgage:supervision:add:quota']"
          @click="adduseLetter"
        >追加用信金额</el-button>
      </div>
      <el-table :data="list" show-summary border v-loading="loading" :summary-method="getSummaries">
        <el-table-column label="序号" type="index" fixed width="50" align="center"></el-table-column>
        <el-table-column property="receiveAmount" label="用信金额（万元）" align="center"></el-table-column>
        <el-table-column property="appendServiceFee" label="服务费（元）" align="center"></el-table-column>
        <el-table-column property="operatorName" label="操作人" align="center"></el-table-column>
        <el-table-column property="createTime" label="操作时间" align="center"></el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="close">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { addSuperviseamountList } from "@/api/ffs/farmingMortgage/supervision";
import { toFixed2 } from "@/utils/east.js";
export default {
  name: "useLetterlLog",
  props: {
    useLetterData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 表格数据
      list: [],

      queryParams: {
        pageNum: 1,
        pageSize: 99999,
      },
    };
  },
  created() {
    this.getList();
  },
  computed: {
  },
  methods: {
    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = "合计";
          return;
        }

        const values = data.map((item) => Number(item[column.property]));
        if (!values.every((value) => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!isNaN(value)) {
              return prev + curr;
            } else {
              return prev;
            }
          }, 0);

          if (index == 2) {
            return (sums[index] += "元");
          } else {
            return (sums[index] += "万元");
          }
        } else {
          sums[index] = "";
        }
      });

      return sums;
    },
    /** 查询列表 */
    getList() {
      addSuperviseamountList({
        superviseId: this.useLetterData.rowData.superviseId,
        operationType:0,
        pageNum: 1,
        pageSize: 10000,
      }).then((res) => {
        setTimeout(() => {
          this.loading = false;
        }, 300);
        if (res.code == 200) {
          this.list = res.result.list || [];
          //   this.total = Number(res.result.total || 0);
        } else {
          this.$message.error(res.message);
        }
      });
    },
    close() {
      this.$emit("close");
    },
    adduseLetter() {
      this.$emit("adduseLetter", this.useLetterData.rowData);
    },
  },
};
</script>

<style lang="scss" scoped>
.btnxvs {
  margin-bottom: 10px;
}
</style>


<template>
    <div>
      <el-row :gutter="10" class="mb8">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
          <el-form-item label="交易时间：">
            <el-date-picker
              v-model="time"
              value-format="yyyy-MM-dd"
              style="width: 240px"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            ></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleExportQuot"
              v-show="total > 0"
            >导出</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </el-row>

      <el-table :data="list" stripe style="width: 100%;margin-top:10px" v-loading="loading" border>
        <el-table-column type="index" label="序号" width="50px"></el-table-column>
        <el-table-column prop="orderCode" label="交易单号" width="180" align="center"></el-table-column>
        <el-table-column prop="checkInCode" label="入厂编号" width="180px" align="center"></el-table-column>
        <el-table-column prop="supplierName" label="供应商" show-overflow-tooltip></el-table-column>
        <el-table-column prop="supplierContactPhone" label="联系电话" width="180px" align="center"></el-table-column>
        <el-table-column prop="checkInTime" label="入厂时间" width="180px" align="center"></el-table-column>
        <el-table-column prop="finalButcherNum" label="屠宰数量" header-align='center' width="130" align="right"></el-table-column>
        <el-table-column prop="finalNetWeight" label="重量(kg)" header-align='center'  width="100" align="right"></el-table-column>
        <el-table-column prop="orderAmount" label="支出金额" header-align='center'  width="100" align="right"></el-table-column>
        <el-table-column prop="totalAmount" label="额标支付金额" header-align='center'  width="120" align="right"></el-table-column>
        <el-table-column prop="orderAmountRate" label="额度支付占比(%)" header-align='center'  width="130" align="right"></el-table-column>
        <el-table-column prop="orderAmount" label="非额度支付金额" header-align='center'  width="120" align="right">
          <template slot-scope="scope">
            <span>{{(scope.row.orderAmount*1000-scope.row.totalAmount*1000)/1000}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="createUserName" label="负责人"  width="100"  show-overflow-tooltip></el-table-column>
        <el-table-column prop="transactionTime" label="交易时间" width="180px" align="center"></el-table-column>
        <el-table-column label="操作" align="center" width="180px" class-name="small-padding fixed-width"  fixed="right">
          <template slot-scope="scope">
            <el-button
              class="text_btn"
              size="mini"
              icon="el-icon-warning-outline"
              @click="stockInfo(scope.row.orderCode)"
              type="text"
            >查看</el-button>
            <el-button
              icon="el-icon-download"
              size="mini"
              type="text"
              @click="downloadFile(scope.row)"
            >购销合同下载</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
      <el-image-viewer
        :zIndex="3001"
        v-if="showViewer"
        :on-close="() => { showViewer = false;}"
        :url-list="imgList"
      />
      <consumptionInfo ref="model"></consumptionInfo>
    </div>
  </template>

  <script>
  import { recordsHistoryTable } from "@/api/ffs/superviseLhApi.js";
  import { settlementInfo } from "@/api/ffs/noGoodsPledged.js";
  import { exportContract } from "@/api/nlbao/repayment";
  import { toFixed2, getFilePath } from "@/utils/east.js";
  import ElImageViewer from "element-ui/packages/image/src/image-viewer";
  import consumptionInfo from  './components/consumptionInfo.vue'
  import { exportQuot } from "@/views/ffs/fundFlowSupervision/components/detailsForm/exportQuot.js";
  export default {
    name: "consumeHistory",
    components: {
      ElImageViewer,
      consumptionInfo
    },
    props: {
      info: {
        type: Object,
        default: () => {},
      },
    },
    data() {
      return {
        queryParams: {
          orderCode: "",
          pageNum: 1,
          pageSize: 10,
          tranStartTime: "",
          tranEndtTime: "",
        },
        time: undefined,
        // 遮罩层
        loading: false,
        // 总条数
        total: 0,
        // 表格数据
        list: [],
        imgList: [],
        showViewer: false,
      };
    },
    created() {
      this.queryParams.quotaCode = this.info.contractNo;
      this.getList();
    },

    computed: {
      payTypeName() {
        return () => "我的额度";
      },
      orderStatusName() {
        return (row, com, val) => {
          const statusMap = {
            0: "订单生成",
            1: "交易受理",
            2: "支付成功",
            3: "确认收货中",
            4: "已完成",
          };
          return statusMap[val];
        };
      },
    },
    methods: {
      bigImg(url) {
        this.imgList = [];
        this.imgList = getFilePath(url);
        this.showViewer = true;
      },
      /** 消费记录 */
      getList() {
        recordsHistoryTable(this.queryParams).then((res) => {
          this.loading = false;
          this.list=res.data.list||[]
        this.total= Number(res.data.totalRow)
        //   if (res.stautscode == 200) {
        //         this.getHistory(res.data.list[0].orderCode)
        //   } else {
        //     this.$message.error(res.message);
        //   }
        });
      },


      getHistory(id){
        settlementInfo({settlementCode:id}).then(res=>{

        })
      },

      //导出
      downloadFile(row) {
        exportContract({ orderCode: row.orderCode }).then((res) => {
          if (res.stautscode === 200) {
            if (res.data.docs) {
              window.open(res?.data?.docs[0]?.fileUrl, "_blank");
            } else {
              window.open(res.data, "_blank");
            }
          } else {
            this.$message.error("合同下载失败");
          }
        });
      },

      handleExportQuot() {
      exportQuot(this, 1, this.info.contractNo);
    },
      /** 搜索按钮操作 */
      handleQuery() {
        if (this.time && this.time.length > 0) {
          this.queryParams.tranStartTime = this.time[0];
          this.queryParams.tranEndtTime = this.time[1];
        } else {
          this.queryParams.tranStartTime = "";
          this.queryParams.tranEndtTime = "";
        }
        this.pageNum = 1;
        this.getList();
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.time = [];
        this.queryParams.tranStartTime = "";
        this.queryParams.tranEndtTime = "";
        this.resetForm("queryForm");
        this.handleQuery();
      },
    //  详情
      stockInfo(id){
        this.$refs.model.showModel(id)
      },
      //关闭弹框
      close() {
        this.outEnterHoseData.open = false;
        this.$emit("close");
      },
    },
  };
  </script>

  <style lang="scss">
  .fieldList {
    .el-dialog__header {
      background-color: #f4f4f4;
    }

    .el-dialog__footer {
      text-align: center;
    }

    .selectWidth {
      width: 100%;
    }
  }
  </style>

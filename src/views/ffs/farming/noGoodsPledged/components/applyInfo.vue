<template>
  <div>
    <div v-if="!info" class="empty">暂无申请信息记录</div>
    <div v-else>
      <el-descriptions :column="4" title="申请人信息" :contentStyle="CS" :label-style="LS">
        <el-descriptions-item label="额度类型">{{typeName(info.type)}}</el-descriptions-item>
        <el-descriptions-item label="申请方式">{{info.applyTypeName}}</el-descriptions-item>
        <el-descriptions-item label="申请人">{{info.userName}}</el-descriptions-item>
        <el-descriptions-item label="联系电话">{{info.userPhone}}</el-descriptions-item>
        <el-descriptions-item label="身份证号">{{showCarId(info.userIdNo)}}</el-descriptions-item>
        <el-descriptions-item label="婚姻状况">{{info.maritalStatus | formartSex}}</el-descriptions-item>
        <el-descriptions-item label="配偶姓名" v-if="info.spouseName">{{info.spouseName}}</el-descriptions-item>
        <el-descriptions-item label="配偶身份证号" v-if="info.spouseIdNo">{{showCarId(info.spouseIdNo)}}</el-descriptions-item>
      </el-descriptions>
      <el-row class="filitem mb10">
        <el-col :span="12" v-if="info.applyPeopleIds?.length">
          <div class="head-title">申请人身份证信息：</div>
          <el-image
            style="width: 190px; height: 120px; padding: 0 5px"
            v-for="(item,index) in info.applyPeopleIds"
            :key="index"
            :src="item"
            :preview-src-list="srcList"
            @click="bigImage(info.applyPeopleIds)"
          ></el-image>
        </el-col>
        <el-col :span="12" v-if="info.spouseIdPicIds?.length">
          <div class="head-title">配偶身份证信息：</div>
          <el-image
            style="width: 190px; height: 120px; padding: 0 5px"
            v-for="(item,index) in info.spouseIdPicIds"
            :key="index"
            :src="item"
            :preview-src-list="srcList"
            @click="bigImage(info.spouseIdPicIds)"
          ></el-image>
        </el-col>
      </el-row>

      <el-descriptions title="经营信息">
        <el-descriptions-item label="经营场所">{{info.businessPremisesName}}{{info.address}}</el-descriptions-item>
      </el-descriptions>

      <el-descriptions :column="4" :contentStyle="CS" :label-style="LS">
        <el-descriptions-item label="牛存栏（头）">{{info.cattleNum}}</el-descriptions-item>
        <el-descriptions-item label="羊存栏（只）">{{info.sheepNum}}</el-descriptions-item>

        <el-descriptions-item label="自有草场（亩）">{{info.ownPasture}}</el-descriptions-item>
        <el-descriptions-item label="租用草场（亩）">{{info.rentPasture}}</el-descriptions-item>
        <el-descriptions-item label="棚圈面积（平方米）" v-if="info.shedArea">{{info.shedArea}}</el-descriptions-item>
        <el-descriptions-item label="房屋面积（平米）" v-if="info.housesArea">{{info.housesArea}}</el-descriptions-item>
        <el-descriptions-item label="负债金额">{{info.debtAmt}}元</el-descriptions-item>
        <el-descriptions-item label="负债银行">{{info.debtExplainStr||'--'}}</el-descriptions-item>
      </el-descriptions>

      <el-row class="filitem mb10" v-if="info.shedPicList?.length">
        <el-col>
          <div class="head-title">棚圈照片：</div>
          <el-image
            style="width: 190px; height: 120px; padding: 0 5px"
            v-for="(item,index) in info.shedPicList"
            :key="index"
            :src="item"
            :preview-src-list="srcList"
            @click="bigImage(info.shedPicList)"
          ></el-image>
        </el-col>
      </el-row>

      <el-row class="filitem mb10" v-if="info.livestockPicList?.length">
        <el-col>
          <div class="head-title">养殖活畜照片：</div>
          <el-image
            style="width: 190px; height: 120px; padding: 0 5px"
            v-for="(item,index) in info.livestockPicList"
            :key="index"
            :src="item"
            :preview-src-list="srcList"
            @click="bigImage(info.livestockPicList)"
          ></el-image>
        </el-col>
      </el-row>

      <el-row class="filitem mb10" v-if="info.housesPicList?.length">
        <el-col>
          <div class="head-title">申请人房屋照片：</div>
          <el-image
            style="width: 190px; height: 120px; padding: 0 5px"
            v-for="(item,index) in info.housesPicList"
            :key="index"
            :src="item"
            :preview-src-list="srcList"
            @click="bigImage(info.housesPicList)"
          ></el-image>
        </el-col>
      </el-row>

      <el-row class="filitem mb10" v-if="info.otherPicList?.length">
        <el-col>
          <div class="head-title">其它照片：</div>
          <el-image
            style="width: 190px; height: 120px; padding: 0 5px"
            v-for="(item,index) in info.otherPicList"
            :key="index"
            :src="item"
            :preview-src-list="srcList"
            @click="bigImage(info.otherPicList)"
          ></el-image>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import { fundAplyDetail } from "@/api/ffs/superviseLhApi.js";
import { getFilePath } from "@/utils/east.js";
export default {
  name: "inspectionInfo",
  props: {
    dataInfo: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      CS: {
        "max-width": "300px", //最小宽度
        "word-break": "break-all", //过长时自动换行
      },
      LS: {
        "word-break": "keep-all",
        color: "#000",
      },

      queryParams: {},
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 表格数据
      list: [],

      srcList: [""],
      info: {
        patrolLivestockList: [{ patrolPos: "" }],
      },
    };
  },
  filters: {
    formartSex: (type) => {
      const statusMap = {
        1: "未婚",
        2: "已婚",
        3: "离异",
        4: "丧偶",
      };
      return statusMap[type];
    },
  },
  created() {
    this.info = this.dataInfo;
    this.queryParams.quotaCode = this.dataInfo.contractNo;
    this.getDetails();
  },
  computed: {
    showCarId() {
      return (p) => {
        if (!p) return "--";
        return this.carId(p);
      };
    },
    typeName() {
      return (val) => {
        if (val == 1) {
          return "专项额度";
        } else if (val == 2) {
          return "通用额度";
        } else {
          return "";
        }
      };
    },
    applyTypeName() {
      return (val) => {
        if (val == 1) {
          return "活畜抵押";
        } else if (val == 2) {
          return "信用额度";
        } else {
          return "";
        }
      };
    },
  },
  methods: {
    bigImage(urls) {
      this.srcList = [];
      this.srcList = urls;
    },
    /** 申请信息 */
    getDetails(id) {
      fundAplyDetail(this.queryParams)
        .then((res) => {
          console.log("rte：", res);
          this.loading = false;
          if (res.stautscode == 200) {
            const dataList = res.data || {};
            //   申请人身份证图片
            let applyPeopleIds = "";
            if (dataList.userCertPic1) {
              applyPeopleIds += dataList.userCertPic1 + ",";
            }
            if (dataList.userCertPic2) {
              applyPeopleIds += dataList.userCertPic2;
            }
            applyPeopleIds = getFilePath(applyPeopleIds);
            dataList.applyPeopleIds = applyPeopleIds;

            //   配偶身份证图片
            let spouseIdPicIds = "";
            if (dataList.spouseIdPic1) {
              spouseIdPicIds += dataList.spouseIdPic1 + ",";
            }
            if (dataList.spouseIdPic2) {
              spouseIdPicIds += dataList.spouseIdPic2;
            }
            spouseIdPicIds = getFilePath(spouseIdPicIds);
            dataList.spouseIdPicIds = spouseIdPicIds;

            // 棚圈照片
            let shedPicList = getFilePath(dataList.shedPic);
            dataList.shedPicList = shedPicList;

            // 养殖活畜照片
            let livestockPicList = getFilePath(dataList.livestockPic);
            dataList.livestockPicList = livestockPicList;

            // 申请人房屋照片
            let housesPicList = getFilePath(dataList.housesPic);
            dataList.housesPicList = housesPicList;

            // 其它照片
            let otherPicList = getFilePath(dataList.otherPic);
            dataList.otherPicList = otherPicList;

            // 负债银行
            if (dataList.debtExplain) {
              const debtExplain=JSON.parse(dataList.debtExplain||"[{}]")
              let omll = [];
              debtExplain.forEach((item) => {
                omll.push(item.name);
              });
              dataList.debtExplainStr = omll.toString();
            }
            console.log('878787：',dataList.debtExplainStr);
            

            this.info = dataList;
          } else {
            this.$message.error(res.message);
          }
        })
        .catch((err) => {
          this.info = null;
          console.log("err", err);
        });
    },
    close() {
      this.$emit("close");
    },
  },
};
</script>

<style lang="scss" scoped>
.fieldList {
  .el-dialog__header {
    background-color: #f4f4f4;
  }

  .el-dialog__footer {
    text-align: center;
  }

  .selectWidth {
    width: 100%;
  }
}
.head-title {
  font-size: 14px;
  color: #333;
  margin-bottom: 10px;
}
.qihsd {
  color: red;
}
.qianmad {
  height: 50px;
  margin-top: -20px;
}
.mlt20 {
  margin-left: 20px;
}
.filitem {
  padding: 5px 0px;
}
.f {
  display: flex;
}
.mb10 {
  margin-bottom: 10px;
}
.cksdare {
  font-size: 12px;
  color: #666;
  font-weight: 500;
  margin-top: 2px;
}
.empty {
  text-align: center;
}
</style>

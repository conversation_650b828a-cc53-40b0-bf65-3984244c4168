<!--  -->
<template>
    <div class="setmain">
      <el-descriptions title="被监管方信息" :column="3">
        <el-descriptions-item label="姓名">{{form.applyName}}</el-descriptions-item>
        <el-descriptions-item label="联系电话">{{form.applyPhone}}</el-descriptions-item>
        <el-descriptions-item label="身份证号">{{showCarId(form.applyCardNo)}}</el-descriptions-item>
        <el-descriptions-item label="家庭住址" >{{handelAddress(form.applyAddress)}}</el-descriptions-item>
      </el-descriptions>
      <el-descriptions title="委托方信息" class="mtop" :column="3">
        <el-descriptions-item label="企业名称">{{form.bankInfo.companyName}}</el-descriptions-item>
        <el-descriptions-item label="企业类型">{{companyTypeName(form.bankInfo.companyType)}}</el-descriptions-item>
        <el-descriptions-item label="营业执照编号">{{form.bankInfo.businessLicenseNo}}</el-descriptions-item>
        <el-descriptions-item
          label="地址"
        >{{form.bankInfo.provinceName}}{{form.bankInfo.cityName}}{{form.bankInfo.countyName}}</el-descriptions-item>
        <el-descriptions-item label="法人">{{form.bankInfo.realName}}</el-descriptions-item>
        <el-descriptions-item label="法人电话">{{form.bankInfo.phoneNumber}}</el-descriptions-item>
        <el-descriptions-item label="身份证号">{{showCarId(form.bankInfo.idCard)}}</el-descriptions-item>
        <el-descriptions-item
          label="负责人姓名"
          v-if="form.bankInfo.corprateName"
        >{{form.bankInfo.corprateName}}</el-descriptions-item>
        <el-descriptions-item
          label="负责人电话"
          v-if="form.bankInfo.phonenumber"
        >{{form.bankInfo.phonenumber}}</el-descriptions-item>
        <el-descriptions-item label="详细地址">{{form.bankInfo.detailAddress}}</el-descriptions-item>
      </el-descriptions>
  
      <el-descriptions title="推荐企业" class="mtop" :column="3">
        <template v-for="flowItem in form.fundFlowList||[]" >
          <el-descriptions-item label="企业名称" >{{flowItem.recommendCompanyName}}</el-descriptions-item>
          <el-descriptions-item label="法人">{{flowItem.legalName}}</el-descriptions-item>
          <el-descriptions-item label="企业代码">{{flowItem.certNo}}</el-descriptions-item>
        </template>
      </el-descriptions>
    </div>
  </template>
  
  <script>
  export default {
    name: "basicInfoInfoForm",
    components: {},
    props: {
      info: {
        type: Object,
        default: () => {},
      },
    },
    data() {
      return {
        dialog: {
          open: false,
          title: "",
          id: "",
        },
        useLetterData: {
          open: false,
          title: "用信记录",
        },
        form: {
          bankInfo: {},
          placeName: "",
          yeassd: "",
          dakdata: "",
          datefw: "",
          rangeDate: "",
          metho: "",
        },
      };
    },
    created() {
      this.form = this.info;
    },
    computed: {
      companyTypeName() {
        return (val) => {
          if (val == 1) {
            return "养殖企业";
          } else if (val == 2) {
            return "屠宰企业";
          } else if (val == 3) {
            return "银行";
          } else if (val == 4) {
            return "保险公司";
          } else if (val == 5) {
            return "行业协会";
          } else if (val == 6) {
            return "物流公司";
          } else if (val == 7) {
            return "个体工商户";
          } else if (val == 8) {
            return "合作社";
          }
        };
      },
      showCarId() {
        return (p) => {
          if (!p) return "--";
          return this.carId(p);
        };
      },
      handelAddress(){
          return (value)=>{
              if(value){
              return  value.replace(/null/g, " ")
          }
  
          }
      },
    },
    mounted() {},
    methods: {
      close() {
        this.$emit("close");
      },
    },
  };
  </script>
  <style  scoped lang="scss">
  </style>
  
<template>
    <div>
        <el-dialog title="上传视频" :visible.sync="offLine.open" width="500px" :close-on-click-modal="false" @close="refuse" class="addOffLine" append-to-body>
            <div style="display: flex; justify-content: center;">
                <uploadVideo :type="2" v-model="form.offlineVideoUrlStr" boxWidth="200px" boxHeight="200px"></uploadVideo>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="refuse">关闭</el-button>
                <el-button type="primary" @click="submitForm">提交</el-button>
            </span>
        </el-dialog>
    </div>
</template>
  
<script>
import uploadVideo from "@/components/uploadVideo/index.vue";
import { superviseEditSome } from "@/api/ffs/supervisionSheet/livingSupervisionApi.js";
export default {
    components: {
        uploadVideo,
    },
    props: {
        offLine: {
            type: Object,
            default: () => {
                return {};
            },
        },
    },
    data() {
        return {
            form: {
                offlineVideoUrlStr: ''
            },
        };
    },
    created() { 
        this.form.offlineVideoUrlStr=this.offLine.obj.url
        this.form.superviseId=this.offLine.obj.superviseId
    },
    methods: {
        /** 提交按钮 */
        submitForm() {
            if(!this.form.offlineVideoUrlStr){
                this.$modal.msgError('请上传视频');
                return
            }
            this.$modal.loading("正在提交中，请耐心等待...");
            superviseEditSome(this.form)
                .then((response) => {
                    this.$modal.closeLoading();
                    if (response.code == 200) {
                        this.$emit("refresh");
                        this.$modal.msgSuccess("提交成功");
                        this.$emit("close");
                    }
                })
                .catch(() => {
                    this.$modal.closeLoading();
                });
        },
        refuse() {
            this.$emit("close");
        },
    },
};
</script>
  
<style lang="scss">
.addOffLine {
    .el-dialog__header {
        background-color: #f4f4f4;
    }

    //   .el-dialog__body {
    //     padding-top: 10px;
    //   }
    //   .el-input__inner {
    //     border: none;
    //   }
    .el-dialog__footer {
        text-align: center;
    }

    .selectWidth {
        width: 100%;
    }

    &-info {
        background: #f4f4f4;
        width: 540px;
        margin: 0 0 22px 120px;
        border-radius: 8px;
        padding: 10px 14px;

        &-row {
            padding: 5px 0;
        }
    }
}

.inputWidth {
    width: 100%;
}
</style>
  
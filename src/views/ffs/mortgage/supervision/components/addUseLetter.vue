<template>
  <div>
    <el-dialog
      :title="addLetterData.title"
      :visible.sync="addLetterData.open"
      :close-on-click-modal="false"
      @close="close"
      append-to-body
      class="fieldList"
    >
      <el-form :model="form" status-icon :rules="rules" ref="ruleForm">
        <el-form-item label="授信金额" prop="superviseAmount">
          <el-input v-model="form.superviseAmount" clearable placeholder="请输入授信金额" readonly>
            <template slot="append">万元</template>
          </el-input>
        </el-form-item>
        <el-form-item label="用信金额：" prop="superviseAmountReceive">
          <el-input v-model="form.superviseAmountReceive" clearable placeholder="请输入用信金额" readonly>
            <template slot="append">万元</template>
          </el-input>
        </el-form-item>
        <el-form-item label="追加用信金额：" prop="receiveAmount">
          <el-input
            v-model.number="form.receiveAmount"
            clearable
            placeholder="请输入追加用信金额"
            @blur="superviseAmountReceiveAmountBlur"
            :disabled="disabled"
            type="number"
            oninput="if(value.length>10)value=value.slice(0,10)"
          >
            <template slot="append">万元</template>
          </el-input>
        </el-form-item>
        <el-form-item label="追加服务费：" prop="appendServiceFee">
          <el-input
            v-model.number="form.appendServiceFee"
            clearable
            placeholder="请输入追加服务费"
            @blur="appendServiceFeeBlur"
            :disabled="disabled"
            type="number"
            oninput="if(value.length>10)value=value.slice(0,10)"
          >
            <template slot="append">元</template>
          </el-input>
        </el-form-item>
      </el-form>
      <div v-if="disabled" class="worn">没有可用追加用信额度啦</div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="close">取消</el-button>
        <el-button type="primary" @click="submitForm" v-if="!disabled"  v-hasPermi="['mortgage:supervision:add:quota']">提交</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { addSuperviseamount } from "@/api/ffs/mortgage/supervision.js";

export default {
  props: {
    addLetterData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    // 默认价格
    const twoPoint = (rule, value, callback) => {
      if (/^\d+\.?\d{0,2}$/.test(value)) {
        if (
          value.toString().indexOf(".") == "-1" &&
          value.length > 1 &&
          value.slice(0, 1) == "0"
        ) {
          return callback(new Error("最多包含两位⼩数的正数且不能为以0开头"));
        }
        callback();
      } else {
        return callback(new Error("最多包含两位⼩数的正数且不能为以0开头"));
      }
    };
    return {
      form: {
        superviseType:'2',
        superviseId: "",
        operatorId: "",
        operatorName: "",
        operatorPhone: "",
        superviseAmount: "", //监管金额
        superviseAmountReceive: "", //当前用信总金额
        superviseServiceFee: "", //监管总服务费
        receiveAmount: "", //本次追加用信金额
        appendServiceFee: "", //本次追加服务费
        dayServiceFee:"",//日服务费
        operationType:"0"//操作类型，0追加，1修改
      },

      disabled: false,

      rules: {
        receiveAmount: [
          { required: true, message: "请填写追加用信金额", trigger: "blur" },
          {
            validator: twoPoint,
            message: "请填写正确有效的追加用信金额",
            trigger: "blur",
          },
        ],
        appendServiceFee: [
          { required: true, message: "请填写追加服务费金额", trigger: "blur" },
          {
            validator: twoPoint,
            message: "请填写正确有效的追加服务费金额",
            trigger: "blur",
          },
        ],
      },
    };
  },
  created() {
    const { userId, userName, phonenumber } = this.$store.state.user.user;
    console.log("userId", this.$store.state.user.user);
    const {
      superviseId,
      superviseAmount,
      dayServiceFee,
      superviseAmountReceive = 0,
      superviseServiceFee = 0,
    } = this.addLetterData.info;

    this.form.operatorId = userId;
    this.form.operatorName = userName;
    this.form.operatorPhone = phonenumber;

    this.form.superviseId = superviseId;
    this.form.superviseAmount = superviseAmount;
    this.form.superviseAmountReceive = superviseAmountReceive || 0;
    this.form.superviseServiceFee = superviseServiceFee || 0;
    this.form.receiveAmount = superviseAmount - superviseAmountReceive;
     this.form.dayServiceFee = dayServiceFee;
    if (superviseAmount - superviseAmountReceive == 0) {
      this.disabled = true;
    }
  },
  methods: {
    //     追加用信金额
    superviseAmountReceiveAmountBlur(val) {
      const blurVal = val.target.value * 1;
      const surplusQuota =
        this.form.superviseAmount - this.form.superviseAmountReceive;
      if (blurVal > surplusQuota) {
        this.form.receiveAmount = surplusQuota;
        this.$message({
          message: "追加用信金额不能大于最多可用信金额：" + surplusQuota,
          type: "error",
        });
      }
    },

    appendServiceFeeBlur(val) {
      const blurVal = val.target.value * 1;
      if (this.form.receiveAmount == "") {
        this.$message({
          message: "请先输入追加用信金额",
          type: "error",
        });
        this.form.appendServiceFee = "";
        return;
      }
      if (blurVal < 0) {
        this.$message({
          message: "追加服务费金额不能小于0",
          type: "error",
        });
        return;
      }
      const surplusQuota = this.form.receiveAmount * 10000;
      if (blurVal > surplusQuota) {
        this.form.appendServiceFee = surplusQuota;
        this.$message({
          message: "追加服务费金额不能大于追加用信金额：" + surplusQuota + "元",
          type: "error",
        });
      }
    },

    close() {
      this.$emit("close");
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["ruleForm"].validate((valid) => {
        if (valid) {
          const {
            superviseAmount,
            superviseAmountReceive,
            receiveAmount,
            appendServiceFee,
          } = this.form;
          this.$confirm(
            `
            <strong style="color:red">请您确认今天之前的所有还款数据已经全部录入系统！追加完成后，还款日期在今天之前的数据将无法录入（包含今天）。</strong>
      <div>授信金额：${superviseAmount}万</div>
      <div>用信金额：${superviseAmountReceive}万</div>
      <div>追加用信金额：${receiveAmount}万</div>
      <div>追加服务费：${appendServiceFee}元</div>
       <strong>请确认本次追加是否准确，追加之后不能修改和删除</strong>
     
      `,
            "确认信息",

            {
              distinguishCancelAndClose: true,
              dangerouslyUseHTMLString: true,
              confirmButtonText: "确定",
              cancelButtonText: "取消",
            }
          ).then(() => {
            this.$modal.loading("正在提交中，请耐心等待...");
            addSuperviseamount(this.form)
              .then((response) => {
                this.$modal.closeLoading();
                if (response.code == 200) {
                  this.$modal.msgSuccess("提交成功");
                  this.open = false;
                  this.$emit(
                    "refresh",
                    this.form.receiveAmount,
                    this.form.appendServiceFee
                  );
                  this.$emit("close");
                }
              })
              .catch(() => {
                this.$modal.closeLoading();
              });
          });
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.fieldList {
  .el-dialog__header {
    background-color: #f4f4f4;
  }

  .el-dialog__footer {
    text-align: center;
  }

  .selectWidth {
    width: 100%;
  }
}
.worn {
  text-align: center;
  color: red;
}
</style>

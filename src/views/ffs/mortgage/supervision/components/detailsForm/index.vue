<template>
  <div>
    <el-dialog
      :title="detailsFormData.title"
      :visible.sync="detailsFormData.open"
      width="1300px"
      :close-on-click-modal="false"
      @close="close"
      class="fieldList mortgagesp"
      :modal="modal"
    >
      <div class="syaddsya">
        <el-tag type="danger" effect="dark">{{superviseStatusName(info.superviseStatus)}}</el-tag>
      </div>

      <el-descriptions title="监管信息" :column="3">
        <el-descriptions-item label="监管单号">{{detailsFormData.superviseId}}</el-descriptions-item>
        <el-descriptions-item label="合同编号">{{info.contractNo}}</el-descriptions-item>
        <el-descriptions-item label="服务费率(%)">{{info.superviseServiceRate}}</el-descriptions-item>
      </el-descriptions>

      <el-descriptions title :column="3" v-show="info.superviseStatus==3">
        <el-descriptions-item label="关闭人">{{info.closeUserName}}</el-descriptions-item>
        <el-descriptions-item label="关闭时间">{{info.closeTime}}</el-descriptions-item>
        <el-descriptions-item label="关闭原因">{{info.closeDesc}}</el-descriptions-item>
        <el-descriptions-item label="硬件是否已拆除">{{info.deviceFlag == 1 ? '是' : '否'}}</el-descriptions-item>
        <el-descriptions-item label="服务费是否已结清">{{info.superviseFeeFlag == 1 ? '是' : '否'}}</el-descriptions-item>
      </el-descriptions>

      <el-row :gutter="20" type="flex" justify="space-between" class="mbt20">
        <el-col :span="5">
          <el-card shadow="hover" class="carbix">
            <div class="carbixtx islink" @click="useLetterDig">用信金额（万）</div>
            <div class="yxjebx">
              <div class="carbixmys" @click="useLetterDig">{{info.superviseAmountReceive}}</div>
              <el-button
                type="primary"
                size="mini"
                @click="adduseLetter"
                class="yxbtadd"
                v-if="info.superviseStatus == 2"
                v-hasPermi="['mortgage:supervision:add:quota']"
              >追加用信</el-button>
            </div>
          </el-card>
        </el-col>
        <el-col :span="5">
          <el-card shadow="hover" class="carbix">
            <div class="carbixtx">授信金额（万）</div>
            <div class="carbixmys">{{info.superviseAmount}}</div>
          </el-card>
        </el-col>
        <el-col :span="5">
          <el-card shadow="hover" class="carbix">
            <div class="carbixtx">剩余未还（万元）</div>
            <div class="carbixmys">{{info.superviseBalanceDue}}</div>
          </el-card>
        </el-col>
        <el-col :span="5">
          <el-card shadow="hover" class="carbix">
            <div class="carbixtx">当前货值（万元）</div>
            <div class="carbixmys">{{info.reserveTotalPrice||'--'}}</div>
          </el-card>
        </el-col>
        <el-col :span="5">
          <el-card shadow="hover" class="carbix">
            <div class="carbixtx">当前库存（吨）</div>
            <div class="carbixmys">{{info.reserveTotalWeight||'--'}}</div>
          </el-card>
        </el-col>
        <el-col :span="5">
          <el-card shadow="hover" class="carbix">
            <div class="carbixtx">监管周期（月）</div>
            <div class="carbixmys">{{info.superviseLimit||'--'}}</div>
          </el-card>
        </el-col>
      </el-row>

      <div class="msabxs">
        <div v-if="info.superviseStart">
          <span class="msaitirts">监管时间：</span>
          <span class="msaitimss">{{info.superviseStart}}至{{info.superviseEnd}}</span>
        </div>

        <div>
          <span class="msaitirts">监管员：</span>
          <span class="msaitimss">{{info.supervisorName||'--'}}</span>
        </div>
        <div>
          <span class="msaitirts">客户经理：</span>
          <span class="msaitimss">{{info.loanOfficerName}}</span>
        </div>
        <div v-if="info.pastorName">
          <span class="msaitirts">畜牧师：</span>
          <span class="msaitimss">{{info.pastorName}}</span>
        </div>
        <div>
          <span class="msaitirts">创建时间：</span>
          <span class="msaitimss">{{info.createTime}}</span>
        </div>
      </div>

      <div class="head-title">
        <el-tabs v-model="stepsActive" type="card" stretch>
          <el-tab-pane v-for="(tab,index) in tbasList" :key="index" :label="tab"></el-tab-pane>
        </el-tabs>
      </div>
      <basic-info v-show="stepsActive==0" :info="info" />
      <goods-info v-show="stepsActive==1" :info="info" />
      <warehouse-info v-show="stepsActive==2" :motionType="1" :info="info"></warehouse-info>
      <warehouse-info v-show="stepsActive==3" :motionType="2" :info="info"></warehouse-info>
      <stock-info v-show="stepsActive==4" :info="info"></stock-info>
      <regulatory-log v-show="stepsActive==5" :info="info" />
      <inspection-records v-show="stepsActive==6" :info="info" :sourceType="1" />
      <paymen-history v-if="stepsActive==7" :info="info" />
      <!-- <video-surveillance v-show="stepsActive==8" :info="info" /> -->
      <emergency-contact v-if="stepsActive==8" :info="info" />
      <supervisor-set v-if="stepsActive==9" :info="info" />
      <postPoneRecord v-show="stepsActive==10" :superviseId="info.superviseId"></postPoneRecord>
      <clockIn v-if="stepsActive==11" :source="false" :applyId="detailsFormData.applyId"></clockIn>
      <useLetterl-log
        :useLetterData="useLetterData"
        @close="useLetterData.open=false"
        v-if="useLetterData.open"
      />

      <add-use-letter
        :addLetterData="addLetterData"
        @close="addLetterData.open=false"
        v-if="addLetterData.open"
        @refresh="refreshReceiveAmount"
      />

      <span slot="footer" class="dialog-footer" v-show="!detailsFormData.disable">
        <el-button type="info" @click="close">取消</el-button>
      </span>
    </el-dialog>
  </div>
</template>



<script>
import basicInfo from "./basicInfo"; //基本信息
import goodsInfo from "./goodsInfo"; //质押商品信息
import warehouseInfo from "./warehouseInfo"; //出库记录
import stockInfo from "./stockInfo"; //库存查询
import regulatoryLog from "./regulatoryLog"; //监管日志
import inspectionRecords from "./inspectionRecords"; //巡检记录
import paymenHistory from "./paymenHistory"; //还款记录
import videoSurveillance from "./videoSurveillance"; //视频监控
import emergencyContact from "@/views/ffs/supervisionSheet/livingSupervision/components/detailsForm/emergencyContact"; //紧急联系人
import supervisorSet from "@/views/ffs/supervisionSheet/livingSupervision/components/detailsForm/supervisorSet"; //监管员设置
import useLetterlLog from "@/views/ffs/mortgage/supervision/components/useLetterlLog.vue"; //用信记录
import addUseLetter from "@/views/ffs/mortgage/supervision/components/addUseLetter.vue"; //追加用信金额
import postPoneRecord from './postPoneRecord.vue'
import clockIn from '../../../../../xmb/clockIn/index.vue'
import {
  superviseStatus,
} from "@/views/ffs/supervisionSheet/livingSupervision/utils/formatVal.js";
export default {
  name: "detailsFormIndex",
  components: {
    basicInfo,
    goodsInfo,
    warehouseInfo,
    stockInfo,
    regulatoryLog,
    inspectionRecords,
    paymenHistory,
    emergencyContact,
    supervisorSet,
    videoSurveillance,
    useLetterlLog,
    addUseLetter,
    postPoneRecord,
    clockIn
  },
  props: {
    modal:{
        type:Boolean,
        default:true
    },
    detailsFormData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      loading: false,
      tbasList: [
        "基本信息",
        "质押商品信息",
        "入库记录",
        "出库记录",
        "库存查询",
        "监管日志",
        "巡检记录",
        "还款记录",
        "紧急联系人",
        "监管员设置",
        "续单记录",
        // "打卡记录"
      ],
      form: {
        fileName: "",
        datetime: "",
      },
      useLetterData: {
        open: false,
        title: "用信记录",
      },
      addLetterData: {
        open: false,
        title: "追加用信金额",
        info: {},
      },
      stepsActive: "",
      info: {
        bankInfo: {},
      },
    };
  },
  created() {
    this.info = this.detailsFormData.info || {};
    this.stepsActive = this.detailsFormData.stepsActive || "";
  },
  computed: {
    superviseStatusName() {
      return (val) => {
        return superviseStatus(val);
      };
    },
  },
  methods: {
    close() {
      this.$emit("close");
    },
    useLetterDig() {
      this.useLetterData.rowData = this.info;
      this.useLetterData.type = "info";
      this.useLetterData.open = true;
    },
    adduseLetter() {
      this.addLetterData.open = true;
      this.addLetterData.info = this.detailsFormData.info;
    },
    refresh() {
      console.log("刷新");
    },
    refreshReceiveAmount(receiveAmount, appendServiceFee) {
      this.info.superviseAmountReceive =
        this.info.superviseAmountReceive * 1 + receiveAmount * 1;
      this.info.superviseServiceFee =
        this.info.superviseServiceFee * 1 + appendServiceFee * 1;
    },
  },
};
</script>

<style lang="scss" >
.fieldList {
  .el-dialog__header {
    background-color: #f4f4f4;
  }

  .el-dialog__footer {
    text-align: center;
  }

  .selectWidth {
    width: 100%;
  }
}
.mortgagesp {
  .el-card__body {
    padding-left: 8px;
    padding-right: 8px;
  }
}
.carbix {
  text-align: center;
}
.carbixtx {
  color: #999;
  margin-bottom: 14px;
}
.carbixmys {
  color: #333;
  font-weight: 600;
  line-height: 30px;
}
.msabxs {
  display: flex;
  justify-content: flex-end;
  margin-top: 30px;
  border-bottom: 1px solid #eee;
  padding-bottom: 20px;
}
.msabxs > div {
  margin-left: 30px;
}
.msaitirts {
  color: #333;
}
.msaitimss {
  color: #999;
}
.mbt20 {
  margin-top: 20px;
}
.syaddsya {
  position: absolute;
  right: 20px;
}
.yxjebx {
  display: flex;
  align-items: center;
  justify-content: center;
}
.yxbtadd {
  margin-left: 10px;
}
.islink {
  text-decoration: underline;
  text-decoration-color: blue !important;
  color: blue !important;
  cursor: pointer;
}
</style>

<template>
  <div>
    <el-dialog
      :title="recordsInfoDig.title"
      :visible.sync="recordsInfoDig.open"
      width="1250px"
      :close-on-click-modal="false"
      @close="close"
      class="fieldList"
      append-to-body
    >
      <span
        class="qihsd mlt20"
        style="float:right;overflow: hidden"
      >状态：{{info.inspectStatus==1?'正常':'异常'}}</span>
      <el-descriptions :column="4" :contentStyle="CS" :label-style="LS">
        <div slot="title">
          <div>{{info.storehouseName}} {{(info.storehouseNo)}}</div>
          <div class="cksdare">{{info.storehouseAddress}}</div>
        </div>

        <el-descriptions-item label="巡检员">{{info.inspectByName}}</el-descriptions-item>
        <el-descriptions-item label="巡检时间">{{info.inspectTime?.split(' ')[0]||''}}</el-descriptions-item>

        <el-descriptions-item label="巡检员签字">
          <el-image
          v-show="info.signUrl"
            class="qianmad"
            :src="info.signUrl"
            :preview-src-list="srcList"
            @click="bigImage(info.signUrl)"
          ></el-image>
        </el-descriptions-item>
        <el-descriptions-item label="巡检说明" v-if="info.remark">{{info.remark}}</el-descriptions-item>
      </el-descriptions>
      <el-descriptions v-if="sourceType==2" :column="3" :contentStyle="CS" :label-style="LS">
        <div slot="title" style="display:flex;justify-content: space-between ;width: 1210px;">
          <span>出入库频率</span>
        </div>
        <el-descriptions-item label="出入库频率">{{ formartFrequency(info.frequency) }}</el-descriptions-item>
      </el-descriptions>
      <el-descriptions :column="3" :contentStyle="CS" :label-style="LS">
        <div slot="title" style="display:flex;justify-content: space-between ;width: 1210px;">
          <span>销售渠道及本月销量</span>
        </div>
        <el-descriptions-item v-if="info.orderMarket" label="订单">{{ info.orderMarket }} 元/月</el-descriptions-item>
        <el-descriptions-item v-if="info.commerceMarket" label="电商">{{ info.commerceMarket }} 元/月</el-descriptions-item>
        <el-descriptions-item v-if="info.cateringMarket" label="餐饮终端店">{{ info.cateringMarket }} 元/月</el-descriptions-item>
        <el-descriptions-item v-if="info.useMarket" label="自有终端店">{{ info.useMarket }} 元/月</el-descriptions-item>
        <el-descriptions-item v-if="info.otherMarket" label="其它">{{ info.otherMarket }} 元/月</el-descriptions-item>
      </el-descriptions>
      

      <el-row class="filitem">
        <el-col>
          <el-image
            style="width: 150px; height: 120px; padding: 0 5px"
            v-for="(item,index) in info.inspectImgUrl"
            :key="index"
            :src="item"
            :preview-src-list="srcList"
            @click="bigImage(picPath(item))"
          ></el-image>

          <video
            style="width: 150px; height: 120px; padding: 0 5px"
            v-for="(item,index) in info.inspectVedioUrl"
            :key="index"
            :src="item"
            controls="controls"
          ></video>
        </el-col>
      </el-row>

      <el-row
        :gutter="10"
        class="mb8"
        type="flex"
        justify="end"
        v-show="info.inspectDetailList?.length"
      >
        <el-button type="primary" icon="el-icon-top" size="mini" @click="handleExportQuot">导出</el-button>
      </el-row>

      <el-table :data="info.inspectDetailList" style="width: 100%" border>
        <el-table-column prop="positionName" label="货区名称"></el-table-column>
        <el-table-column prop="commodityName" label="商品名称"></el-table-column>
        <el-table-column prop="commodityNo" label="商品编码"></el-table-column>
        <el-table-column prop="reserveNum" label="库存数量"></el-table-column>
        <el-table-column prop="inventoryNum" label="盘点数量"></el-table-column>
        <el-table-column prop="inventoryNum" label="盘点差异" :formatter="countVariance"></el-table-column>
        <el-table-column prop="sampleNum" label="抽检数量"></el-table-column>
        <el-table-column prop="commodityQuality" label="商品质量" :formatter="commodityQualityName"></el-table-column>
        <el-table-column prop="remark" label="备注信息"></el-table-column>
        <el-table-column prop="sampleImgUrl" label="巡检图片">
          <template slot-scope="scope">
            <div
              style="cursor: pointer;color: #5672FA;"
              v-if="scope.row.sampleImgUrl != ''"
              @click="bigImg(scope.row.sampleImgUrl)"
            >巡检图片</div>
          </template>
        </el-table-column>
        <el-table-column prop="inspectVedioUrl" label="巡检视频">
          <template slot-scope="scope">
            <div
            style="cursor: pointer;color: #5672FA;"
              v-if="scope.row.inspectVedioUrl != ''"
              @click="bigVide(scope.row.inspectVedioUrl)"
            >巡检视频</div>
          </template>
        </el-table-column>
      </el-table>

      <span slot="footer" class="dialog-footer">
        <el-button type="info" @click="close">关闭</el-button>
      </span>
    </el-dialog>

    <el-image-viewer
      :zIndex="3001"
      v-if="showViewer"
      :on-close="() => { showViewer = false;}"
      :url-list="imgList"
    />

    <el-dialog
      class="fieldList"
      title="查看视频"
      :visible.sync="dialogVisible"
      width="70%"
      append-to-body
    >
      <video
        v-for="(item,index) in videList"
        :src="item"
        :key="index"
        width="260"
        height="260"
        controls
        style="margin-right:30px"
      ></video>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import ElImageViewer from "element-ui/packages/image/src/image-viewer";
import { fileStreamToFile } from "@/utils/exportQuot.js";
import {
  superviseinspectInfo,
  mortgageInspectionExport,
} from "@/api/ffs/mortgage/supervision";
import { getFilePath, picPath ,getDateTime} from "@/utils/east.js";

export default {
  commodityQuality: "inspectionRecordsInfo",
  components: {
    ElImageViewer,
  },
  props: {
    recordsInfoDig: {
      type: Object,
      default: () => {
        return {
          info: {},
        };
      },
    },
    sourceType: {
      typeof: [String, Number],
      default: 1
    }
  },
  data() {
    return {
      CS: {
        "max-width": "300px", //最小宽度
        "word-break": "break-all", //过长时自动换行
      },
      LS: {
        "word-break": "keep-all",
        color: "#000",
      },
      dialogVisible: false,
      showViewer: false,
      imgList: [],
      videList: [],
      queryParams: { fileName: "", superviseType: "" },
      form: {
        fileName: "",
        datetime: "",
        imagesArr: [],
        videosArr: [],
      },

      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 表格数据
      list: [],
      srcList: [""],
      info: {
        inspectDetailList: [],
      },
    };
  },
  created() {
    this.getDetails();
  },
  computed: {
    countVariance() {
      return (row, com, val) => {
        return row.sampleNum&&!(row.inventoryNum)?'':(row.inventoryNum - row.reserveNum);
      };
    },
    commodityQualityName() {
      return (row, com, val) => {
        if (val == 1) {
          return "合格";
        } else if (val == 2) {
          return "不合格";
        }
      };
    },
    formartFrequency(){
      return (val) => {
        if(!val) return
        const frequencylMapping = { 1: "每日", 2: "每周", 3: "每月", 4: "不定期出库"};
        return val.split(',').map(type => frequencylMapping[type]).join('、');
      }
    }
  },
  methods: {
    bigImage(url) {
      this.srcList = [];
      this.srcList.push(url);
    },
    bigImg(url) {
      this.imgList = [];
      this.imgList = getFilePath(url);
      this.showViewer = true;
    },
    bigVide(url) {
      this.videList = [];
      this.videList = getFilePath(url);
      this.dialogVisible = true;
    },
    //导出
    handleExportQuot() {
      mortgageInspectionExport({
        ids: [this.recordsInfoDig.inspectId],
      }).then((res) => {
        fileStreamToFile(this.info.storehouseName + this.info.storehouseNo + "盘点"+getDateTime(), res);
      });
    },
    /** 仓单巡检详情 */
    getDetails() {
      superviseinspectInfo({ ids: [this.recordsInfoDig.inspectId] }).then(
        (res) => {
          this.loading = false;
          if (res.code == 200) {
            const dataList = res.result || { inspectDetailList: [] };
            dataList.inspectImgUrl = getFilePath(dataList.inspectImgUrl);
            dataList.inspectVedioUrl = getFilePath(dataList.inspectVedioUrl);
            dataList.signUrl = picPath(dataList.signUrl);

            this.info = dataList;
          } else {
            this.$message.error(res.message);
          }
        }
      );
    },
    close() {
      this.$emit("close");
    },
  },
};
</script>

<style lang="scss">
:deep(.el-descriptions__header){
  margin-bottom: 10px;
}

.fieldList {
  .el-dialog__header {
    background-color: #f4f4f4;
  }

  .el-dialog__footer {
    text-align: center;
  }

  .selectWidth {
    width: 100%;
  }
}
.head-title {
  font-size: 16px;
  color: #999;
  margin-bottom: 20px;
}
.qihsd {
  color: red;
}
.qianmad {
  height: 50px;
  margin-top: -20px;
}
.mlt20 {
  margin-left: 20px;
}
.filitem {
  padding: 5px 15px;
}
.f {
  display: flex;
}
.cksdare {
  font-size: 12px;
  color: #666;
  font-weight: 500;
  margin-top: 2px;
}

.fieldList {
  .el-dialog__header {
    background-color: #f4f4f4;
  }

  .el-dialog__footer {
    text-align: center;
  }

  .selectWidth {
    width: 100%;
  }
}
.head-title {
  font-size: 16px;
  color: #999;
  margin-bottom: 20px;
}
.head-title span {
  color: red;
}
</style>

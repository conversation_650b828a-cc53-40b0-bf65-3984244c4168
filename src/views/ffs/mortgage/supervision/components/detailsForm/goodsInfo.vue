<template>
  <div>
    <el-row :gutter="10" class="mb8">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="120px">
        <el-row class="form_row">
         <el-col class="form_col">
        <el-form-item label="质押品类型：" prop="mortgageType">
          <el-select v-model="queryParams.mortgageType" clearable class="selectWidth">
            <el-option
              v-for="dict in dict.type.ffs_mortgage_type"
              :key="dict.label"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="商品品类：" prop="commodityCategory">
          <el-select v-model="queryParams.commodityCategory" clearable class="selectWidth">
            <el-option
              v-for="dict in dict.type.ffs_commodity_category"
              :key="dict.label"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="加工方式：" prop="processWay">
          <el-select v-model="queryParams.processWay" clearable class="selectWidth">
            <el-option
              v-for="dict in dict.type.ffs_machining_type"
              :key="dict.label"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="商品类型：" prop="commodityType">
          <el-select v-model="queryParams.commodityType" clearable class="selectWidth">
            <el-option
              v-for="dict in dict.type.ffs_commodity_type"
              :key="dict.label"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="商品编码：" prop="commodityNo">
          <el-input v-model="queryParams.commodityNo" placeholder="请输入商品编码" clearable @keyup.enter.native="handleQuery"/>
        </el-form-item>
        <el-form-item label="商品名称：" prop="commodityName">
          <el-input v-model="queryParams.commodityName" placeholder="请输入商品名称" clearable @keyup.enter.native="handleQuery"/>
        </el-form-item>
        </el-col>
        </el-row>
        <el-row style="margin-left: 120px;">
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          <template >
                <el-button type="text" @click="packUp" >
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <i
                  :class="{ 'el-icon-arrow-down': !toggleSearchStatus, 'el-icon-arrow-up': toggleSearchStatus }"
                ></i>
              </el-button>
              </template>
        </el-form-item>
        </el-row>
      </el-form>
    </el-row>

    <el-row type="flex">
      <el-col :span="4">质押商品数量：{{info.mortgageCount}}</el-col>
      <el-col :span="4">质押重量：{{info.mortgageTotalWeight}}吨</el-col>
      <el-col :span="4">质押货值：{{info.mortgageTotalAmount}}万元</el-col>
      <el-col :span="2"></el-col>
      <el-col :span="4">当前商品数量：{{info.reserveTotalNum}}</el-col>
      <el-col :span="4">当前质押重量：{{info.reserveTotalWeight}}吨</el-col>
      <el-col :span="4">当前质押货值：{{info.reserveTotalPrice}}万元</el-col>
    </el-row>

    <el-table :data="list" stripe style="width: 100%;margin-top:10px" v-loading="loading" border>
      <el-table-column type="index" label="序号" width="50" fixed="left"></el-table-column>
      <el-table-column prop="commodityCategoryName" label="商品品类" width="80" fixed="left"></el-table-column>
      <el-table-column prop="commodityTypeName" label="商品类型" fixed="left"></el-table-column>
      <el-table-column prop="commodityName" label="商品名称" fixed="left" width="120"></el-table-column>
      <el-table-column prop="commodityNo" label="商品编码"></el-table-column>
      <el-table-column
        label="商品规格"
        prop="commodityUnitName"
        :formatter="getCommoditySpecificationsName"
        width="120px"
      ></el-table-column>
      <el-table-column prop="reserveNum" label="库存数量" width="80"></el-table-column>
      <el-table-column prop="reserveWeight" label="库存重量(吨)" width="110"></el-table-column>
      <el-table-column prop="processWayName" label="加工方式" width="78"></el-table-column>
      <el-table-column prop="mortgageTotalPrice" label="库存货值（元）" width="140" align="center"></el-table-column>
      <el-table-column prop="mortgageRate" label="质押率%" width="78"></el-table-column>
      <el-table-column prop="mortgagePrice" label="质押单价（元）" width="120"></el-table-column>
      <el-table-column prop="mortgageNum" label="质押数量" width="80"></el-table-column>
      <el-table-column prop="mortgageWeight" label="质押重量（千克）" width="150"></el-table-column>
      <el-table-column prop="mortgageTotalPrice" label="质押货值（元）" width="150"></el-table-column>
      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-info"
            @click="openOutEnterHoseDig(scope.row,2)"
            style="margin-left:-6px"
          >出库记录</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-info"
            @click="openOutEnterHoseDig(scope.row,1)"
            style="margin-left:-8px"
          >入库记录</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <goodsOutEnterHoseDetails
      :outEnterHoseData="outEnterHoseData"
      @close="close"
      v-if="outEnterHoseData.open"
    />
  </div>
</template>

<script>
import { commodityListBuySuperviseId } from "@/api/ffs/mortgage/supervision.js";
import goodsOutEnterHoseDetails from "./goodsOutEnterHoseDetails"; //商品出入库详情
import {commoditySpecificationsName} from "./utils.js"
import { searchUi } from "@/utils/mixin/searchUi.js";
export default {
  name: "detailsGoodsInfo",
  mixins: [searchUi],
  dicts: [
    "ffs_mortgage_type",
    "ffs_commodity_category",
    "ffs_machining_type",
    "ffs_commodity_type",
  ],
  components: {
    goodsOutEnterHoseDetails,
  },
  props: {
    info: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      outEnterHoseData: {
        open: false,
        id: "",
        title: "",
      },
      queryParams: {
        mortgageType: undefined,
        searchValue: undefined,
        processWay: undefined,
        commodityCategory: undefined,
        mortgageType: undefined,
        commodityType: undefined,
        commodityNo: "",
        commodityName: "",
        pageNum: 1,
        pageSize: 10,
        createStart: "",
        createEnd: "",
      },
      form: {},

      time: undefined,

      // 遮罩层
      loading: false,
      // 总条数
      total: 0,
      // 表格数据
      list: [],
    };
  },
  created() {
    this.queryParams.superviseId = this.info.superviseId;
    this.form = this.info;
    this.getList();
  },

  computed: {
    getCommoditySpecificationsName() {
      return (row, com, val) => {
        return commoditySpecificationsName(row);
      };
    },
  },
  methods: {
    /** 查询列表 商品列表 */
    getList() {
      commodityListBuySuperviseId(this.queryParams).then((res) => {
        this.loading = false;
        if (res.code == 200) {
          this.list = res.result.list || [];
          this.total = Number(res.result.total || 0);
        } else {
          this.$message.error(res.message);
        }
      });
    },

    //打开出入库详情
    openOutEnterHoseDig(rowInfo, eq) {
      let title = "出库记录";
      if (eq == 1) {
        title = "入库记录";
      }
      this.outEnterHoseData.motionType = eq;
      this.outEnterHoseData.info = rowInfo;
      this.outEnterHoseData.title = title;
      this.outEnterHoseData.open = true;
    },

    /** 搜索按钮操作 */
    handleQuery() {
      if (this.time && this.time.length > 0) {
        this.queryParams.createStart = this.time[0];
        this.queryParams.createEnd = this.time[1];
      } else {
        this.queryParams.createStart = "";
        this.queryParams.createEnd = "";
      }
      this.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.time = [];
      this.queryParams.createStart = "";
      this.queryParams.createEnd = "";
      this.resetForm("queryForm");
      this.handleQuery();
    },

    //关闭弹框
    close() {
      this.outEnterHoseData.open = false;
      this.$emit("close");
    },
  },
};
</script>

<style lang="scss">
.fieldList {
  .el-dialog__header {
    background-color: #f4f4f4;
  }

  .el-dialog__footer {
    text-align: center;
  }

  .selectWidth {
    width: 100%;
  }
}
</style>

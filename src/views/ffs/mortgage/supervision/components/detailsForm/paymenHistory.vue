<template>
  <div>
    <el-row :gutter="10" class="mb8">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="100px">
        <el-row class="form_row">
         <el-col class="form_col">
        <!-- <el-form-item label="交易流水号：" prop="repaymentNo">
          <el-input v-model="queryParams.repaymentNo" placeholder="请输入交易流水号" clearable />
        </el-form-item> -->

        <el-form-item label="还款人：" prop="repaymentUserName">
          <el-input v-model="queryParams.repaymentUserName" placeholder="请输入还款人" clearable @keyup.enter.native="handleQuery"/>
        </el-form-item>

        <el-form-item label="联系电话：" prop="repaymentUserPhone">
          <el-input
            v-model="queryParams.repaymentUserPhone"
            placeholder="请输入联系电话"
            type="number"
            oninput="if(value.length>11)value=value.slice(0,11)"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
<!--
        <el-form-item label="身份证号：" prop="repaymentUserIdcard">
          <el-input
            v-model="queryParams.repaymentUserIdcard"
            placeholder="请输入身份证号"
            maxlength="18"
            clearable
          />
        </el-form-item> -->
<!--
        <el-form-item label="操作人：" prop="createBy">
          <el-input v-model="queryParams.createBy" placeholder="请输入操作人姓名" clearable @keyup.enter.native="handleQuery"/>
        </el-form-item> -->
        <el-form-item label="还款时间：">
          <el-date-picker
          style="width: 215px"
            v-model="time"
            value-format="yyyy-MM-dd hh:mm:ss"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
        </el-form-item>
        </el-col>
        </el-row>
        <el-row style="margin-left: 100px;">
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          <template v-if="toggleSearchDom">
                        <el-button type="text" @click="packUp" >
                        {{ toggleSearchStatus ? '收起' : '展开' }}
                        <i
                        :class="{ 'el-icon-arrow-down': !toggleSearchStatus, 'el-icon-arrow-up': toggleSearchStatus }"
                        ></i>
                    </el-button>
                    </template>
        </el-form-item>
    </el-row>
      </el-form>
    </el-row>

    <el-table :data="list" stripe style="width: 100%;margin-top:10px" v-loading="loading" border>
      <el-table-column type="index" label="序号" width="50"></el-table-column>
      <!-- <el-table-column prop="repaymentNo" label="交易流水号" width="160"></el-table-column> -->
      <el-table-column prop="repaymentUserName" label="还款人"></el-table-column>
      <el-table-column prop="repaymentUserPhone" label="联系电话" width="120"></el-table-column>
      <!-- <el-table-column prop="repaymentUserIdcard" label="身份证号" width="165"></el-table-column> -->
      <el-table-column
        prop="repaymentAmount"
        label="还款金额(本金/元)"
        width="140px"
        :formatter="repaymentAmount"
      ></el-table-column>
      <el-table-column prop="repaymentTime" label="还款时间"></el-table-column>
      <el-table-column prop="repaymentCertificateUrl" label="还款凭证">
        <template slot-scope="scope">
          <div
            style="cursor: pointer;color: #5672FA;"
            v-show="scope.row.repaymentCertificateUrl != ''"
            @click="bigImg(scope.row.repaymentCertificateUrl)"
          >查看凭证</div>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="上传时间" width="160"></el-table-column>
      <el-table-column prop="createUserName" label="操作人"></el-table-column>
      <el-table-column label="操作" align="center" fixed="right" width="120px">
        <template slot-scope="scope">
          <el-button
            v-hasPermi="['ffs:mortgage:payment:delete']"
            size="mini"
            type="text"
            @click="handleDelete(scope.row)"
            icon="el-icon-delete"
          >删除</el-button>
        </template>
<!--        v-hasPermi="['ffs:mortgage:payment:delete']"-->
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <goodsOutEnterHoseDetails
      :outEnterHoseData="outEnterHoseData"
      @close="close"
      v-if="outEnterHoseData.open"
    />

    <el-image-viewer
      :zIndex="3001"
      v-if="showViewer"
      :on-close="() => { showViewer = false;}"
      :url-list="imgList"
    />
  </div>
</template>

<script>
import { paymenHistoryList, deleteByRepayment } from "@/api/ffs/mortgage/supervision.js";
import { toFixed2 ,getFilePath} from "@/utils/east.js";
import ElImageViewer from "element-ui/packages/image/src/image-viewer";
import { searchUi } from "@/utils/mixin/searchUi.js";
export default {
  name: "paymenHistory",
  mixins: [searchUi],
  components: {
    ElImageViewer,
  },
  props: {
    info: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      outEnterHoseData: {
        open: false,
        id: "",
        title: "",
      },
      queryParams: {
        repaymentNo: "",
        repaymentUserName: "",
        repaymentUserPhone: "",
        repaymentUserIdcard: "",
        createBy: "",
        pageNum: 1,
        pageSize: 10,
        startTime: "",
        endTime: "",
      },
      form: {},

      time: undefined,

      // 遮罩层
      loading: false,
      // 总条数
      total: 0,
      // 表格数据
      list: [],
      imgList: [],
      showViewer: false,
    };
  },
  created() {
    console.log('superviseId======>>>>>>>>', this.info.superviseId)
    this.queryParams.superviseId = this.info.superviseId;
    this.form = this.info;
    this.getList();
  },

  computed: {
    repaymentAmount() {
      return (row, com, val) => {
        return toFixed2(val);
      };
    },
  },
  methods: {
    bigImg(url) {
      this.imgList = [];
      this.imgList=getFilePath(url);
      this.showViewer = true;
    },
    /** 查询列表 监管单还款记录 */
    getList() {
      paymenHistoryList(this.queryParams).then((res) => {
        this.loading = false;
        if (res.code == 200) {
          this.list = res.result.list || [];
          this.total = Number(res.result.total || 0);
        } else {
          this.$message.error(res.message);
        }
      });
    },

    /** 搜索按钮操作 */
    handleQuery() {
      if (this.time && this.time.length > 0) {
        this.queryParams.startTime = this.time[0];
        this.queryParams.endTime = this.time[1];
      } else {
        this.queryParams.startTime = "";
        this.queryParams.endTime = "";
      }
      this.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.time = [];
      this.queryParams.startTime = "";
      this.queryParams.endTime = "";
      this.resetForm("queryForm");
      this.handleQuery();
    },
    //关闭弹框
    close() {
      this.outEnterHoseData.open = false;
      this.$emit("close");
    },
    handleDelete(row){
      console.log(row)
      this.$modal.confirm('是否确认删除').then(()=> {
        deleteByRepayment({ids: [row.repaymentId]}).then(res => {
          if(res.code== 200) {
            this.$modal.msgSuccess("删除成功");
            this.getList();
           }
         })
        })
        .catch(() => {});
    }
  },
};
</script>

<style lang="scss">
.fieldList {
  .el-dialog__header {
    background-color: #f4f4f4;
  }

  .el-dialog__footer {
    text-align: center;
  }

  .selectWidth {
    width: 100%;
  }
}
</style>

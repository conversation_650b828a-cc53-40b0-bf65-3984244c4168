<template>
  <div>
    <el-row :gutter="10" class="mb8" v-if="sourcePage!='inspection'">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="100px">
        <el-row class="form_row">
         <el-col class="form_col">
        <el-form-item label="状态：" prop="inspectStatus">
          <el-select v-model="queryParams.inspectStatus" clearable class="selectWidth">
            <el-option label="全部" value />
            <el-option label="正常" value="1" />
            <el-option label="异常" value="2" />
          </el-select>
        </el-form-item>

        <el-form-item label="巡检员：" prop="inspectByName">
          <el-input v-model="queryParams.inspectByName" placeholder="请输入巡检员" clearable @keyup.enter.native="handleQuery"/>
        </el-form-item>

        <el-form-item label="巡检时间：">
          <el-date-picker
            v-model="time"
            style="width: 215px"
            value-format="yyyy-MM-dd"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
        </el-form-item>
        </el-col>
        </el-row>
        <el-row style="margin-left: 100px;">
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
        </el-row>
      </el-form>
    </el-row>

    <el-table :data="list" stripe style="width: 100%;margin-top:10px" v-loading="loading" border>
      <el-table-column type="index" label="序号" width="100"></el-table-column>
      <el-table-column prop="inspectStatus" label="状态" :formatter="statusName" width="100" align="center"></el-table-column>
      <el-table-column prop="inspectByName" label="巡检员" width="200"></el-table-column>
      <el-table-column prop="inspectTime" label="巡检时间" width="180" align="center">
        <template slot-scope="scope">
        <span v-show="scope.$index!=rowIndex" >{{ scope.row.inspectTime.split(' ')[0] }}</span>
        <el-date-picker ref="datePick" v-model="inspectTime" type="date" placeholder="选择日期" popper-class="date-style"
        v-if="rowIndex==scope.$index"  @change="changeTime"  @blur="leaveBlur" value-format="yyyy-MM-dd hh:mm:ss" class="picker" :picker-options="dateButton">
        </el-date-picker>
        </template>
      </el-table-column>
      <el-table-column prop="remark" label="巡检说明"></el-table-column>
      <el-table-column label="操作" align="center" width="160">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-info"
            @click="openRecordsInfoDig(scope.row.inspectId)"
          >详情</el-button>
          <template v-if="sourcePage!='inspection'">
            <el-button
            v-hasPermi="['ffs:supervision:inspectTime']"
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="editTime(scope.$index,scope.row.inspectTime,scope.row.inspectId)"
            >修改时间</el-button>
          </template>
          
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <inspectionRecordsInfo
      :recordsInfoDig="recordsInfoDig"
      :sourceType="sourceType"
      @close="close"
      v-if="recordsInfoDig.open"
    />
  </div>
</template>

<script>
import { superviseinspectPage,inspectTimeEdit } from "@/api/ffs/mortgage/supervision.js";
import inspectionRecordsInfo from "./inspectionRecordsInfo"; //巡检详情
export default {
  name: "inspectionRecords",
  components: {
    inspectionRecordsInfo,
  },
  props: {
    info: {
      type: Object,
      default: () => {},
    },
    params:{
      type: Object,
      default: () => {},
    },
    sourcePage:{
      type: String,
      default: '',
    },
    sourceType: {
      typeof: [String, Number],
      default: 1
    }
  },
  data() {
    var that = this;
    return {
        dateButton: {
      	time:'',
        shortcuts: [
          {
            text: "取消",
            onClick() {
                that.$refs.datePick.handleClose();
            },
          },
          {
            text: "确定",
            onClick() {
              that.selectDate()
              //通过$ref设置 点击确定时，关闭日期选择的弹窗
            },
          },
        ],
      },
      inspectTime:'',//巡检事件
      inspectId:'',//巡检id
      rowIndex:null,
      recordsInfoDig: {
        open: false,
        id: "",
        title: "",
      },
      queryParams: {
        inspectStatus: undefined,
        inspectByName: undefined,
        processWay: undefined,
        commodityCategory: undefined,
        mortgageType: undefined,
        commodityType: undefined,
        pageNum: 1,
        pageSize: 10,
        startTime: "",
        endTime: "",
      },
      form: {},

      time: undefined,

      // 遮罩层
      loading: false,
      // 总条数
      total: 0,
      // 表格数据
      list: [],
    };
  },
  created() {
    this.queryParams.superviseId = this.info.superviseId;
    this.form = this.info;
    if(this.sourcePage=='inspection'){
      // 来源页面：巡检统计
      this.queryParams.inspectName=this.params.inspectName
      this.queryParams.inspectStart=this.params.inspectStart
      this.queryParams.inspectEnd=this.params.inspectEnd
    }
    this.getList();
  },

  computed: {
    unitName() {
      return (row, com, val) => {
        return (
          row.commoditySpecifications +
          row.commodityUnitName +
          "/" +
          row.inventoryUnitName
        );
      };
    },
    statusName() {
      return (row, com, val) => {
        if (val == 1) {
          return "正常";
        } else if (val == 2) {
          return "异常";
        }
      };
    },
  },
  methods: {
    leaveBlur(){
        if(!this.$refs.datePick.pickerVisible){
            this.rowIndex=null
        }
    },
    //巡检时间修改
    editTime(index,time,inspectId){
        this.rowIndex=index
        this.inspectTime=time
        this.inspectId=inspectId
        this.$nextTick(()=>{
            this.$refs.datePick.pickerVisible =true;
        })

    },
    selectDate(){
        inspectTimeEdit({inspectId:this.inspectId,inspectTime:this.inspectTime}).then((res)=>{
            if(res.code==200){
                this.$refs.datePick.handleClose();
                this.$modal.msgSuccess("修改成功");
                this.getList()
            }
        })
    },
    changeTime(){
        this.$refs.datePick.pickerVisible =true;
    },


    /** 查询列表 商品列表 */
    getList() {
      superviseinspectPage(this.queryParams).then((res) => {
        this.loading = false;
        if (res.code == 200) {
        //   console.log("查询仓单巡检列表:", res);
          this.list = res.result.list || [];
          this.total = Number(res.result.total || 0);
        } else {
          this.$message.error(res.message);
        }
      });
    },

    //打开巡检详情
    openRecordsInfoDig(inspectId) {
      this.recordsInfoDig.inspectId = inspectId;
      this.recordsInfoDig.title = '巡检详情';
      this.recordsInfoDig.open = true;
    },

    /** 搜索按钮操作 */
    handleQuery() {
      if (this.time && this.time.length > 0) {
        this.queryParams.startTime = this.time[0]+' 00:00:00';
        this.queryParams.endTime = this.time[1]+' 23:59:59';
      } else {
        this.queryParams.startTime = "";
        this.queryParams.endTime = "";
      }
      this.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.time = [];
      this.queryParams.startTime = "";
      this.queryParams.endTime = "";
      this.resetForm("queryForm");
      this.handleQuery();
    },

    //关闭弹框
    close() {
      this.recordsInfoDig.open = false;
      this.$emit("close");
    },
  },
};
</script>

<style lang="scss">
.picker{
        width: 150px !important;
}
.fieldList {
  .el-dialog__header {
    background-color: #f4f4f4;
  }

  .el-dialog__footer {
    text-align: center;
  }

  .selectWidth {
    width: 100%;
  }
}



/* 日期控件样式 */
/* input框placeholder样式 */
     /* 日期选择框面板样式 */
     .date-style.el-date-picker, .date-style.el-picker-panel{
        width: 340px !important;
        height: 380px !important;
        border: 1px solid rgba(255, 255, 255, 0.3);
        margin: 0;
        font-size: 1rem !important;
        font-family: 'HelveticaNeue-Medium, HelveticaNeue';
        font-weight: 500;
          /* 日期选择器左侧快捷方式样式重写 定位到底部 */
        .el-picker-panel__sidebar {
            width: 320px;
            height: 134px;
            padding: 0;
            display: flex;
            justify-content: right;
            align-items: center;
            margin-top: 283px;
            background: rgba(0, 0, 0, 0) !important;
            border-right: 1px solid rgba(0, 0, 0, 0);
        }

        .el-picker-panel__sidebar :nth-child(2) {
            background-color: #3AA0B8;
            border: none;
            color: #FFFFFF;
        }

        .el-picker-panel__sidebar>button {
            width: 56px;
            height: 24px;
            line-height: 20px;
            margin-right: 16px;
            border: 1px solid #6DE3FF;
            font-size: 12px;
            font-family: 'PingFangSC-Regular, PingFang SC';
            font-weight: 400;
            // color: #FFFFFF;
            padding: 0;
            text-align: center;
            border-radius: 2px;
        }

        /* 日期选择器日历部分样式*/
        .el-picker-panel__body-wrapper {
            width: 506px;
            height: 323px;
            display: flex;
            flex-wrap: wrap;
        }

        .el-picker-panel__body {
            margin: 0 !important;
        }
     }

</style>

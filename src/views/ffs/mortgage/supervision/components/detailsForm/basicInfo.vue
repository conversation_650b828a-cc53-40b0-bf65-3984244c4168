<!--  -->
<template>
  <div class="setmain">
    <el-descriptions title="被监管方信息" :column="3">
      <el-descriptions-item label="姓名">{{form.applyName}}</el-descriptions-item>
      <el-descriptions-item label="联系电话">{{form.applyPhone}}</el-descriptions-item>
      <el-descriptions-item label="身份证号">{{showCarId(form.applyCardNo)}}</el-descriptions-item>
      <el-descriptions-item label="家庭住址">{{handelAddress(form.applyAddress)}}</el-descriptions-item>
    </el-descriptions>
    <el-descriptions title="委托方信息" class="mtop" :column="3">
      <el-descriptions-item label="企业名称">{{form.bankInfo.companyName}}</el-descriptions-item>
      <el-descriptions-item label="企业类型">{{companyTypeName(form.bankInfo.companyType)}}</el-descriptions-item>
      <el-descriptions-item label="营业执照编号">{{form.bankInfo.businessLicenseNo}}</el-descriptions-item>
      <el-descriptions-item
        label="地址"
      >{{form.bankInfo.provinceName}}{{form.bankInfo.cityName}}{{form.bankInfo.countyName}}{{form.bankInfo.detailAddress}}</el-descriptions-item>
      <el-descriptions-item label="法人">{{form.bankInfo.realName}}</el-descriptions-item>
      <el-descriptions-item label="法人电话">{{form.bankInfo.phoneNumber}}</el-descriptions-item>
      <el-descriptions-item label="身份证号">{{showCarId(form.bankInfo.idCard)}}</el-descriptions-item>
      <el-descriptions-item
        label="负责人姓名"
        v-if="form.bankInfo.corprateName"
      >{{form.bankInfo.corprateName}}</el-descriptions-item>
      <el-descriptions-item
        label="负责人电话"
        v-if="form.bankInfo.phoneNumber"
      >{{form.bankInfo.phoneNumber}}</el-descriptions-item>
      <!-- <el-descriptions-item label="详细地址">{{form.bankInfo.detailAddress}}</el-descriptions-item> -->
    </el-descriptions>

    <div class="mtop fbc">
      <span class="exportQuot">服务费信息</span>
      <el-button type="primary" icon="el-icon-top" size="mini" @click="handleExportQuot">导出excel</el-button>
    </div>
    <div class="mb10">
      <superviseServiceTable :info="form" />
    </div>

    <el-descriptions title="调研信息" class="mtop" :column="3">
      <el-descriptions-item label="调研信息" v-if="form.intentionListId">
        <span class="wbrdw" @click="lookCreditInvestigation">查看></span>
      </el-descriptions-item>

      <el-descriptions-item label="意向单附件" v-if="form.fileIntentionList">
        <span class="loadpc">
          <i class="el-icon-folder"></i>
          意向单附件.{{regulatorySchemeName(form.fileIntentionList)}}
        </span>

        <a >
          <span class="wbrdw" @click="clickView(form.fileIntentionList)">查看></span>
        </a>
      </el-descriptions-item>

      <el-descriptions-item label="调研表附件" v-if="form.fileCreditInvestigation">
        <span class="loadpc">
          <i class="el-icon-folder"></i>
          调研表附件.{{regulatorySchemeName(form.fileCreditInvestigation)}}
        </span>

        <a >
          <span class="wbrdw" @click="clickView(form.fileCreditInvestigation)">查看></span>
        </a>
      </el-descriptions-item>
    </el-descriptions>

    <el-form ref="form" :model="form" label-width="120px">
      <div class="iputiirt nkfdj skeme">
        <span class="marrt20">监管文件</span>
      </div>
      <el-row class="filerow">
        <el-col :span="8" v-if="form.regulatoryScheme">
          <el-form-item label="监管方案：" prop="regulatoryScheme">
            <file-upload
              :fileType="['doc', 'pdf','png', 'jpg', 'jpeg']"
              :fileSize="100"
              :limit="1"
              v-model="form.regulatoryScheme"
              :isShowTip="false"
              :disabled="true"
              :delBtn="false"
              :preview="true"
            >
              <el-button type="primary" plain size="mini" class="lobtb">
                <i class="el-icon-upload el-icon--right"></i>
                上传监管方案
              </el-button>
            </file-upload>
          </el-form-item>
        </el-col>
        <el-col :span="8" v-show="form.fileTripleAgreement">
          <el-form-item label="三方协议：" prop="fileTripleAgreement">
            <file-upload
              :fileType="['doc', 'pdf','png', 'jpg', 'jpeg']"
              :fileSize="100"
              :limit="1"
              v-model="form.fileTripleAgreement"
              :isShowTip="false"
              :disabled="true"
              :delBtn="false"
              :preview="true"
            >
              <el-button type="primary" plain size="mini" class="lobtb">
                <i class="el-icon-upload el-icon--right"></i>
                上传三方协议
              </el-button>
            </file-upload>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item
            label="租赁合同："
            prop="fileLeaseContract"
            v-show="form.fileLeaseContract"
          >
            <file-upload
              :fileType="['doc', 'pdf','png', 'jpg', 'jpeg']"
              :fileSize="100"
              :limit="1"
              v-model="form.fileLeaseContract"
              :isShowTip="false"
              :disabled="true"
              :delBtn="false"
              :preview="true"
            >
              <el-button type="primary" plain size="mini" class="lobtb">
                <i class="el-icon-upload el-icon--right"></i>
                上传租赁合同
              </el-button>
            </file-upload>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row class="filerow">
        <el-col :span="8" v-if="form.fileLoan">
          <el-form-item label="贷款单：" prop="fileVaccineRecord">
            <file-upload
              :fileType="['doc', 'pdf','png', 'jpg', 'jpeg']"
              :fileSize="100"
              :limit="1"
              v-model="form.fileLoan"
              :isShowTip="false"
              :disabled="true"
              :delBtn="false"
              :preview="true"
            >
              <el-button type="primary" plain size="mini" class="lobtb">
                <i class="el-icon-upload el-icon--right"></i>
                贷款单
              </el-button>
            </file-upload>
          </el-form-item>
        </el-col>
        <el-col :span="8" v-if="form.fileOther">
          <el-form-item label="其他：" prop="fileOther">
            <file-upload
              :fileType="['doc', 'pdf','png', 'jpg', 'jpeg']"
              :fileSize="100"
              :limit="1"
              v-model="form.fileOther"
              :isShowTip="false"
              :disabled="true"
              :delBtn="false"
              :preview="true"
            >
              <el-button type="primary" plain size="mini" class="lobtb">
                <i class="el-icon-upload el-icon--right"></i>
                上传其他
              </el-button>
            </file-upload>
          </el-form-item>
        </el-col>
        
      </el-row>
    </el-form>

    <useLetterl-log
      :useLetterData="useLetterData"
      @close="useLetterData.open=false"
      v-if="useLetterData.open"
    />

    <model-form v-if="dialog.open" :dialog="dialog"></model-form>
  </div>
</template>

<script>
import { areaData } from "@/utils/mixin/area.js";
import modelForm from "@/views/ffs/supervisionSheet/supervisionApply/components/modelForm.vue";
import useLetterlLog from "@/views/ffs/mortgage/supervision/components/useLetterlLog.vue"; //用信记录
import { settlementWay } from "@/views/ffs/supervisionSheet/livingSupervision/utils/formatVal.js";
import { exportRecord } from "@/api/ffs/supervisionSheet/livingSupervisionApi";
import { exportExcel ,viewFile} from "@/utils/east";
import superviseServiceTable from "./superviseServiceTable";
export default {
  mixins: [areaData],
  name: "dbasicInfoForm",
  components: { modelForm, useLetterlLog,superviseServiceTable },
  props: {
    info: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      dialog: {
        open: false,
        title: "",
        id: "",
      },
      useLetterData: {
        open: false,
        title: "用信记录",
      },
      form: {
        bankInfo: {},
        placeName: "",
        yeassd: "",
        dakdata: "",
        datefw: "",
        rangeDate: "",
        metho: "",
      },
    };
  },
  created() {
    this.form = this.info;
  },
  computed: {
    handelAddress(){
     
        return (value)=>{
        if(value){
            return  value.replace(/null/g, " ")
        }

        }
    },
    regulatorySchemeName() {
      return (val) => {
        if (!val) return "";
        return val.slice(val.lastIndexOf(".") + 1);
      };
    },
    companyTypeName() {
      return (val) => {
        if (val == 1) {
          return "养殖企业";
        } else if (val == 2) {
          return "屠宰企业";
        } else if (val == 3) {
          return "银行";
        } else if (val == 4) {
          return "保险公司";
        } else if (val == 5) {
          return "行业协会";
        } else if (val == 6) {
          return "物流公司";
        } else if (val == 7) {
          return "个体工商户";
        } else if (val == 8) {
          return "合作社";
        }
      };
    },
    showCarId() {
      return (p) => {
        if (!p) return "--";
        return this.carId(p);
      };
    },
    settlementWayName() {
      return (val) => {
        return settlementWay(val);
      };
    },
  },
  mounted() {},
  methods: {
    clickView(url){
        viewFile(url)
    },
     //导出
    handleExportQuot() {
      const obj = {
        operationType: "1",
        pageNum: 1,
        pageSize: 99999,
        superviseId: this.info.superviseId,
      };
      exportExcel(exportRecord, obj,"仓单服务费信息");
    },
    close() {
      this.$emit("close");
    },
    //去调研信息
    lookCreditInvestigation() {
      this.dialog.open = true;
      this.dialog.id = this.form.intentionListId;
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["ruleForm"].validate((valid) => {
        if (valid) {
          this.$modal.loading("正在计算中，数据量较大，请耐心等待...");
          this.form.datetime = this.form.datetime + "-01";
          insertBankReport(this.form)
            .then((response) => {
              this.$modal.closeLoading();
              if (response.code == 200) {
                this.$modal.msgSuccess("报表生成成功，请在列表中点击下载。");
                this.open = false;
                this.$emit("refresh");
                this.$emit("close");
              }
            })
            .catch(() => {
              this.$modal.closeLoading();
            });
        }
      });
    },

    useLetterDig() {
      if (this.info.superviseStatus == 1) {
        return;
      }
      this.useLetterData.rowData = this.info;
      this.useLetterData.type = "info";
      this.useLetterData.open = true;
    },
  },
};
</script>
<style  scoped lang="scss">
@import url("../addForm/index.scss");
.exportQuot {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}
.mb10 {
  margin-top: 10px;
}
.fbc {
  display: flex;
  justify-content: space-between;
}
</style>

<template>
  <div class="app-container">
    <el-card class="mb10 form_box" shadow="never" ref="formBox">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="140px">
        <el-row class="form_row">
          <el-col class="form_col">
            <el-form-item label="区域查询：" prop="areaDataRange" v-show="regionDom">
                <RegCascaderTag v-model="queryParams.areaDataRange"></RegCascaderTag>
            </el-form-item>
            <el-form-item label="监管单号" prop="superviseId">
              <el-input v-model="queryParams.superviseId" placeholder="请输入监管单号" clearable  @keyup.enter.native="handleQuery"/>
            </el-form-item>
            <el-form-item label="委托方" prop="bankName">
              <el-input v-model="queryParams.bankName" placeholder="请输入委托方名称" clearable  @keyup.enter.native="handleQuery"/>
            </el-form-item>
            <el-form-item label="监管类型" prop="superviseType">
              <el-select v-model="queryParams.superviseType" clearable>
                <el-option label="全部" value />
                <el-option label="活体监管" value="1" />
                <el-option label="仓单监管" value="2" />
              </el-select>
            </el-form-item>
            <el-form-item label="结算状态" prop="settlementStatus">
              <el-select v-model="queryParams.settlementStatus" clearable>
                <el-option label="全部" value />
                <el-option label="进行中" value="1" />
                <el-option label="已完成" value="2" />
              </el-select>
            </el-form-item>
            <el-form-item label="被监管方" prop="applyName">
              <el-input v-model="queryParams.applyName" placeholder="请输入被监管方姓名" clearable  @keyup.enter.native="handleQuery"/>
            </el-form-item>
            <el-form-item label="付款方类型" prop="payerCode">
              <el-select v-model="queryParams.payerCode" placeholder="请选择" clearable>
                <el-option
                  v-for="dict in dict.type.ffs_supervise_payer"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="创建时间">
              <el-date-picker
                v-model="dateRange"
                style="width: 215px"
                value-format="yyyy-MM-dd"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              ></el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row style="margin-left: 120px;">
          <el-col>
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
              <template v-if="toggleSearchDom">
                <el-button type="text" @click="packUp">
                  {{ toggleSearchStatus ? '收起' : '展开' }}
                  <i
                    :class="{ 'el-icon-arrow-down': !toggleSearchStatus, 'el-icon-arrow-up': toggleSearchStatus }"
                  ></i>
                </el-button>
              </template>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    <el-card shadow="never" class="list_table">
      <el-row class="mb8 form_btn">
        <el-row>
          <el-col :span="18">
            <el-tabs v-model="activeName" @tab-click="handleClick" class="serciceFee">
              <el-tab-pane
                v-for="item in tabs"
                :label="item.label"
                :name="item.name"
                :key="item.name"
              ></el-tab-pane>
            </el-tabs>
          </el-col>
          <el-col :span="6" class="form_btn_col">
            <el-button
              type="success"
              icon="el-icon-plus"
              size="mini"
              @click="exportFeedList"
              plain
            >导出</el-button>
          </el-col>
        </el-row>
        <el-col :span="24">
          <el-row :gutter="10" class="mb8">
            <el-col :span="6">应收合计：{{params.receivableTotal||0}}元</el-col>
            <el-col :span="6">已收合计：{{params.receivedTotal||0}}元</el-col>
            <el-col :span="6">剩余未收合计：{{params.uncollectedTotal||0}}元</el-col>
          </el-row>
        </el-col>
      </el-row>
      <!-- 表格数据 -->
      <el-table
        :data="tableData"
        stripe
        style="width: 100%"
        v-loading="loading"
        :height="tableHeight"
        border
      >
        <el-table-column type="index" width="50px" align="center" label="序号" fixed />
        <el-table-column prop="settlementStatus  " label="结算状态" width="80px" align="center">
          <template slot-scope="scope">
            <div class="round" :style="{color:handelColor(scope.row.settlementStatus)}">
                <div class="round-dot" :style="{background:handelColor(scope.row.settlementStatus)}"></div>
                <span>
                {{ scope.row.settlementStatus ? scope.row.settlementStatus == 1? "进行中": "已完成": "-" }}
                </span>
            </div>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="remainServiceFee" label="剩余未收(元)" min-width="100"></el-table-column> -->

        <el-table-column min-width="178" align="right" sortable :sort-method="(a,b) => sortBy(a ,b , 'superviseServiceFee')">
          <template slot="header">
            <el-tooltip
              class="item"
              effect="dark"
              content="服务费总金额=每次用信金额对应的服务费总和"
              placement="top-start"
            >
              <span>
                服务费总金额(元)
                <i class="el-icon-question" style="color:red"></i>
              </span>
            </el-tooltip>
          </template>
          <template slot-scope="scope">
            <el-popover
              placement="right"
              trigger="click"
              @show="clickTrig(scope.row.superviseId,scope.row.superviseType)"
            >
              <div class="popover">
                <el-row :gutter="20" class="popover-row">
                  <el-col :span="4" class="popover-col">序号</el-col>
                  <el-col :span="5" class="popover-col">用信金额(万元)</el-col>
                  <el-col :span="4" class="popover-col">服务费(元)</el-col>
                  <el-col :span="4" class="popover-col">操作人</el-col>
                  <el-col :span="7" class="popover-col">操作时间</el-col>
                </el-row>
                <el-row :gutter="20" v-for="(item ,index) in tab" :key="index" class="popover-line">
                  <el-col :span="4" class="popover-col">{{index+1}}</el-col>
                  <el-col :span="5" class="popover-col">{{item.receiveAmount||0}}</el-col>

                  <el-col :span="4" class="popover-col">{{item.appendServiceFee||0}}</el-col>
                  <el-col :span="4" class="popover-col">{{item.operatorName||''}}</el-col>
                  <el-col :span="7" class="popover-col">{{item.createTime||''}}</el-col>
                </el-row>
                <el-row :gutter="20" class="popover-row">
                  <el-col :span="4" class="popover-col">合计</el-col>
                  <el-col :span="5" class="popover-col">{{receiveAmount.toFixed(2)}}</el-col>
                  <el-col :span="4" class="popover-col">{{appendServiceFee.toFixed(2)}}</el-col>
                </el-row>
              </div>
              <span
                style="color:#1890ff; cursor: pointer;"
                slot="reference"
              >{{scope.row.superviseServiceFee}}</span>
            </el-popover>
          </template>
        </el-table-column>

        <el-table-column prop="remainServiceFee" min-width="160" align="right" sortable :sort-method="(a,b) => sortBy(a ,b , 'remainServiceFee')">
          <template slot="header">
            <el-tooltip
              class="item"
              effect="dark"
              content="应收金额=服务费总金额-实收总金额"
              placement="top-start"
            >
              <span>
                应收金额(元)
                <i class="el-icon-question" style="color:red"></i>
              </span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column min-width="160" prop="receiveServiceFee" align="right" sortable :sort-method="(a,b) => sortBy(a ,b , 'receiveServiceFee')">
          <template slot="header">
            <el-tooltip class="item" effect="dark" content="实收总金额=已维护的收款金额合计" placement="top-start">
              <span>
                实收总金额(元)
                <i class="el-icon-question" style="color:red"></i>
              </span>
            </el-tooltip>
          </template>
        </el-table-column>

        <el-table-column min-width="178" align="right" sortable :sort-method="(a,b) => sortBy(a ,b , 'totalAmount')">
          <template slot="header">
            <el-tooltip
              class="item"
              effect="dark"
              content="实际发生金额为已产生的服务费用，每月1日更新上个月信息"
              placement="top-start"
            >
              <span>
                实际发生金额(元)
                <i class="el-icon-question" style="color:red"></i>
              </span>
            </el-tooltip>
          </template>
          <template slot-scope="scope">
            <el-popover placement="right" trigger="click" @show="actual(scope.row.superviseId)">
              <el-table
                :data="actualList"
                stripe
                style="width: 100%"
                show-summary
                :summary-method="getSummaries"
              >
                <el-table-column label="序号" type="index" align="center" />
                <el-table-column label="监管时间" prop="time" min-width="180" align="center">
                  <!-- <template slot-scope="scope">{{scope.row.startTime+'~'+scope.row.endTime}}</template> -->
                </el-table-column>
                <el-table-column label="监管天数" prop="superviseDays" align="center" />
                <el-table-column
                  label="服务费金额(元/天）"
                  prop="dayServiceFee"
                  min-width="140"
                  align="center"
                />
                <el-table-column
                  label="服务费发生金额(元)"
                  prop="occurredAmount"
                  min-width="140"
                  align="center"
                />
              </el-table>
              <span
                style="color:#1890ff; cursor: pointer;"
                slot="reference"
              >{{scope.row.totalAmount||0}}</span>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column min-width="150" align="right" sortable :sort-method="(a,b) => sortBy(a ,b , 'reconciliationAmount')">
          <template slot="header">
            <el-tooltip
              class="item"
              effect="dark"
              content="对账金额=实收总金额-实际发生金额"
              placement="top-start"
            >
              <span>
                对账金额(元)
                <i class="el-icon-question" style="color:red"></i>
              </span>
            </el-tooltip>
          </template>
          <template slot-scope="scope">{{scope.row.reconciliationAmount||0}}</template>
        </el-table-column>
        <el-table-column label="实收服务费有效期" prop="validEndTime" min-width="140" ></el-table-column>
        <el-table-column prop="bankName" label="委托方" show-overflow-tooltip min-width="180"></el-table-column>
        <el-table-column prop="applyName" label="被监管方" min-width="160" show-overflow-tooltip></el-table-column>
        <el-table-column label="结算方式">
          <template slot-scope="scope">
            {{
            handelSettlementWay(scope.row.settlementWay)
            }}
          </template>
        </el-table-column>
        <el-table-column prop="superviseType" label="监管类型" min-width="100" align="center">
          <template
            slot-scope="scope"
          >{{scope.row.superviseType?(scope.row.superviseType == 1? "活体监管":(scope.row.superviseType==2?'仓单监管':'资金流向')):''}}</template>
        </el-table-column>
        <el-table-column prop="superviseId" label="监管单号" min-width="180" align="center"></el-table-column>
        <el-table-column prop="superviseServiceRate" label="服务费率%" min-width="130" align="right" sortable :sort-method="(a,b) => sortBy(a ,b , 'superviseServiceRate')"/>

        <el-table-column label="付款方类型" min-width="160" show-overflow-tooltip>
          <template slot-scope="scope">{{showPayerCode(scope.row.payerCode)}}</template>
        </el-table-column>
        <el-table-column prop="endTime" label="服务费结束时间" min-width="180" align="center" ></el-table-column>
        <el-table-column prop="createUserName" label="创建人" min-width="120" show-overflow-tooltip align="left"></el-table-column>
        <el-table-column prop="createTime" label="创建时间" min-width="180" align="center" sortable :sort-method="(a,b) => sortBy(a ,b , 'createTime')"></el-table-column>
        <el-table-column label="操作" fixed="right" width="170" align="center">
          <template slot-scope="scope">
            <el-button
              icon="el-icon-warning"
              size="mini"
              @click="
                handleDetails(scope.row.settlementServiceFeeId,scope.row)
              "
              type="text"
            >详情</el-button>
            <el-button
              size="mini"
              v-hasPermi="['ffs:serviceFee:add']"
              icon="el-icon-s-check"
              @click="handleAssessment(scope.row.creditInvestigationId,scope.row)"
              type="text"
              class="btn_color_t"
              v-if="scope.row.settlementStatus == 1"
            >收款</el-button>
            <el-button
              icon="el-icon-circle-close"
              size="mini"
              class="btn_color_f"
              @click="closeFrame(scope.row)"
              type="text"
              v-if="scope.row.settlementStatus == 1"
            >关闭</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>
    <recordModel v-if="dialog.open" :dialog="dialog" @close="close"></recordModel>
    <collectionModel
      v-if="dialogCollection.open"
      @close="close"
      :dialogCollection="dialogCollection"
      @refresh="refresh"
    ></collectionModel>
  </div>
</template>

<script>
import {
  feeList,
  feeStatistics,
  feeClose,
  exportFeeList,
  occurredAmount,
} from "@/api/ffs/serviceFee.js";
import { addSuperviseamountList } from "@/api/ffs/mortgage/supervision.js";
import recordModel from "./components/recordModel.vue";
import collectionModel from "./components/collectionModel.vue";
import { getDicts } from "@/api/system/dict/data.js";
import dayjs from "dayjs";
import { exportExcel } from "@/utils/east";
import { tableUi } from "@/utils/mixin/tableUi.js";
export default {
  dicts: ["ffs_supervise_payer"],
  mixins: [tableUi],
  components: {
    recordModel,
    collectionModel,
  },
  data() {
    return {
      dialog: {
        open: false,
        title: "",
        id: "",
      },
      dialogCollection: {
        open: false,
        title: "",
      },
      tab: [],
      actualList: [], //实际金额明细
      activeName: "5",
      tabs: [
      { label: "全部", name: "5" },
        { label: "即将到期", name: "1" },
        { label: "已逾期", name: "2" },
        { label: "进行中", name: "3" },
        { label: "已结束", name: "4" },
       
      ],
      receiveAmount: 0, //用信金额总计
      appendServiceFee: 0, //服务费总计
      queryParams: {
        contractNo: "",
        superviseId: "",
        bankName: "",
        loanOfficerName: "",
        loanOfficerPhone: "",
        applyName: "",
        applyPhone: "",
        settlementStatus: "",
        payerCode: null,
        pageNum: 1,
        pageSize: 20,
        dateFlag: null,
        superviseType: "",
        areaDataRange:'',
      },
      loading: true,
      showType: 0,
      total: 0,
      dateRange: [],
      timeRange: [],
      tableData: [],
      payerCodeList: [],
      params: {},
      settlementWay: [
        { value: 1, text: "年付" },
        { value: 2, text: "半年付" },
        { value: 3, text: "季付" },
        { value: 4, text: "其他" },
      ],
    };
  },

  created() {
    this.getList();
    this.getPayerCode();
  },
  computed: {
        //处理状态颜色
        handelColor(){
        return (value)=>{
            if(value==1){
                return '#12AE63'
            }if(value==2){
                return '#9DA1A8'
            }
        }
    },
    handelSettlementWay() {
      return (status) => {
        let name = "";
        this.settlementWay.forEach((item) => {
          if (status == item.value) {
            name = item.text;
          }
        });
        return name;
      };
    },
    showPayerCode() {
      return (value) => {
        let name = "";
        if (value) {
          this.payerCodeList.forEach((item) => {
            if (item.dictValue == value) {
              name = item.dictLabel;
            }
          });
          return name;
        }
      };
    },
  },
  methods: {
    sortBy(a,b,key){
        let at=a[key]
        let bt=b[key]
        if(key=='createTime'){
            return  at-bt
        }else{
            return parseFloat(at) - parseFloat(bt)
        }
    },
    //实际发生金额
    actual(id) {
      occurredAmount({ superviseId: id }).then((res) => {
        console.log(res);
        if (res.code == 200) {
          res.result.list.forEach((item) => {
            item.time = item.startTime + "~" + item.endTime;
          });
          this.actualList = res.result.list;
        }
      });
    },
    //表格统计
    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = "总价";
          return;
        }
        const values = data.map((item) => Number(item[column.property]));
        if (column.property != "dayServiceFee" && column.property != "time") {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!isNaN(value)) {
              if (column.property == "occurredAmount") {
                let num = prev ? parseFloat(prev) : Number(prev);
                return num + parseFloat(curr);
              } else {
                return Number(prev) + Number(curr);
              }
            } else {
              return prev;
            }
          }, "");
          sums[index];
        }
      });
      return sums;
    },

    getPayerCode() {
      getDicts("ffs_supervise_payer").then((res) => {
        if (res.code == 200) {
          this.payerCodeList = res.data;
        }
      });
    },
    clickTrig(id, type) {
      this.receiveAmount = 0;
      this.appendServiceFee = 0;
      let operationType = "1";
      type == 1 ? (operationType = "1") : (operationType = "0");
      addSuperviseamountList({
        superviseId: id,
        pageNum: 1,
        pageSize: 100000,
        operationType: operationType,
      }).then((res) => {
        if (res.code == 200) {
          if (type == 1 && res.result.list.length > 0) {
            this.tab = [res.result.list[0]];
          } else {
            this.tab = res.result.list;
          }
          this.tab.forEach((item) => {
            this.appendServiceFee += parseFloat(item.appendServiceFee);
            this.receiveAmount += parseFloat(item.receiveAmount);
          });
        }
      });
    },
    //列表查询
    getList() {
      if (this.queryParams.dateFlag == 1) {
        this.queryParams.settlementStatus = "1";
      }
      feeList(this.queryParams).then((res) => {
        this.loading = false;
        if (res.code == 200) {
          this.handleEndTime(res.result.list);
          this.tableData = res.result.list;
          console.log(this.tableData);
          this.total = Number(res.result.total);
          this.params = res.result.params;
        }
      });
    },
    //计算服务费服务费结束时间
    handleEndTime(arry) {
      let closeTime = "";
      arry.forEach((item) => {
        let superviseEnd = new Date(item.superviseEnd).getTime();
        if (item.closeTime) {
          closeTime = new Date(item.closeTime).getTime();
        }
        if (item.superviseStatus != 3) {
          item.endTime = item.superviseEnd;
        }
        if (item.superviseStatus == 3 && closeTime >= superviseEnd) {
          item.endTime = item.superviseEnd;
        }
        if (item.superviseStatus == 3 && closeTime < superviseEnd) {
          if (item.superviseType == 2) {
            item.endTime = item.closeTime;
          } else {
            this.liveEndTime(item);
          }
        }
      });
    },
    //计算活体监管服务费的结束时间
    liveEndTime(item) {
      let obj = this.getTimeDiff(item.superviseStart, item.closeTime);
      let liveTime = "";
      if (obj.month >= 6) {
        liveTime = dayjs(item.superviseStart)
          .add(1, "year")
          .format("YYYY-MM-DD");
      }
      if (obj.month < 6 && obj.day > 0) {
        liveTime = dayjs(item.superviseStart)
          .add(6, "month")
          .format("YYYY-MM-DD");
      }
      if (dayjs(item.superviseEnd).diff(liveTime, "day") > 0) {
        item.endTime = liveTime;
      } else {
        item.endTime = item.superviseEnd;
      }
    },
    //     //计算两时间的差值
    getTimeDiff(value1, value2) {
      let flag = [1, 3, 5, 7, 8, 10, 12, 4, 6, 9, 11, 2];
      let start = new Date(value1);
      let end = new Date(value2);
      let year = end.getFullYear() - start.getFullYear();
      let month = end.getMonth() - start.getMonth();
      let day = end.getDate() - start.getDate();
      if (month < 0) {
        year--;
        month = end.getMonth() + (12 - start.getMonth());
      }
      if (day < 0) {
        month--;
        let index = flag.findIndex((temp) => {
          return temp === start.getMonth() + 1;
        });
        let monthLength;
        if (index <= 6) {
          monthLength = 31;
        } else if (index > 6 && index <= 10) {
          monthLength = 30;
        } else {
          monthLength = 28;
        }
        day = end.getDate() + (monthLength - start.getDate());
      }
      return { year: year, month: month, day: day };
    },

    //tab切换
    handleClick(tab) {
      this.queryParams.settlementStatus = null;
      this.queryParams.dateFlag = null;
      if (Number(tab.name) <= 2) {
        this.queryParams.dateFlag = tab.name;
      }
      if (tab.name == 3) {
        this.queryParams.settlementStatus = "1";
      }
      if (tab.name == 4) {
        this.queryParams.settlementStatus = "2";
      }
      this.getList();
    },
    reset() {
      this.resetForm("queryForm");
    },
    close() {
      this.dialog.open = false;
      this.dialogCollection.open = false;
    },
    //重置
    resetQuery() {
      this.dateRange = [];
      this.timeRange = [];
      this.reset();
      this.handleQuery();
    },
    //刷新页面
    refresh() {
      this.getList();
    },
    //搜索
    handleQuery() {
      console.log(this.timeRange, "00");
      this.queryParams.pageNum = 1;
      if (this.dateRange) {
        this.queryParams.createStartTime = this.dateRange[0];
        this.queryParams.createEndTime = this.dateRange[1];
      } else {
        this.queryParams.createStartTime = "";
        this.queryParams.createEndTime = "";
      }
      if (this.timeRange && this.timeRange.length > 0) {
        this.queryParams.superviseStart = this.timeRange[0];
        this.queryParams.superviseEnd = this.timeRange[1];
      } else {
        this.queryParams.superviseStart = "";
        this.queryParams.superviseEnd = "";
      }
      this.getList();
    },
    /** 查看详情按钮操作 */
    handleDetails(id, row) {
      this.dialog.open = true;
      this.dialog.title = "收费记录";
      this.dialog.id = id;
      this.dialog.row = row;
    },
    //确认收款
    handleAssessment(id, row) {
      this.dialogCollection.open = true;
      this.dialogCollection.title = "请确认收款金额";
      this.dialogCollection.id = id;
      this.dialogCollection.row = row;
    },
    //信息导出
    exportFeedList() {
      if (this.tableData.length <= 0) {
        this.$message.error("无内容可导出");
        return;
      }
      let obj = this.queryParams;
      const filename = "服务费信息";
      exportExcel(exportFeeList, obj, filename);
    },

    //服务费关闭
    closeFrame(row) {
      let text = "";
      let btnText = false;
      if (row.superviseStatus != 3) {
        text = "监管未结束，无法关闭服务费";
        btnText = false;
      } else {
        if (row.superviseType == 2) {
          text =
            "请确认服务费已全部收取，关闭后无法继续维护收款信息。是否确认关闭？";
          btnText = true;
        }
        if (row.superviseType == 1) {
          if (dayjs().diff(row.endTime, "day") > 0) {
            text =
              "请确认服务费已全部收取，关闭后无法继续维护收款信息。是否确认关闭？";
            btnText = true;
          } else {
            btnText = false;
            text = `服务费收取截止时间为：${row.endTime}，无法关闭！`;
          }
        }
      }
      this.$confirm(text, "提示", {
        showCancelButton: btnText,
        confirmButtonText: "确定",
        cancelButtonText: false,
        type: "warning",
        center: true,
      })
        .then(() => {
          if (btnText) {
            feeClose({
              settlementServiceFeeId: row.settlementServiceFeeId,
              settlementStatus: 2,
            }).then((res) => {
              if (res.code == 200) {
                this.$message({ type: "success", message: "关闭成功" });
                this.getList();
              }
            });
          }
        })
        .catch(() => {});
    },
  },
};
</script>

<style lang="scss" scoped>
.popover {
  &-row {
    background: #fafafa;
    padding: 10px 10px;
    width: 600px;
    text-align: center;
    font-weight: 700;
  }
  &-line {
    padding: 10px 10px;
    width: 600px;
    text-align: center;
  }
}
</style>
<style lang="scss">
.serciceFee {
  .el-tabs__nav-wrap::after {
    height: 0px !important;
  }
}
</style>

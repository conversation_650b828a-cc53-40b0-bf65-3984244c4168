<template>
  <div>
    <el-dialog
      title="监控视频"
      :visible.sync="videoOpen.open"
      width="700px"
      :close-on-click-modal="false"
      @close="refuse"
      class="siteManagement"
       append-to-body
      center
    >
    <div  v-loading="loading" class="videoDom">
      <video autoplay controls width="100%" muted id="myVideo" ref="video"  v-if="videoOpen.url.deviceType!=3"  ></video>
     <div id="playWind" v-if="videoOpen.url.deviceType==3" ></div>
    </div>
    </el-dialog>
  </div>
</template>
<script>
import flvjs from "flv.js";
import { getPlayUrl,getVideoUrl,ezvizUrl } from "@/api/ffs/farmingSupervisionSheet/farmingSiteManagement";
export default {
  props: {
    videoOpen: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      loading:true,
      flvPlayer: undefined,
      EZOPENDemo:undefined
    };
  },
  created() {
    this.$nextTick(()=>{
      this.getPlay()
   })
  },


  methods: {
    createDom(accessToken,url){
    var domain = "https://open.ys7.com";
    //   window.EZOPENDemo = EZOPENDemo;
      const ezopenInit = () => {
       this.EZOPENDemo = new EZUIKit.EZUIKitPlayer({
          id: 'playWind',
          width: '650',
          height: '450',
          template: "pcLive",
          url: url,
          accessToken: accessToken
        });
      }
      ezopenInit()
    },
    //获取播放地址
    getPlay(){
        if(this.videoOpen.url.deviceType==2){
            getVideoUrl({deviceCode:this.videoOpen.url.deviceCode}).then(res=>{
                this.loading=false
                if(res.code==200){
                    this.playerVideo(res.result.data);
                }
            })
        }
        if(this.videoOpen.url.deviceType==3){
            ezvizUrl({deviceCode:this.videoOpen.url.deviceCode,channelCode:this.videoOpen.url.channelCode,ezvizId:this.videoOpen.url.ezvizId}).then(res=>{
                this.loading=false
                if(res.code==200){
                   this.createDom(res.result.accessToken,res.result.url)
                }
            })
        }
        if(this.videoOpen.url.deviceType==1) {
            getPlayUrl({deviceCode:this.videoOpen.url.deviceCode,channelCode:this.videoOpen.url.channelCode}).then(res=>{
                this.loading=false
            if(res.code==200){
                this.loading=false
                this.playerVideo(res.result.httpsFmp4);
            }
        })
        }
    },

    playerVideo(url) {
      if (flvjs.isSupported()) {
        this.$nextTick(() => {
          var videoElement = document.getElementById("myVideo");
          let typeValue = this.videoOpen.url.deviceType==2 ? "flv" : "mp4";
          this.flvPlayer = flvjs.createPlayer({
            type: typeValue,
            url: url, //你的url地址
            isLive: true,
            hasAudio: false,
            hasVideo: true,
            withCredentials: false,
            autoplay: false,
            cors: true,
          });
          this.flvPlayer.attachMediaElement(videoElement);
          this.flvPlayer.load();
          this.flvPlayer.play();
        });
      } else {
        this.$message({
          type: "warning",
          message: "加载失败",
        });
      }
    },

    refuse() {
      this.flv_destroy();
      this.$emit("close");
      if(this.EZOPENDemo){
        this.EZOPENDemo.stop()
      }
    },

    flv_destroy() {
      if (this.flvPlayer) {
        this.flvPlayer.pause();
        this.flvPlayer.unload();
        this.flvPlayer.detachMediaElement();
        this.flvPlayer.destroy();
        this.flvPlayer = null;
      }
    },
  },
};
</script>

      <style lang="scss" >
.addMonitor {
  .el-dialog__header {
    background-color: #f4f4f4;
  }
  //   .el-dialog__body {
  //     padding-top: 10px;
  //   }
  //   .el-input__inner {
  //     border: none;
  //   }
  .el-dialog__footer {
    text-align: center;
  }

  .selectWidth {
    width: 100%;
  }
  &-info {
    background: #f4f4f4;
    width: 540px;
    margin: 0 0 22px 120px;
    border-radius: 8px;
    padding: 10px 14px;
    &-row {
      padding: 5px 0;
    }
  }
}

.inputWidth {
  width: 100%;
}
.siteManagement {
        padding: 0;
        margin: 0;
        text-align: center;
        background: #00000080;
        overflow: hidden;
      }
      .videoDom{
        video::-webkit-media-controls-timeline {
        display: none;
      }
      }
</style>

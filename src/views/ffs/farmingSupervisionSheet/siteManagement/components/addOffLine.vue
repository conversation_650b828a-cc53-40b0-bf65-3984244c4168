<template>
    <div>
      <el-dialog
        title="新增视频设备"
        :visible.sync="offLine.open"
        width="700px"
        :close-on-click-modal="false"
        @close="refuse"
        class="addOffLine"
        append-to-body
      >
        <el-form
          :model="form"
          :rules="rules"
          ref="ruleForm"
          label-width="120px"
          class="demo-ruleForm"
        >
          <el-form-item label="视频播放名称" prop="videoName">
            <el-input v-model.trim="form.videoName" placeholder="请输入视频播放名称"></el-input>
          </el-form-item>
          <el-form-item label="视频播放地址" prop="videoAddress">
            <el-input v-model.trim="form.videoAddress" placeholder="请输入视频播放地址"></el-input>
          </el-form-item>
          <el-form-item label="视频" prop="videoUrl">
          <el-upload
              accept=".mp4"
              action="#"
              list-type="picture-card"
              :http-request="upload"
              :show-file-list="false"
            >
              <i class="el-icon-plus" v-show="form.videoUrl == ''"></i>
              <video
                width="146px"
                height="146px"
                v-show="form.videoUrl!= ''"
                :src="form.videoUrl"
                controls="controls"
              ></video>
            </el-upload>
            <span class="box-delete"
              ><i
                class="el-icon-delete"
                v-show="form.videoUrl!= ''"
                @click="handleRemove"
              ></i
            ></span>
            </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input type="textarea" v-model.trim="form.remark" placeholder="请输入备注信息"></el-input>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="refuse">关闭</el-button>
          <el-button type="primary" @click="submitForm">提交</el-button>
        </span>
      </el-dialog>
    </div>
  </template>

      <script>
import ObsClient from "esdk-obs-browserjs/src/obs";
import { selectObsData } from "@/api/system/appupgrade";
import {offlineVideoAdd,offlineVideoEdit} from  '@/api/ffs/farmingSupervisionSheet/farmingSiteManagement'
  export default {

    props: {
        offLine: {
        type: Object,
        default: () => {
          return {};
        },
      },
    },
    data() {
      return {
        ObsClient:null,

        form: {
            sourceId:'',
            sourceType:'',//1养殖场，2仓库
            videoName:'',
            videoAddress:'',
            videoUrl:'',
            remark:'',
            sourceName:''
        },
        rules: {
            videoName: [
            {
              required: true,
              message: "请填写视频播放名称",
              trigger: "blur",
            },
          ],
          videoUrl: [
            {
              required: true,
              message: "请上传视频",
              trigger: "blur",
            },
          ],
          videoAddress: [
            {
              required: true,
              message: "请填写视频播放地址",
              trigger: "blur",
            },
          ],
        },
      };
    },
    created() {
      this.form.sourceId = this.$route.query.id;
      this.form.sourceType = this.$route.query.type;

    },
    methods: {
    //上传视频
    upload(params){
        this.$modal.loading("正在上传文件，请稍候...");
        selectObsData().then((response) => {
        if(response.code==200){
            this.obsInfo = response.result;
            this.obsClient = new ObsClient({
              access_key_id: this.obsInfo.ak, // 你的ak
              secret_access_key: this.obsInfo.sk, // 你的sk
              server: this.obsInfo.sslMode + "://" + this.obsInfo.endpoint // 你的endPoint
            });
            this.params(params)
        }
      });
    },
     params(params) {
         let date=new Date()
         let that=this
         this.obsClient.putObject({
           Bucket: this.obsInfo.bucketName, // 桶名
           Key:  this.obsInfo.active +`/${date.getFullYear()}/${date.getMonth() +1 }/`+`${date.getTime()}/` + params.file.name, // 路径 + 文件名
           SourceFile: params.file,
           ProgressCallback: this.callback
         }, function (err, result) {
           if (err) {
             that.$message.error('文件上传失败')
             that.$modal.closeLoading();
           } else {
             let url= that.obsInfo.sslMode + "://" + that.obsInfo.bucketName + "." + that.obsInfo.endpoint + "/" + that.obsInfo.active +`/${date.getFullYear()}/${date.getMonth() +1 }/`+`${date.getTime()}/` + params.file.name;
             that.form.videoUrl=url
              that.$modal.closeLoading();
           }
         })
     },

      submitForm() {
        this.$refs["ruleForm"].validate(async (valid) => {
          if (!valid) {
            return;
          }
          if(this.form.videoId){
            this.edit()
          }else{
          offlineVideoAdd(this.form).then((res) => {
              if (res.code == 200) {
                this.$message({
                  type: "success",
                  message: "添加成功",
                });
                this.$emit("refresh");
                this.refuse();
              }
            });
        }
        });

      },
      //视频编辑
      edit() {
        offlineVideoEdit(this.form).then((res) => {
          if (res.code == 200) {
            this.$message({
              type: "success",
              message: "编辑成功",
            });
            this.$emit("refresh");
            this.refuse();
          }
        });
      },

      refuse() {
        this.$emit("close");
      },
      //删除
      handleRemove(){
        this.form.videoUrl=''
      }
    },
  };
  </script>

      <style lang="scss">
  .addOffLine {
    .el-dialog__header {
      background-color: #f4f4f4;
    }
    //   .el-dialog__body {
    //     padding-top: 10px;
    //   }
    //   .el-input__inner {
    //     border: none;
    //   }
    .el-dialog__footer {
      text-align: center;
    }

    .selectWidth {
      width: 100%;
    }
    &-info {
      background: #f4f4f4;
      width: 540px;
      margin: 0 0 22px 120px;
      border-radius: 8px;
      padding: 10px 14px;
      &-row {
        padding: 5px 0;
      }
    }
    .box {
  position: relative;
  &-delete {
    font-size: 30px;
    top: 44px;
    left: 55px;
    position: absolute;
  }
}
  }

  .inputWidth {
    width: 100%;
  }
  </style>

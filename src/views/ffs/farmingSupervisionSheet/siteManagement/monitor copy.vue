<template>
  <div class="app-container">
    <el-card style="padding:10px 0" shadow="never">
      <div class="clearfix" v-if="headBar">
        <el-button type="success" size="mini" icon="el-icon-arrow-left" @click="goback">返回</el-button>
        <div
          style="display: flex; justify-content: end; width: 100%;"
          v-hasPermi="['ffs:video:add']"
        >
          <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="addMonitor">新增视频设备</el-button>
        </div>
      </div>
      <div v-for="(bigTtem ,index) in tableData" :key="index">
        <el-row :gutter="20" style="padding:10px 0;font-size:16px ;font-weight: 700;">
          <el-col :span="6">养殖场名称：{{bigTtem.pastureName}}</el-col>
          <!-- <el-col
            :span="8"
          >养殖场地址：{{pastureData.provinceName}}{{pastureData.cityName}}{{pastureData.countyName}}</el-col>-->
          <el-col :span="10">
            <div class="monitor-census">
              <span>视频监控总数：{{monitorToast}}</span>
              <!-- <span>在线：19</span>
              <span>离线：19</span>-->
            </div>
          </el-col>
        </el-row>
        <div class="monitor-conter" v-for="(item ,index) in bigTtem.busInfoList" :key="index">
          <div class="monitor-conter-imge">
            <img
              :src="item.coverPicture"
              alt
              style="width:100%;height: 100%;"
              v-if="item.coverPicture"
            />
            <img
              src="@/assets/images/ffs/monitor.png"
              alt
              style="width:100%;height: 100%;"
              v-show="!item.coverPicture"
            />
            <div class="monitor-conter-imge-play" @click="pay(item)">
              <i class="el-icon-video-play"></i>
            </div>
            <div
              class="monitor-conter-imge-delete"
              @click="deletePastureVideo(item.videoId)"
              v-hasPermi="['ffs:video:delete']"
            >删除</div>
            <div
              class="monitor-conter-imge-edit"
              @click="editPastureVideo(item)"
              v-if="headBar"
              v-hasPermi="['ffs:video:edit']"
            >编辑</div>
            <div
              class="monitor-conter-imge-onLine"
            >{{item.status==1?'在线':(item.status!=null?'不在线':'未知')}}</div>
            <div class="monitor-conter-imge-number">安装地点：{{item.mountLocation}}</div>
          </div>
        </div>
      </div>
      <div v-if="tableData.length==0" style="text-align: center;color:#909399">暂无数据</div>
    </el-card>
    <addMonitor v-if="monitor.open" :monitor="monitor" @close="close" @refresh="refresh"></addMonitor>
    <videoModel v-if="videoOpen.open" :videoOpen="videoOpen" @close="close"></videoModel>
  </div>
</template>
      <script>
import {
  pastureVideoList,
  pastureById,
  deleteVideo,
} from "@/api/ffs/farmingSupervisionSheet/farmingSiteManagement";
import {
  warehouseOfflineVideo,
  listByPastureIds,
} from "@/api/ffs/farmingSupervisionSheet/livingSupervisionApi";

import addMonitor from "./components/addMonitor.vue";
import videoModel from "./components/videoModel.vue";

export default {
  components: {
    addMonitor,
    videoModel,
  },
  props: {
    headBar: {
      typeof: Boolean,
      default: true,
    },
    info: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      monitor: {
        open: false,
        objItem: {},
      },
      videoOpen: {
        open: false,
        url: "",
      },
      animalsCategory: [], //活畜类别
      animalsVarieties: [], //品种
      animalsType: [], //类型
      videoPlayer: "",
      tableData: [],
      pastureData: {},
      flvPlayer: "",
      queryParams: {
        pastureId: "",
        userPhone: "",
      },
      monitorToast: 0,
    };
  },
  computed: {},

  watch: {
    info(val) {
      if (val) {
        this.queryParams.userPhone = val.applyPhone;
        console.log("queryParams_queryParams", val);
        this.getList();
      }
    },
  },

  created() {
    this.queryParams.pastureId = this.$route.query.pastureId;
    if (this.headBar) {
      this.getPasture();
    }
  },

  methods: {
    // superviseType = 1; //监管类型：1活畜，2仓单
    getMonitorList(superviseType, superviseId) {
      let fn = warehouseOfflineVideo;
      if (superviseType == 1) {
        fn = listByPastureIds;
      }
      fn({ ids: [superviseId] }).then((res) => {
        if (res.code == 200) {
          this.pastureData = res.result;
          this.queryParams.userPhone = res.result.userName;
          this.getList();
        }
      });
    },
    //查询养殖场信息
    getPasture() {
      pastureById({ ids: [this.queryParams.pastureId] }).then(async (res) => {
        if (res.code == 200) {
          this.pastureData = res.result;
          this.queryParams.userPhone = res.result.userName;
          this.getList();
        }
      });
    },
    //列表查询
    getList() {
      return new Promise((resolve, reject) => {
        pastureVideoList(this.queryParams).then((res) => {
          if (res.code == 200) {
            const result = res.result.list || res.result;
            this.monitorToast = res.result.list
              ? res.result.list.length
              : res.result.length;
            this.tableData = this.mergeGroup(result);
            this.loading = false;
            resolve([this.mergeGroup(result)]);
            console.log('888：',888);

          } else {
            resolve([]);
          }
        });
      });
    },
    //视频编辑
    editPastureVideo(item) {
      this.monitor.open = true;
      this.monitor.objItem = item;
    },
    //查询养殖场信息
    deletePastureVideo(id) {
      this.$confirm("此操作将永久删除, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          deleteVideo({ ids: [id] }).then((res) => {
            if (res.code == 200) {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              this.getList();
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    mergeGroup(arr) {
      var map = {},
        dest = [];
      for (var i = 0; i < arr.length; i++) {
        var ai = arr[i];
        if (!map[ai.pastureId]) {
          dest.push({
            videoId: ai.videoId,
            groupPastureId: ai.pastureId,
            pastureName: ai.pastureName,
            busInfoList: [ai],
          });
          map[ai.pastureId] = ai;
        } else {
          for (var j = 0; j < dest.length; j++) {
            var dj = dest[j];
            if (dj.groupPastureId == ai.pastureId) {
              dj.busInfoList.push(ai);
              break;
            }
          }
        }
      }
      return dest;
    },
    refresh() {
      this.getList();
    },
    //页面返回
    goback() {
      this.$tab.closeOpenPage();
      this.$router.go(-1);
    },
    reset() {
      this.resetForm("queryForm");
    },
    close() {
      this.monitor.open = false;
      this.monitor.objItem = {};
      this.videoOpen.open = false;
    },
    addMonitor() {
      this.monitor.open = true;
    },
    //视频播放
    pay(item) {
      this.videoOpen.open = true;
      this.videoOpen.url = item;
    },
  },
};
</script>
<style lang="scss" scoped>
video::-webkit-media-controls-timeline {
  display: none;
}
.clearfix {
  display: flex;
  justify-content: space-between;
}
.monitor {
  &-census {
    font-weight: 400;
    display: flex;
    width: 100%;
    justify-content: flex-end;
    span {
      padding: 0 10px;
    }
  }
  &-conter {
    display: inline-block;
    &-imge {
      position: relative;
      display: inline-block;
      overflow: hidden;
      box-sizing: border-box;
      width: 300px;
      height: 300px;
      margin: 20px 20px;
      background: rgba(242, 243, 245, 1);
      &-onLine {
        position: absolute;
        right: 10px;
        top: 10px;
        color: #13ce66;
      }
      &-number {
        position: absolute;
        left: 10px;
        bottom: 10px;
      }
      &-play {
        font-size: 70px;
        position: absolute;
        left: 50%;
        top: 50%;
        color: black;
        z-index: 1;
        transform: translate(-50%, -50%);
      }
      &-delete {
        position: absolute;
        right: 10px;
        bottom: 10px;
        color: red;
        z-index: 1;
        cursor: pointer;
      }
      &-edit {
        position: absolute;
        right: 11px;
        bottom: 40px;
        color: #5173ff;
        z-index: 1;
        cursor: pointer;
      }
    }
  }
}
</style>

const statusMap = {
  1: "年付",
  2: "半年付",
  3: "季付",
  4: "其他",
};
const settlementWay = function (val) {
  return statusMap[val];
};

const superviseStatus = function (val) {
  if (val == 1) {
    return "待监管";
  } else if (val == 2) {
    return "监管中";
  } else if (val == 3) {
    return "已结束";
  }
};


const serviceFeeType = function (val) {
  if (val == 1) {
    return "非包干";
  } else if (val == 0) {
    return "包干";
  }
};



const settlementWayList = function () {
  let arr = [];
  for (const key in statusMap) {
    arr.push({
      name: statusMap[key],
      value: key*1,
    });
  }
  return arr
};

export { settlementWay, superviseStatus, settlementWayList,serviceFeeType };

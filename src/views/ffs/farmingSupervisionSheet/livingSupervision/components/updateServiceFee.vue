<template>
    <div>
      <el-dialog
        :title="updateServiceFeeData.title"
        :visible.sync="updateServiceFeeData.open"
        :close-on-click-modal="false"
        @close="close"
        append-to-body
        class="fieldList"
      >
        <el-form :model="form" status-icon :rules="rules" ref="ruleForm">
          <div v-if="form.superviseType==1">
            <el-form-item label="变更后用信金额" prop="superviseAmount">
              <el-input
                v-model.number="form.superviseAmount"
                clearable
                oninput="if(value.length>8)value=value.slice(0,8)"
                placeholder="请输入变更后用信金额"
                @blur="changeSuperviseAmount"
              >
                <template slot="append">万元</template>
              </el-input>
            </el-form-item>
            <el-form-item label="变更后抵押活畜数量：" prop="mortgageCount">
              <el-input
                v-model.number="form.mortgageCount"
                clearable
                placeholder="请输入变更后抵押活畜数量"
                oninput="if(value.length>4)value=value.slice(0,4)"
              ></el-input>
            </el-form-item>
          </div>
          <div v-else-if="form.superviseType==5">
            <el-form-item label="变更后质押货值" prop="mortgageTotalAmount">
              <el-input
                v-model.number="form.mortgageTotalAmount"
                clearable
                oninput="if(value.length>8)value=value.slice(0,8)"
                placeholder="请输入变更后质押货值"
                @blur="changeMortgageTotalAmount"
              >
                <template slot="append">万</template>
              </el-input>
            </el-form-item>
          </div>
          <el-form-item label="变更后每天服务费金额" prop="dayServiceFee">
            <el-input
              v-model.number="form.dayServiceFee"
              clearable
              placeholder="请输入变更后每天服务费金额"
              type="number"
              @blur="changedayServiceFee"
            >
              <template slot="append">元/天</template>
            </el-input>
          </el-form-item>
          <el-form-item label="开始时间" prop="effectTime">
            <el-date-picker
              v-model="form.effectTime"
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="请选择开始时间"
              class="selectWidth"
              @change="effectTimeChange"
              :picker-options="isDisabled"
              :default-value="dataDefaultValue"
            ></el-date-picker>
          </el-form-item>
          <div class="demipls" v-if="form.daySum ">监管天数：{{form.daySum }}天，监管单结束日期：{{form.superviseEnd }}</div>
          <el-form-item label="预计收入" prop="estimatedRevenue">
            <el-input
              v-model.number="form.estimatedRevenue"
              clearable
              placeholder="请输入预计收入"
              type="number"
              oninput="if(value.length>10)value=value.slice(0,10)"
              readonly
            >
              <template slot="append">元</template>
            </el-input>
          </el-form-item>
          <el-form-item label="变更后服务费率" prop="superviseServiceRate">
            <el-input
              v-model.number="form.superviseServiceRate"
              clearable
              placeholder="请输入变更后服务费率"
              type="number"
              oninput="if(value.length>10)value=value.slice(0,10)"
              readonly
            >
              <template slot="append">%</template>
            </el-input>
          </el-form-item>
        </el-form>
        <!-- <div class="worn">注：生效日期仅能选择本月及之后日期</div> -->
        <span slot="footer" class="dialog-footer">
          <el-button @click="close">取消</el-button>
          <el-button
            type="primary"
            @click="submitForm"
            v-if="!disabled"
            v-hasPermi="['ffs:supervision:edit']"
          >提交</el-button>
        </span>
      </el-dialog>
    </div>
  </template>
  <script>
  import { editFee } from "@/api/ffs/farmingSupervisionSheet/livingSupervisionApi";
  import { bySupervisionFeeList } from "@/api/ffs/farmingSupervisionSheet/livingSupervisionApi";
  var dayjs = require("dayjs");
  import { toFixed2 } from "@/utils/east.js";
  export default {
    props: {
      updateServiceFeeData: {
        type: Object,
        default: () => {},
      },
    },
    data() {
      // 默认价格
      const twoPoint = (rule, value, callback) => {
        if (/^\d+\.?\d{0,2}$/.test(value)) {
          if (
            value.toString().indexOf(".") == "-1" &&
            value.length > 1 &&
            value.slice(0, 1) == "0"
          ) {
            return callback(new Error("小数位最多不超过两位"));
          }
          callback();
        } else {
          return callback(new Error("小数位最多不超过两位"));
        }
      };
      return {
        isDisabled: {
          disabledDate: (time) => {
            // let minData = Date.now();
             let minData = this.updateServiceFeeData.info.superviseStart
            let maxData = dayjs(this.form.superviseEnd).subtract(1, "day");
            if (this.lastServideFee != "") {
              minData = dayjs(this.lastServideFee.effectTime).add(1, "day");
            }

             this.dataDefaultValue=dayjs(minData).format('YYYY-MM-DD')
            return (
              time.getTime() > dayjs(maxData).valueOf() ||
              time.getTime() < dayjs(minData).valueOf()
            );
          },
        },
        dataDefaultValue:Date.now(),
        lastServideFee: "",
        form: {
          superviseId: "",
          operationType: "1",
          superviseType: "",
          dayServiceFee: "", //变更后每天服务费金额
          effectTime: "", //开始时间
          superviseServiceRate: "", //变更后服务费率
          estimatedRevenue: "", //预计收入
          mortgageCount: "", //变更后抵押活畜数量
          mortgageTotalAmount: "", // 变更后质押货值
          superviseAmount: "", //变更后用信金额
          daySum:"",
        },
        disabled: false,
        rules: {
          superviseAmount: [
            { required: true, message: "请填写变更后用信金额", trigger: "blur" },
            {
              validator: twoPoint,
              message: "请填写正确有效的变更后用信金额",
              trigger: "blur",
            },
          ],
          mortgageTotalAmount: [
            { required: true, message: "请填写变更后质押货值", trigger: "blur" },
            {
              validator: twoPoint,
              message: "请填写正确有效的变更后质押货值",
              trigger: "blur",
            },
          ],
          mortgageCount: [
            {
              required: true,
              message: "请填写变更后抵押活畜数量",
              trigger: "blur",
            },
          ],
          effectTime: [
            { required: true, message: "请选择开始时间", trigger: "blur" },
          ],
          dayServiceFee: [
            {
              required: true,
              message: "请填写变更后每天服务费金额",
              trigger: "blur",
            },
            {
              validator: twoPoint,
              trigger: ['blur','change'],
            },
          ],
        },
      };
    },
    created() {
      const { superviseId, superviseType, superviseEnd } =
        this.updateServiceFeeData.info;
      const { userId, userName, phonenumber ,corprateName,nickName} = this.$store.state.user.user;
      this.form.superviseId = superviseId;
      this.form.superviseType = superviseType;
      this.form.superviseEnd = superviseEnd;
      this.form.operatorId = userId;
      this.form.operatorName = corprateName||nickName||userName;
      this.form.operatorPhone = phonenumber;
      this.getLastServideFee();
    },
    methods: {
      effectTimeChange(val) {
        const { superviseEnd,superviseStart } = this.updateServiceFeeData.info;
        const daySum = dayjs(superviseEnd).diff(val, "day");
        if (daySum <= 0) {
          this.$message({
            message: "开始时间必须大于监管结束日期：" + superviseEnd,
            type: "error",
          });
          this.form.effectTime = "";
          return;
        }
        this.getEstimatedRevenue();
        this.getSuperviseServiceRate();
      },
      changedayServiceFee() {
        this.getEstimatedRevenue();
        this.getSuperviseServiceRate();
      },
      changeMortgageTotalAmount() {
        this.getSuperviseServiceRate();
      },
      changeSuperviseAmount() {
        this.getSuperviseServiceRate();
      },
      // 预计收放=每天服务费金额*监管天数
      getEstimatedRevenue() {
        const { effectTime, dayServiceFee } = this.form;
        if (!effectTime || !dayServiceFee) return;
        const { superviseEnd } = this.updateServiceFeeData.info;
        const daySum = dayjs(superviseEnd).diff(effectTime, "day");
        this.form.estimatedRevenue = toFixed2(dayServiceFee * (daySum));
      },
      // 变更后服务费率=（每天监管服务费金额*监管天数）/变更后用信金额*100%
      getSuperviseServiceRate() {
        const {
          effectTime,//付费结束时间
          dayServiceFee,//每天的服务费
          superviseAmount,//用信金额
          superviseType,
          mortgageTotalAmount,//变更后的货值
        } = this.form;
        let totalMoney = superviseAmount;
        if (superviseType == 1) {
          totalMoney = superviseAmount;
        } else if (superviseType == 5) {
          totalMoney = mortgageTotalAmount;
        }

        if (!effectTime || !dayServiceFee || !totalMoney) return;
        const { superviseEnd } = this.updateServiceFeeData.info;
        const daySum = dayjs(dayjs(superviseEnd)).diff(effectTime, "day");
        this.form.daySum = daySum;
        this.form.superviseServiceRate = toFixed2(((dayServiceFee * daySum) / (totalMoney * 10000))*100);
      },
      close() {
        this.$emit("close");
      },
      /** 提交按钮 */
      submitForm() {
        this.$refs["ruleForm"].validate((valid) => {
            console.log(this.$refs['ruleForm'],"cccccxxxx");
          if (valid) {
            const {
              superviseAmount,
              mortgageCount,
              dayServiceFee,
              mortgageTotalAmount,
              superviseType,
            } = this.form;
            let totalMoney;
            let p1 = "";
            let p2 = "";
            if (superviseType == 1) {
              totalMoney = superviseAmount;
              p1 = `变更后用信金额：${totalMoney}万`;
              p2 = `变更后抵押活畜数量：${mortgageCount}`;
            } else if (superviseType == 5) {
              totalMoney = mortgageTotalAmount;
              p1 = `变更后质押货值：${totalMoney}万`;
            }
            this.$confirm(
              `
        <div>${p1}</div>
        <div>${p2}</div>
        <div>变更后每天服务费金额：${dayServiceFee}元</div>
         <strong>请确认本次填写信息是否准确</strong>
        `,
              "确认",
              {
                distinguishCancelAndClose: true,
                dangerouslyUseHTMLString: true,
                confirmButtonText: "确定",
                cancelButtonText: "取消",
              }
            ).then(() => {
              this.$modal.loading("正在提交中，请耐心等待...");
              if (superviseType == 5) {
                this.form.superviseAmount =
                  this.updateServiceFeeData.info.superviseAmount;
              }
              editFee(this.form)
                .then((response) => {
                  this.$modal.closeLoading();
                  if (response.code == 200) {
                    this.$modal.msgSuccess("提交成功");
                    this.open = false;
                    this.$emit(
                      "refresh",
                      this.form.receiveAmount,
                      this.form.appendServiceFee
                    );
                    this.$emit("close");
                  }
                })
                .catch(() => {
                  this.$modal.closeLoading();
                });
            });
          }
        });
      },
      getLastServideFee() {
        const { superviseId } = this.updateServiceFeeData.info;
        bySupervisionFeeList({
          pageNum: 1,
          pageSize: 99999,
          operationType: "1",
          superviseId,
        }).then((res) => {
          if (res.code == 200) {
            const result = res.result.list || [];
            if (result.length > 0) {
              this.lastServideFee = result[result.length - 1];
            }
          } else {
            this.$message.error(res.message);
          }
        });
      },
    },
  };
  </script>
  <style lang="scss" scoped>
  .fieldList {
    .el-dialog__header {
      background-color: #f4f4f4;
    }
    .el-dialog__footer {
      text-align: center;
    }
    .selectWidth {
      width: 100%;
    }
  }
  .worn {
    text-align: center;
    color: red;
  }
  .demipls {
    margin-top: -8px;
    font-size: 12px;
    color: #666;
    margin-bottom: 20px;
  }
  </style>

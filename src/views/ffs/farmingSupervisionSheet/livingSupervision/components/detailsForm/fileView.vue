<!--  -->
<template>
  <div class="fileView">
    <el-dialog
      title="查看附件"
      :visible.sync="open"
      :close-on-click-modal="false"
      append-to-body
      width="600px"
      class="postPone"
    >
      <el-row>
        <el-form :model="form"  ref="ruleForm"  label-width="100px">
          <el-form-item label="三方协议：" prop="livestockFileTripleAgreement" v-show="form.livestockFileTripleAgreement">
            <file-upload
              :fileSize="100"
              :limit="1"
              :preview="true"
              :delBtn="false"
              v-model="form.livestockFileTripleAgreement"
              :isShowTip="false"
            ></file-upload>
          </el-form-item>
          <el-form-item label="监管意向：" prop="livestockFileIntentionList" v-show="form.livestockFileIntentionList">
            <file-upload
              :fileSize="100"
              :limit="1"
              :preview="true"
              :delBtn="false"
              v-model="form.livestockFileIntentionList"
              :isShowTip="false"
            ></file-upload>
          </el-form-item>
          <el-form-item label="调研表：" v-show="form.livestockFileCreditInvestigation">
            <file-upload
              :fileSize="100"
              :limit="1"
              :preview="true"
              :delBtn="false"
              v-model="form.livestockFileCreditInvestigation"
              :isShowTip="false"
            ></file-upload>
          </el-form-item>
          <el-form-item label="监管方案：" v-show="form.livestockFileRegulatoryScheme">
            <file-upload
              :fileSize="100"
              :limit="1"
              :preview="true"
              :delBtn="false"
              v-model="form.livestockFileRegulatoryScheme"
              :isShowTip="false"
            ></file-upload>
          </el-form-item>
          <el-form-item label="监管合同：" v-show="form.livestockFileSuperviseContract">
            <file-upload
              :fileSize="100"
              :limit="1"
              :preview="true"
              :delBtn="false"
              v-model="form.livestockFileSuperviseContract"
              :isShowTip="false"
            ></file-upload>
          </el-form-item>
          <el-form-item label="防疫记录：" v-show="form.livestockFileVaccineRecord">
            <file-upload
              :fileSize="100"
              :limit="1"
              :preview="true"
              :delBtn="false"
              v-model="form.livestockFileVaccineRecord"
              :isShowTip="false"
            ></file-upload>
          </el-form-item>
          <el-form-item label="保险单：" v-show="form.livestockFileInsurance">
            <file-upload
              :fileSize="100"
              :limit="1"
              :preview="true"
              :delBtn="false"
              v-model="form.livestockFileInsurance"
              :isShowTip="false"
            ></file-upload>
          </el-form-item>
          <el-form-item label="村镇证明：" v-show="form.livestockFileGazhaTestify">
            <file-upload
              :fileSize="100"
              :limit="1"
              :preview="true"
              :delBtn="false"
              v-model="form.livestockFileGazhaTestify"
              :isShowTip="false"
            ></file-upload>
          </el-form-item>
          <el-form-item label="贷款单：" v-show="form.fileLoan">
            <file-upload
              :fileSize="100"
              :limit="1"
              :preview="true"
              :delBtn="false"
              v-model="form.fileLoan"
              :isShowTip="false"
            ></file-upload>
          </el-form-item>
          <el-form-item label="租赁合同：" v-show="form.fileLeaseContract">
            <file-upload
              :fileSize="100"
              :limit="1"
              :preview="true"
              :delBtn="false"
              v-model="form.fileLeaseContract"
              :isShowTip="false"
            ></file-upload>
          </el-form-item>
          <el-form-item label="其他：" v-show="form.fileOther">
            <file-upload
              :fileSize="100"
              :limit="1"
              :preview="true"
              :delBtn="false"
              v-model="form.fileOther"
              :isShowTip="false"
            ></file-upload>
          </el-form-item>
        </el-form>
      </el-row>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "",
  data() {
    return {
      open: false,
      form: {
        livestockFileIntentionList: "", //监管意向
        livestockFileCreditInvestigation: "", //调研表
        livestockFileRegulatoryScheme: "", //监管方案
        livestockFileTripleAgreement: "", //三方协议
        livestockFileSuperviseContract: "", //监管合同
        livestockFileVaccineRecord: "", //防疫记录
        livestockFileInsurance: "", //保险单
        livestockFileGazhaTestify: "", //村镇证明
        fileLoan: "", //贷款单
        fileLeaseContract: "", //租赁合同
        fileOther: "", //其他文件
      },
    };
  },
  methods: {},
};
</script>
<style lang="scss" scoped>
/* @import url(); 引入css类 */
.fileView {
  .name {
    width: 100px;
  }
}
</style>
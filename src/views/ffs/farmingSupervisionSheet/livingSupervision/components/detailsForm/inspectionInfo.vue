<template>
  <div>
    <el-dialog
      :title="inspectionInfomData.title"
      :visible.sync="inspectionInfomData.open"
      width="1250px"
      :close-on-click-modal="false"
      @close="close"
      class="fieldList"
      append-to-body
    >
      <el-descriptions title="基本信息" :column="3" :contentStyle="CS" :label-style="LS">
        <div slot="title" style="display:flex;justify-content: space-between ;width: 1210px;">
          <span>基本信息</span>
          <span class="qihsd mlt20">巡检结果：{{patrolStatusName(info.patrolStatus)}}</span>
        </div>
        <el-descriptions-item label="巡检位置">{{info.patrolLivestockList[0].patrolPos}}</el-descriptions-item>
        <el-descriptions-item label="耳标掉标情况">{{info.patrolLivestockList[0].eartagSituation}}</el-descriptions-item>
        <!-- <el-descriptions-item label="pda盘点数量">{{info.patrolLivestockList[0].patrolLivestockCount}}</el-descriptions-item> -->
        <el-descriptions-item label="现有基础母牛总数量">{{info.patrolLivestockList[0].catagory3Count}}</el-descriptions-item>
        <el-descriptions-item label="现有牛犊数量">{{info.patrolLivestockList[0].patrolCalfCount}}</el-descriptions-item>
        <el-descriptions-item label="保踹母牛">{{info.patrolLivestockList[0].catagory1Count}}</el-descriptions-item>
        <el-descriptions-item label="预计出栏">{{info.patrolLivestockList[0].planSlaughter}}</el-descriptions-item>
        <el-descriptions-item label="出栏说明">{{info.patrolLivestockList[0].slaughterRemark}}</el-descriptions-item>
        <el-descriptions-item label="其它母牛">{{info.patrolLivestockList[0].patrolOtherCount}}</el-descriptions-item>
        <el-descriptions-item label="淘汰母牛">{{info.patrolLivestockList[0].catagory2Count}}</el-descriptions-item>
        <el-descriptions-item label="死亡数量">{{info.patrolLivestockList[0].deadCount}}</el-descriptions-item>
      </el-descriptions>

      <el-descriptions title="设备信息" :column="3" :contentStyle="CS" :label-style="LS">
        <el-descriptions-item
          label="监控设备"
        >{{info.patrolLivestockList[0].equipmentMonitorStatus==1?'正常':'异常'}}</el-descriptions-item>
        <el-descriptions-item
          label="其它设备"
        >{{info.patrolLivestockList[0].equipmentOtherStatus==1?'正常':'异常'}}</el-descriptions-item>
        <el-descriptions-item label="异常说明">{{info.patrolLivestockList[0].equipmentMonitorDesc}}</el-descriptions-item>
        <el-descriptions-item label="备注">{{info.patrolLivestockList[0].equipmentOtherDesc}}</el-descriptions-item>
      </el-descriptions>

      <el-descriptions title="巡检员信息" :column="3" :contentStyle="CS" :label-style="LS">
        <el-descriptions-item label="被监管方签字">
          <el-image
            class="qianmad"
            :src="picPath(info.patrolLivestockList[0].patrolSign)"
            :preview-src-list="srcList"
            @click="bigImage(picPath(info.patrolLivestockList[0].patrolSign))"
          ></el-image>
        </el-descriptions-item>
        <el-descriptions-item label="巡检员">{{info.patrolUserName}}</el-descriptions-item>
        <el-descriptions-item label="巡检员联系电话">{{info.patrolUserPhone}}</el-descriptions-item>
        <el-descriptions-item label="巡检日期">{{info.patrolLivestockList[0].patrolTime.split(' ')[0]}}</el-descriptions-item>
      </el-descriptions>

      <div>
        <el-image
          class="video-img"
          v-for="(item,index) in info.patrolLivestockList[0].patrolImgArr"
          :key="item"
          :src="item"
          :preview-src-list="srcList"
          @click="bigImage(item)"
        ></el-image>

        <video
          class="video-img"
          v-for="(item,index) in info.patrolLivestockList[0].patrolVideoArr"
          :key="item"
          :src="item"
          controls="controls"
        ></video>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button type="info" @click="close">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { inspectionInfo } from "@/api/ffs/farmingSupervisionSheet/livingSupervisionApi";
import { getFilePath } from "@/utils/east.js";
export default {
  name: "inspectionInfo",
  props: {
    inspectionInfomData: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      CS: {
        "max-width": "300px", //最小宽度
        "word-break": "break-all", //过长时自动换行
      },
      LS: {
        "word-break": "keep-all",
        color: "#000",
      },

      queryParams: { fileName: "", superviseType: "" },
      form: {
        fileName: "",
        datetime: "",
        imagesArr: [
          { url: "dev/2022/09/4a1662446333102.jpg" },
          { url: "dev/2022/09/4a1662446333102.jpg" },
        ],
        videosArr: [{ url: "dev/2022/09/ar1662446361190.mp4" }],
      },

      // 遮罩层
      loading: true,
      // 总条数
      total: 3,
      // 表格数据
      list: [
        {
          fileName: "嚯哈哈",
          orderNum: "13201860552ABCD3",
          mobile: "13201860552",
          money: "30.00",
          createTime: "2022-12-12 10:10:11",
        },
      ],
      srcList: [],
      info: {
        patrolLivestockList: [{ patrolPos: "" }],
      },
    };
  },
  created() {
    this.getDetails();
  },
  computed: {
    patrolStatusName() {
      return (val) => {
        if (val == 0) {
          return "正常";
        } else {
          return "异常";
        }
      };
    },
  },
  methods: {
    bigImage(url) {
      this.srcList = [];
      this.srcList.push(url);
    },
    /** 查询巡检记录详情 */
    getDetails(id) {
      inspectionInfo({ ids: [this.inspectionInfomData.patrolId] }).then(
        (res) => {
          this.loading = false;
          if (res.code == 200) {
            res.result.patrolLivestockList[0].patrolImgArr = getFilePath(
              res.result.patrolLivestockList[0].patrolImg
            );
            res.result.patrolLivestockList[0].patrolVideoArr = getFilePath(
              res.result.patrolLivestockList[0].patrolVideo
            );

            console.log("查询巡检记录详情------", res);
            this.info = res.result;
          } else {
            this.$message.error(res.message);
          }
        }
      );
    },
    close() {
      this.$emit("close");
    },
  },
};
</script>

<style lang="scss">
.fieldList {
  .el-dialog__header {
    background-color: #f4f4f4;
  }

  .el-dialog__footer {
    text-align: center;
  }

  .selectWidth {
    width: 100%;
  }
}
.head-title {
  font-size: 16px;
  color: #999;
  margin-bottom: 20px;
}
.qihsd {
  color: red;
}
.qianmad {
  height: 50px;
  margin-top: -20px;
}
.mlt20 {
  margin-left: 20px;
}
.f {
  display: flex;
}
.video-img {
  width: 220px;
  height: 180px;
  padding: 0 5px;
  background: #eee;
  border-radius: 8px;
  overflow: hidden;
  margin-right: 20px;
  margin-bottom: 30px;
}
</style>

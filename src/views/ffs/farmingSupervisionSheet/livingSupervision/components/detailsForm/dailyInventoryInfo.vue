<template>
  <div>
    <el-dialog
      :title="inspectionInfomData.title"
      :visible.sync="inspectionInfomData.open"
      width="1250px"
      :close-on-click-modal="false"
      @close="close"
      class="inventory"
      append-to-body
    >
    <!-- 自动盘点 -->
    <el-table :data="info?.detailsList" v-show="info?.patrolType==1||info?.patrolType==4" border>
        <el-table-column label="序号" type="index"></el-table-column>
        <el-table-column label="耳标编号" prop="eartagNo"></el-table-column>
        <el-table-column label="活畜类别" prop="typeName"></el-table-column>
        <el-table-column label="活畜品种" prop="varietiesName"></el-table-column>
        <el-table-column label="活畜类型" prop="categoryName"></el-table-column>
        <!-- <el-table-column label="养殖场名称" prop="categoryName"></el-table-column> -->
        <el-table-column label="盘点状态" >
            <template >{{ formatStatus(info.patrolStatus) }}</template>
        </el-table-column>
        <el-table-column label="当天首次点时间" prop="firstPatrolTime" min-width="120px"></el-table-column>
    </el-table>
      <!-- 人工盘点 -->
      <div v-show="info?.patrolType!=1||info?.patrolType!=4">
        <el-row :gutter="20" class="inventory-padding">
            <el-col :span="24"  class="inventory-title">养殖场名称：{{ info?.pastureName}}</el-col>
        </el-row>
        <el-row :gutter="20" class="inventory-padding">
            <el-col :span="24">养殖场地址：{{info?.pastureAddress}}</el-col>
        </el-row>
        <el-row :gutter="20" class="inventory-padding">
            <el-col :span="8">监管数量：{{info?.mortgageCount||0}}</el-col>
            <el-col :span="8">差异数量：{{ (info.patrolCount||0)-(info.mortgageCount||0) }}</el-col>
            <el-col :span="8">实盘数量：{{info?.patrolCount||0}}</el-col>
        </el-row>
        <!-- PDA盘点 -->
        <el-table :data="info?.detailsList" v-show="info?.patrolType==2" border>
            <el-table-column label="序号" type="index"></el-table-column>
            <el-table-column label="耳标编号" prop="eartagNo"></el-table-column>
            <el-table-column label="活畜类别" prop="typeName"></el-table-column>
            <el-table-column label="活畜品种" prop="varietiesName"></el-table-column>
            <el-table-column label="活畜类型" prop="categoryName"></el-table-column>
            <el-table-column label="盘点状态" >
                <template >{{ formatStatus(info.patrolStatus) }}</template>
            </el-table-column>
        </el-table>
        <el-row :gutter="20" class="inventory-padding">
            <el-col :span="24" class="inventory-title">盘点说明：</el-col>
        </el-row>
        <el-row :gutter="20" class="inventory-padding">
            <el-col :span="24">{{ info?.remark }}</el-col>
        </el-row>
        <el-row :gutter="20" class="inventory-padding">
            <el-col :span="24" class="inventory-padding inventory-title inventory-f" >
                用户签字：
                <el-image class="inventory-img" :src="picPath(info?.patrolSign)" v-show="info?.patrolSign" ></el-image>
            </el-col>
        </el-row>
        <el-row :gutter="20" class="inventory-padding inventory-title">
            <el-col :span="24" >盘点日期：{{ info?.patrolTime?info.patrolTime.split(" ")[0]:'' }}</el-col>
        </el-row>
        <el-row :gutter="20" class="inventory-padding">
            <el-image class="inventory-img" v-for="(item) in handelUrl(info?.patrolImg)" :key="item.url" :src="picPath(item.url)" @click="bigImg(picPath(item.url))"></el-image>
            <video class="inventory-img" v-for="(item) in handelUrl(info?.patrolVideo)" :key="item.url" :src="picPath(item.url)" controls  ></video>
        </el-row>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="info" @click="close">关闭</el-button>
      </span>
      <el-image-viewer v-if="showViewer" :on-close="() => {showViewer = false;}" :url-list="imgList" style="z-index:9999"/>
    </el-dialog>
  </div>
</template>

  <script>
import { queryById } from "@/api/ffs/farmingSupervisionSheet/livingSupervisionApi";
import ElImageViewer from "element-ui/packages/image/src/image-viewer";
export default {
  name: "inspectionInfo",
  components: {
    ElImageViewer

  },
  props: {
    inspectionInfomData: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      list: [],
      info:{},
      imgList: [],
      showViewer: false,
    };
  },
  created() {
    this.$nextTick(()=>{
        this.getDetails();
    })

  },
  computed: {
    formatStatus() {
      return (val) => {
        return val==0?'正常':'异常';
      };
    },
    handelImage() {
      return (list) => {
       if(list){
        return list.split(',')
       }else{
        return []
       }
      };
    },
    handelUrl(){
        return (url)=>{
            if(url){
                return JSON.parse(url)
            }else{
                return []
            }
        }
    }
  },
  methods: {
    /** 查询巡检记录详情 */
    getDetails() {
      queryById({ patrolId: this.inspectionInfomData.id ,patrolTime:this.inspectionInfomData.time}).then((res) => {
      this.info=res.result
      });
    },
    bigImg(list){
        this.showViewer=true
       this.imgList.push(list);
    },
    close() {
      this.$emit("close");
    },
  },
};
</script>

  <style lang="scss">
.inventory {
    .el-dialog__body{
        padding: 10px 30px
    }
  .el-dialog__header {
    background-color: #f4f4f4;
  }

  .el-dialog__footer {
    text-align: center;
  }
  &-title{
    font-size: 15px;
    font-weight: 700;
  }
  &-padding{
    padding: 10px 0;
  }
  &-img{
    width: 150px;
    height: 150px;
    padding-left: 10px;
  }
  &-f{
    display: flex;
    text-align: justify;
  }
}
</style>



<template>
  <div>
    <el-table v-loading="loading" :data="list" border>
      <el-table-column label="序号" type="index" fixed></el-table-column>
      <el-table-column label="今日巡检" prop="patrolStatus" :formatter="patrolStatusName"></el-table-column>
      <el-table-column label="抵押活畜数量" prop="superviseIvestockCount"></el-table-column>
      <!-- <el-table-column label="存栏活畜数量" prop="patrolLivestockCount"></el-table-column> -->
      <el-table-column label="巡检员" prop="patrolUserName"></el-table-column>
      <el-table-column label="巡检员联系电话" prop="patrolUserPhone"></el-table-column>
      <el-table-column label="巡检日期" prop="patrolTime" :formatter="patrolTimeName"></el-table-column>
      <el-table-column label="巡检位置" prop="patrolPosMain" show-overflow-tooltip></el-table-column>
      <el-table-column label="操作" align="center" fixed="right">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-info"
            @click="handleAddd(scope.row.patrolId)"
          >查看详情</el-button>
          <el-button
          v-hasPermi="['ffs:supervision:addressTime']"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="editAddresTime(scope.row)"
          >修改</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <inspection-info
      :inspectionInfomData="inspectionInfomData"
      v-if="inspectionInfomData.open"
      @close="close"
      ref="detailsFormRef"
    />
    <editTimeAddress ref="editTimeAddress" @refresh="refresh"></editTimeAddress>
  </div>
</template>

<script>
import inspectionInfo from "./inspectionInfo.vue";
import editTimeAddress from '../editTimeAddress.vue'
import { inspectionLog } from "@/api/ffs/farmingSupervisionSheet/livingSupervisionApi";
export default {
  name: "dailyInspection",
  components: { inspectionInfo ,editTimeAddress},
  props: {
    info: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      queryParams: {
        patrolSuperviseNum: "",
        pageNum: 1,
        pageSize: 10,
      },

      inspectionInfomData: {
        open: false,
        id: "",
      },

      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 表格数据
      list: [],
    };
  },
  created() {},

  watch: {
    info(val) {
      if (val) {
        this.queryParams.patrolSuperviseNum = val.superviseId;
        // this.queryParams.patrolSuperviseNum = "**********";
        this.getList();
      }
    },
  },
  computed: {
    patrolStatusName() {
      return (r,c,val) => {
        if (val == '0') {
          return "正常";
        } else {
          return "异常";
        }
      };
    },
    patrolTimeName() {
      return (row, com, val) => {
        if (row.patrolLivestockList.length) {
          return row.patrolLivestockList[0].patrolTime.split(' ')[0];
        }
        return "";
      };
    },
  },

  methods: {
    //详情
    handleAddd(patrolId) {
      this.inspectionInfomData.title = "巡检详情";
      this.inspectionInfomData.open = true;
      this.inspectionInfomData.patrolId = patrolId;
    },
    //修改巡检时间和地址
   editAddresTime(row){
    this.$refs.editTimeAddress.open=true
    this.$refs.editTimeAddress.form.patrolId=row.patrolId
    this.$refs.editTimeAddress.form.patrolTimeMain=row.patrolTimeMain
    this.$refs.editTimeAddress.form.patrolPosMain  =row.patrolPosMain
   },
    //关闭弹框
    close() {
      this.inspectionInfomData.open = false;
    },
    refresh(){
        this.getList()
    },
    /** 查询列表 */
    getList() {
      this.loading = false;
      inspectionLog(this.queryParams).then((res) => {
        this.loading = false;
        if (res.code == 200) {
          this.list = res.result.list || [];
          this.total = Number(res.result.total || 0);
        } else {
          this.$message.error(res.message);
        }
      });
    },
  },
};
</script>

<style lang="scss">
.fieldList {
  .el-dialog__header {
    background-color: #f4f4f4;
  }

  .el-dialog__footer {
    text-align: center;
  }

  .selectWidth {
    width: 100%;
  }
}
</style>

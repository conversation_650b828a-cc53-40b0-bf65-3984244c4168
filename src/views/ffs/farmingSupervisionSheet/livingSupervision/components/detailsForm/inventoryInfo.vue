<template>
    <div>
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
            <!-- <el-form-item label="盘点时间">
             <el-date-picker
            v-model="dateRange"
            style="width: 240px"
            value-format="yyyy-MM-dd"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
        </el-form-item> -->
          <el-form-item label="盘点方式" prop="patrolType">
          <el-select v-model="queryParams.patrolType" clearable>
            <el-option label="全部" value="" />
            <el-option v-for="(item,index) in inventoryList" :key="index" :label="item.name" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
        </el-form>
      <el-table  :data="list" border>
        <el-table-column label="盘点方式" prop="patrolType" :formatter="handelPatrolType"></el-table-column>
        <el-table-column label="监管活畜数量" prop="mortgageCount"></el-table-column>
        <el-table-column label="盘点数量" prop="patrolCount"></el-table-column>
        <el-table-column label="差异数量" prop="differenceCount">
            <template slot-scope="scope">{{ (scope.row.patrolCount||0)-(scope.row.mortgageCount||0) }}</template>
        </el-table-column>
        <el-table-column label="盘点时间" prop="patrolTime">
            <template slot-scope="scope">
                {{ scope.row.patrolTime? scope.row.patrolTime.split(" ")[0]:'' }}
            </template>
        </el-table-column>
        <el-table-column label="操作" align="center" fixed="right">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-info"
            @click="handleAddd(scope.row.patrolId,scope.row.patrolTime,scope.row.patrolType)"
          >查看详情</el-button>
        </template>
      </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
      <dailyInventoryInfo v-if="inspectionInfomData.open" :inspectionInfomData="inspectionInfomData" @close="close"></dailyInventoryInfo>
    </div>
  </template>

  <script>
  import dailyInventoryInfo from './dailyInventoryInfo.vue'
  import { patrolList } from "@/api/ffs/farmingSupervisionSheet/livingSupervisionApi";
  export default {
    name: "animalInfo",
    components:{
        dailyInventoryInfo
    },
    props: {
        superviseId: {
        type: String,
        default: '',
      },
    },
    data() {
      return {
        dateRange:[],
        // 总条数
        total: 0,
        inspectionInfomData:{
            open:false,
            title:''
        },
        inventoryList:[
            {name:'巡检盘点',value:'1'},
            {name:'设备盘点',value:'4'},
            {name:'PDA盘点',value:'2'},
            {name:'人工盘点',value:'3'},
        ],
        // 表格数据
        list: [],
        queryParams:{
            patrolType:'',
            pageNum: 1,
            pageSize: 10,
        }
      };
    },
    computed:{
        handelPatrolType() {
            return (row, com, val) => {
                let name=''
                this.inventoryList.forEach(item=>{
                    if(val==item.value){
                        name=item.name
                    }
                })
              return name
            };
        },
    },
    created() {
        this.queryParams.superviseId=this.superviseId
        this.getList()
    },
    methods: {
        //搜索
        handleQuery(){
            this.queryParams.pageNum = 1;
            // if (this.dateRange.length>0) {
            //     this.queryParams.createStartTime = this.dateRange[0];
            //     this.queryParams.createEndTime = this.dateRange[1];
            // } else {
            //     this.queryParams.createStartTime = "";
            //     this.queryParams.createEndTime = "";
            // }
            this.getList();
        },
        //重置
        resetQuery(){
            this.resetForm("queryForm");
            // this.dateRange = [];
            this.handleQuery();
        },
        getList(){
            patrolList(this.queryParams).then(res=>{
                if(res.code==200){
                    this.list=res.result.list
                    this.total=parseInt(res.result.total)
                }
            })
        },
        handleAddd(id,time,type){
            this.inspectionInfomData.open=true
            let name=''
            this.inspectionInfomData.id=id
            this.inspectionInfomData.time=time
            type==4?name='设备盘点详情':(type==3?name='人工盘点详情':name='PDA盘点详情')
            this.inspectionInfomData.title=name
        },
        close(){
            this.inspectionInfomData.open=false
        }
    },
  };
  </script>

  <style lang="scss">
  .fieldList {
    .el-dialog__header {
      background-color: #f4f4f4;
    }

    .el-dialog__footer {
      text-align: center;
    }

    .selectWidth {
      width: 100%;
    }
  }
  </style>

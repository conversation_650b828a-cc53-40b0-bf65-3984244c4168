<template>
    <div>
        <el-form :model="form" :rules="rules" ref="ruleForm" label-width="165px">
            <el-form-item label="续单原因：" prop="operationType">
                <el-radio-group v-model="form.operationType">
                    <el-radio :label="1">展期</el-radio>
                    <el-radio :label="2">续贷</el-radio>
                    <el-radio :label="3">续约</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="续单后合同编号：" prop="contractNo">
                <el-input v-model="form.contractNo"  clearable placeholder="请输入续单后的合同编号" >
                </el-input>
            </el-form-item>
            <el-form-item label="续单后用信金额：" prop="superviseAmount" v-if="form.superviseType == 1">
                <el-input v-model="form.superviseAmount" type="number" clearable placeholder="请输入续单后用信金额" @change="handelRate">
                    <template slot="append">万元</template>
                </el-input>
            </el-form-item>
            <el-form-item label="续单后质押货值：" prop="mortgageTotalAmount" v-if="form.superviseType == 5">
                <el-input v-model="form.mortgageTotalAmount" clearable placeholder="请输入续单后质押货值" @change="handelRate">
                    <template slot="append">万元</template>
                </el-input>
            </el-form-item>
            <el-form-item label="续单后活畜数量：" prop="mortgageCount" v-if="form.superviseType == 1">
                <el-input v-model="form.mortgageCount" clearable type="number" oninput="value=value.replace(/[^\d.]/g,'')" placeholder="请输入活畜数量"></el-input>
            </el-form-item>
            <el-form-item label="续单后每天服务费金额" prop="dayServiceFee">
                <el-input v-model="form.dayServiceFee" clearable placeholder="请输入续单后每天服务费金额" type="number" @change="getEstimatedRevenue">
                    <template slot="append">元/天</template>
                </el-input>
                    </el-form-item>
                        <el-form-item label="续单周期：" prop="superviseLimit">
                        <el-select
                        v-model="form.superviseLimit"
                        placeholder="请选择续单周期"
                        class="inputWidth"
                        @change="changeTerm"
                        >
                        <el-option v-for="item in 36 " :key="item.item" :label="item+'个月'" :value="item"></el-option>
                        </el-select>
                    </el-form-item>
            <el-form-item label="续单后服务费率" prop="superviseServiceRate">
                <el-input v-model="form.superviseServiceRate" clearable  type="number" readonly>
                    <template slot="append">%</template>
                </el-input>
            </el-form-item>
            <el-form-item label="保险公司" prop="insuranceCompanyName" v-if="form.superviseType==1">
                <el-input v-model="form.insuranceCompanyName" clearable placeholder="请输入保险公司名称"  >
                </el-input>
            </el-form-item>
            <el-form-item label="预计收入" prop="estimatedRevenue">
                <el-input v-model.number="form.estimatedRevenue" clearable placeholder="请输入预计收入" type="number" oninput="if(value.length>10)value=value.slice(0,10)" readonly>
                    <template slot="append">元</template>
                </el-input>
            </el-form-item>
            <el-form-item label="备注" prop="mark">
                <el-input type="textarea" v-model="form.mark"></el-input>
            </el-form-item>
        </el-form>
    </div>
</template>
    
<script>
var dayjs = require("dayjs");
import { toFixed2 } from "@/utils/east.js";
export default {
    props: {
        info: {
            type: Object,
            default: () => { },
        },
        superviseInfo:{
            type:Object,
            default:{}
        }
    },
    data() {
        // 默认价格
        const twoPoint = (rule, value, callback) => {
            if (value <= 0) {
                return callback(new Error("只能输入大于0的数字"));
            } else {
                if (!(/^\d+\.?\d{0,2}$/.test(value))) {
                    return callback(new Error("小数位最多不超过两位"));
                } else {
                    callback();
                }
            }
        };
        //服务费率
        const rate=(rule, value, callback)=>{
            if(value<=0){
                return callback(new Error("服务费率不能小于0，请调整服务费金额"))
            }if(value>100){
                return callback(new Error("服务费率不能大于100，请调整服务费金额"))
            }else{
                return callback()
            }
        }
        return {
            isDisabled: {
                disabledDate: (time) => {
                    let minData = dayjs(this.superviseEnd).add(1, "day")
                    this.dataDefaultValue = dayjs(minData).format("YYYY-MM-DD");
                    return time.getTime() < dayjs(minData).valueOf();
                },
            },
            dataDefaultValue: Date.now(),
            lastServideFee: {},//最后一次修改的服务费
            superviseEnd:'',
            form: {
                superviseId: null,
                contractNo:'',//合同编号
                insuranceCompanyName:'',//保险公司名称
                superviseType: null, //1表示活体2仓单,
                operationType: null, //操作类型
                superviseAmount: null, //续单用信金额
                mortgageCount: null, //活畜数量
                mortgageTotalAmount: null, //质押货值
                estimatedRevenue: null, //预计收入
                superviseServiceRate: null, //续单后的服务费率
                dayServiceFee: '',
                effectTime: "",
                closeTime: "",
                mark: "",
                operatorId: '',
                operatorName: '',
                superviseLimit:''//续单周期
            },
            disabled: false,
            rules: {
                insuranceCompanyName:[
                { required: true, message: "请输入保险公司名称", trigger: "blur" },
                ],
                operationType: [
                    { required: true, message: "请选择续单原因", trigger: "blur" },
                ],
                superviseAmount: [
                    { required: true, message: "请填写续单后用信金额", trigger: "blur" },
                    {
                        validator: twoPoint,
                        trigger: ['change', 'blur'],
                    },
                ],
                mortgageTotalAmount: [
                    { required: true, message: "请填写续单后质押货值", trigger: "blur" },
                    {
                        validator: twoPoint,
                        trigger: ['change', 'blur'],
                    },
                ],
                mortgageCount: [
                    {
                        required: true,
                        message: "请填写续单后抵押活畜数量",
                        trigger: "blur",
                    },
                ],
                superviseLimit: [
                    { required: true, message: "请选续单周期", trigger: "blur" },
                ],
                superviseServiceRate:[
                    {
                        validator: rate,
                        trigger: ['change', 'blur'],
                    }
                ],
                dayServiceFee: [
                    {
                        required: true,
                        message: "请填写续单后每天服务费金额",
                        trigger: "blur",
                    },
                    {
                        validator: twoPoint,
                        trigger: ["blur", "change"],
                    },
                ],
            },
        };
    },
    created(){
        let user=this.$store.state.user.user
        this.form.operatorId=user.userId
        this.form.operatorName=user.corprateName||user.nickName||user.userName
        this.superviseEnd=this.superviseInfo.superviseEnd
        this.form.superviseType=this.superviseInfo.superviseType
        this.form.superviseId=this.superviseInfo.superviseId
    },
    methods: {
        changeTerm(){
            this.form.closeTime=dayjs(this.superviseEnd).add(this.form.superviseLimit, "month").format("YYYY-MM-DD");
            this.getEstimatedRevenue()
        },
        //预计收放=每天服务费金额*监管天数
        getEstimatedRevenue() {
            if (this.form.closeTime && this.form.dayServiceFee) {
                const daySum = dayjs(dayjs(this.form.closeTime)).diff(this.form.effectTime, "day");
                this.form.estimatedRevenue = toFixed2(this.form.dayServiceFee * (daySum));
            }
            this.handelRate()

        },

        // 计算每天的服务费率   // 变更后服务费率=（每天监管服务费金额*监管天数）/变更后用信金额*100%
        handelRate() {
            if (this.form.dayServiceFee && this.form.closeTime) {
                let totalMoney = 0
                if (this.form.superviseType == 1) {
                    totalMoney = this.form.superviseAmount
                } if (this.form.superviseType == 5) {
                    totalMoney = this.form.mortgageTotalAmount
                }
                if (totalMoney) {
                    const daySum = dayjs(dayjs(this.form.closeTime)).diff(this.form.effectTime, "day");
                    this.form.superviseServiceRate = (((this.form.dayServiceFee * daySum) / (totalMoney * 10000)) * 100).toFixed(3);
                }
            }
        },
        echoInfo() {
            this.lastServideFee=this.info
            this.form.superviseAmount = (this.lastServideFee.superviseAmount ? this.lastServideFee.superviseAmount / 1000000 : this.lastServideFee.superviseAmount)
            this.form.mortgageCount = parseInt(this.lastServideFee.mortgageCount)
            this.form.mortgageTotalAmount = this.lastServideFee.mortgageTotalAmount ? this.lastServideFee.mortgageTotalAmount / 1000000 : this.lastServideFee.mortgageTotalAmount
            this.form.superviseServiceRate = (this.lastServideFee.superviseServiceRate)
            this.form.dayServiceFee = this.lastServideFee.dayServiceFee / 100
            this.form.effectTime =  this.superviseEnd
            this.form.operationType = this.lastServideFee.operationType
            this.form.insuranceCompanyName=this.lastServideFee.insuranceCompanyName
            this.form.contractNo=this.lastServideFee.contractNo
        },
        submitForm() {
            return new Promise((resolve, reject)=>{
                this.$refs["ruleForm"].validate((valid) => {
                    if (valid) {
                            resolve(true)
                        }else{
                            resolve(false)
                        }
                    });
                })
        
      },
    }
};
</script>
    
<style lang="scss" >
.postPone {
    .selectWidth {
        width: 100% !important;
    }
}
</style>
    
<template>
    <div>
        <el-dialog title="巡检时间地址修改" :visible.sync="open" width="500px" :close-on-click-modal="false" @close="refuse" class="editTimeAddress" append-to-body>
            <el-form :model="form" ref="form" size="small" label-width="90px" :rules="rules">
                <el-form-item label="巡检时间" prop="patrolTimeMain">
                    <el-date-picker v-model="form.patrolTimeMain" type="date" placeholder="选择日期" value-format="yyyy-MM-dd" style="width: 100%;" @change="selectData">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="巡检地址" prop="patrolPosMain">
                    <el-input type="textarea" v-model="form.patrolPosMain" rows="3" maxlength="200" show-word-limit></el-input>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer editTimeAddress">
                <el-button @click="refuse">关闭</el-button>
                <el-button type="primary" @click="submitForm">提交</el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script>
import {editPatrolTimeAndPos } from "@/api/ffs/farmingSupervisionSheet/livingSupervisionApi";
export default {

    data() {
        return {
            open: false,
            form: {
                patrolId: '',
                patrolTimeMain: '',
                patrolPosMain: ''
            },
            rules: {
                patrolPosMain: [
                    { required: true, message: '请输入巡检地址', trigger: 'blur' },
                    { max: 200, message: '最大长度不能超过255个字', trigger: 'blur' }
                ],
                patrolTimeMain: [
                    { required: true, message: '巡检时间不能为空', trigger: 'blur' }
                ],
            }
        };
    },
    created() {

    },
    methods: {
        submitForm() {
            this.$refs['form'].validate((valid) => {
                if (valid) {
                    this.$confirm('请注意，修改巡检时间可能会影响畜牧师分佣金额，请您谨慎操作！', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                       this.submit()
                    })
                }
            });
        },
        selectData(){
            var date = new Date()
            var hours = date.getHours() < 10 ? '0' + date.getHours() : date.getHours()
            var minutes = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()
            var seconds = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds()
            this.form.patrolTimeMain=this.form.patrolTimeMain+" "+`${hours}:${minutes}:${seconds}`
        },
        //提交数据
        submit() {
            editPatrolTimeAndPos(this.form).then(res=>{
                if(res.code==200){
                    this.$emit('refresh')
                    this.$message.success('操作成功')
                    this.open=false
                }
            })
        },
        refuse() {
            this.open = false
        },
    },
};
</script>

<style lang="scss" scoped>
.editTimeAddress {
    display: flex;
    justify-content: center;
    align-items: center;
}
</style>

<template>
  <div>
    <el-dialog
      :title="addFormData.title"
      :visible.sync="addFormData.open"
      width="1200px"
      :close-on-click-modal="false"
      @close="close"
      class="fieldList"
    >
      <div class="head-title">
        <el-steps :active="stepsActive" finish-status="success" align-center>
          <el-step title="选择监管关系"></el-step>
          <el-step title="监管信息上传"></el-step>
          <el-step title="监管信息设置"></el-step>
        </el-steps>
      </div>
      <step-oen v-show="stepsActive==1" :infoData="addFormData.info" ref="stepOenRef" />
      <step-two v-show="stepsActive==2" :infoData="addFormData.info" ref="stepTwoRef" />
      <step-three v-show="stepsActive==3" :infoData="addFormData.info" ref="stepThreeRef" />

      <span slot="footer" class="dialog-footer" v-show="!addFormData.disable">
        <el-button type="danger" @click="handleStep(-1)" v-show="stepsActive!=1">上一步</el-button>
        <el-button type="danger" @click="handleStep(1)" v-show="stepsActive<=2">下一步</el-button>
        <el-button type="primary" @click="submitForm(0)" v-show="stepsActive>=3">提交</el-button>
        <el-button type="info" @click="submitForm(1)" v-show="stepsActive!=1">保存草稿</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import stepOen from "./stepOen";
import stepTwo from "./stepTwo";
import stepThree from "./stepThree";
export default {
  name: "livingSupervaddForm",
  components: {
    stepOen,
    stepTwo,
    stepThree,
  },
  props: {
    addFormData: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      stepsActive: 1,
      stepNum:null
    };
  },
  created() {},
  methods: {
    close() {
      this.$store.commit("RESET_ADD_FROM");
      this.$emit("close");
    },
    /** 提交按钮 */
    async submitForm(eq) {
        if (this.stepsActive == 2) {
            const fromTwo = this.$refs.stepTwoRef.submitTwo();
            if (!fromTwo) return false;
         }
         this.$refs.stepThreeRef.handlerSave()
      this.$store.commit("SET_ADD_FROM_SUPERVIS_TYPE", 5);
      let subfnstr = "submitSuperviseSave";
      if (eq == 0) {
        const fromThree = this.$refs.stepThreeRef.submitThree();
        if (!fromThree) return;
      }
      if (eq == 1) {
        subfnstr = "superviseSaveDraft";
      }

      this.$modal.loading("提交中，请耐心等待...");
      this.$store
        .dispatch(subfnstr)
        .then((response) => {
          this.$store.commit("RESET_ADD_FROM");
          this.$modal.closeLoading();
          if (response.code == 200) {
            if (eq == 0) {
              this.$confirm("是否为监管单设置分佣规则?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
              })
                .then(() => {
                  console.log("response：", response.result.data);
                  this.$emit("commissionopen", {
                    superviseType: 1,
                    superviseId: response.result.data,
                  });
                })
                .catch(() => {
                  this.$emit("close");
                  this.$emit("closeAddBaseForm");
                });
            } else {
              this.$emit("close");
              this.$emit("closeAddBaseForm");
            }
            this.$modal.msgSuccess("提交成功");
            this.$emit("refresh");
          }
        })
        .catch(() => {
          this.$modal.closeLoading();
        });
    },
    //上一步下一步操作
    handleStep(num) {
        this.stepNum=num
            if (num == 1) {
                if (this.stepsActive == 1) {
                const fromOen = this.$refs.stepOenRef.submitOen();
                if (!fromOen) return false
                }
                if (this.stepsActive == 2) {
                const fromTwo = this.$refs.stepTwoRef.submitTwo();
                if (!fromTwo) return false;
                }
            }
        if (num == 1 && this.stepsActive++ > 3) return false;
        if (num == -1 && this.stepsActive-- < 1)  return false;
    },
  },
};
</script>

<style lang="scss">
.fieldList {
  .el-dialog__header {
    background-color: #f4f4f4;
  }

  .el-dialog__footer {
    text-align: center;
  }

  .selectWidth {
    width: 100%;
  }
}
</style>

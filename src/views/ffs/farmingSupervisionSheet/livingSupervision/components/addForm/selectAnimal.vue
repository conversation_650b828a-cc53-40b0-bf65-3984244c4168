<template>
  <div>
    <el-dialog
      :title="selectAnimalFormData.title"
      :visible.sync="selectAnimalFormData.open"
      width="1050px"
      :close-on-click-modal="false"
      @close="close"
      class="fieldList"
      append-to-body
    >
      <el-row :gutter="10" class="mb8">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
          <el-form-item label="选择养殖场：" prop="pastureId">
            <el-select v-model="queryParams.pastureId" clearable>
              <el-option
                v-for="it in pastureList"
                :key="it.pastureId"
                :label="it.pastureName||it.userName||it.userPhone"
                :value="it.pastureId"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="耳标编号：" prop="earTagNo">
            <el-input v-model="queryParams.earTagNo" placeholder="请输入输入耳标编号" clearable />
          </el-form-item>

          <el-form-item label="开始编号：" prop="earTagNoStart">
            <el-input v-model="queryParams.earTagNoStart" placeholder="请输入开始耳标编号" clearable />
          </el-form-item>

          <el-form-item label="结束编号：" prop="earTagNoEnd">
            <el-input v-model="queryParams.earTagNoEnd" placeholder="请输入结束耳标编号" clearable />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </el-row>

      <el-table
        border
        :data="list"
        ref="multipleTable"
        :row-key="(row) => row.livestockId"
        @selection-change="tableSelectionChange"
        @select="tableToggleRowSelection"
      >
        <el-table-column type="selection" :selectable="selectEnable" :reserve-selection="true"></el-table-column>
        <el-table-column label="序号" type="index" fixed></el-table-column>
        <el-table-column label="耳标编号" prop="earTagNo" width="155px"></el-table-column>
        <el-table-column label="活畜分类" prop="typeName"></el-table-column>
        <el-table-column label="活畜品种" prop="varietiesName"></el-table-column>
        <el-table-column label="活畜类型" prop="categoryName"></el-table-column>
        <el-table-column label="活畜月龄" prop="livestockAge" :formatter="ageName"></el-table-column>
        <el-table-column label="出生日期" prop="birthday"></el-table-column>
        <el-table-column label="养殖场名称" prop="pastureName"></el-table-column>
        <el-table-column label="养殖场地址" prop="pastureAddress" width="155px"></el-table-column>
        <!-- <el-table-column label="操作" align="center" fixed="right" width="150px">
          <template
            slot-scope="scope"
          >{{scope.row.status}}--{{scope.row.livestockId}}-{{selectEnable(scope.row)}}</template>
        </el-table-column>-->
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
      <div class="tablebewtosa skeme">
        <div>活畜数量：{{total}}</div>
        <div>已选抵押活畜数量：{{getSelectAinmalLength}}</div>
      </div>
      <span slot="footer" class="dialog-footer" v-show="!selectAnimalFormData.disable">
        <el-button @click="close">取消</el-button>
        <el-button type="primary" @click="submitForm">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  getPastureList,
  selectbatchInsert,
  pastureLivestockGroupCount,
  checkActivateLivestock
} from "@/api/ffs/supervisionSheet/livingSupervisionApi";
import { getDicts } from "@/api/system/dict/data.js";

export default {
  name: "selectAnimalf",
  props: {
    selectAnimalFormData: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      queryParams: {
        pastureId: "",
        earTagNo: "",
        earTagNoStart: "",
        earTagNoEnd: "",
        userId: "",
        status: "", //1是未监管的活畜
        pageNum: 1,
        pageSize: 10,
    
      },
      ids:[],
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 表格数据
      list: [],

      pastureList: [],
      selectAinmal: [],
      ageList: [],

      selectSource: [],
    };
  },
  created() {
    this.selectSource = JSON.parse(
      JSON.stringify(this.selectAnimalFormData.livestockList)
    );
    this.queryParams.userId = this.selectAnimalFormData.subjectUserId;
    if (this.selectAnimalFormData.operation == "edit") {
      this.queryParams.status = "";
    }
    this.initData();

  },
  computed: {
    getSelectAinmalLength() {
      let submitAinmal = this.selectAnimalFormData.livestockList.concat(
        this.selectAinmal
      );
      submitAinmal = this.removeDuplication(submitAinmal);
      let len = submitAinmal.length;
      if (this.queryParams.pastureId) {
        const nowPasture = submitAinmal.filter((item)=>  item.pastureId == this.queryParams.pastureId);
        len = nowPasture.length;
      }
      return len;
    },
    // 活畜月龄
    ageName() {
      return (r, c, val) => {
        let dictLabel = "";
        if (!this.ageList) return;
        this.ageList.forEach((item) => {
          if (val == item.dictValue) {
            dictLabel = item.dictLabel;
          }
        });
        return dictLabel;
      };
    },
    selectEnable() {
      return (row, rowIndex) => {
        let disable = true;
        // if (row.status == 1) {
        //   disable = true;
        // } else {
        //   disable = false;
        // }
        // if (this.selectAnimalFormData.operation == "edit") {
        //   this.selectSource.forEach((key) => {
        //     if (row.livestockId == (key.livestockId || key)) {
        //       disable = true;
        //     }
        //   });
        // }
        return disable;
      };
    },
  },
  methods: {
    /** 查询活畜列表 */
    getList() {
        if(!this.queryParams.pastureId){
            this.queryParams.pastureIdList=this.ids
            this.queryParams.checkActive=1
        }else{
           delete this.queryParams.pastureIdList
        }
        this.queryParams.superviseWay=this.$store.state.supervision.addFrom?.superviseWay
        checkActivateLivestock(this.queryParams).then((res) => {
        this.loading = false;
        if (res.code == 200) {
          const result = res.result.list || [];
          this.list = result;
          this.total = Number(res.result.total || 0);
          if (result.length == 0) {
            return;
          }
          this.selectAnimalFormData.livestockList.forEach((key) => {
            res.result.list.forEach((row) => {
              if (row.livestockId == (key.livestockId || key)) {
                this.$refs.multipleTable.toggleRowSelection(row, true);
              }
            });
          });
        } else {
          this.$message.error(res.message);
        }
      });
    },

    async initData() {
      // 活畜月龄
      getDicts("livestock_age").then((res) => {
        this.ageList = res.data || [];
      });

      // 查询养殖场列表
      getPastureList({
        userId: this.selectAnimalFormData.subjectUserId, //被监管人id
      }).then((res) => {
        if (res.code == 200) {
          const result = res.result.list || [];
          this.pastureList = result;
          this.pastureList.forEach(item=>{
            this.ids.push(item.pastureId)
          })
          this.getList();
        }
      });
    },

    //保存选择的活畜
    submitForm() {
      let submitAinmal = this.selectAnimalFormData.livestockList.concat(
        this.selectAinmal
      );
      submitAinmal = this.removeDuplication(submitAinmal);
      if (submitAinmal.length == 0) {
        this.$message({
          message: "请选择活畜",
          type: "error",
        });
        return;
      }
      pastureLivestockGroupCount({
        livestockList: submitAinmal,
      }).then((res) => {
        if (res.code == 200) {
          const result = res.result.data || [];
          result.forEach((item) => {
            item.insuranceAmount = "0";
            item.insuranceTotalAmount = "0";
            item.mortgageCount = item.superviseTotal;
            item.livestockCount = item.livestockTotal;
            submitAinmal.forEach((pit) => {
              if (item.pastureId == pit.pastureId) {
                item.pastureName = pit.pastureName;
                item.pastureAddress = pit.pastureAddress;
              }
            });
          });

          //   console.log("result：", result);
          const mergeGroup = this.mergeGroup(result);
          this.$emit("refresh", submitAinmal, result, mergeGroup || []);
          this.$emit("close");
        }
      });
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },

    //关闭弹框
    close() {
      this.$emit("close");
    },
    /** 打开创建监管单弹窗 */
    handleAddd() {
      this.$emit("addSupervision");
    },
    // 表格选择事件
    tableSelectionChange(val) {
      this.selectAinmal = val;
    },

    // 表格取消选择事件
    tableToggleRowSelection(row, selected) {
      let isCkeck = row.length && row.indexOf(selected) !== -1;
      if (!isCkeck || isCkeck == 0) {
        const index = this.selectAnimalFormData.livestockList.findIndex(
          (item) => (item.livestockId || item) === selected.livestockId
        );
        if (index >= 0) {
          this.selectAnimalFormData.livestockList.splice(index, 1);
        }
      }
    },

    //去重
    removeDuplication(list) {
      let obj = {};
      let peon = list.reduce((cur, next) => {
        obj[next.livestockId]
          ? ""
          : (obj[next.livestockId] = true && cur.push(next));

        return cur;
      }, []);
      return peon;
    },

    mergeGroup(arr) {
      var map = {},
        dest = [];
      for (var i = 0; i < arr.length; i++) {
        var ai = arr[i];
        if (!map[ai.pastureId]) {
          dest.push({
            groupPastureId: ai.pastureId,
            pastureName: ai.pastureName,
            pastureAddress: ai.pastureAddress,
            busInfoList: [ai],
          });
          map[ai.pastureId] = ai;
        } else {
          for (var j = 0; j < dest.length; j++) {
            var dj = dest[j];
            if (dj.groupPastureId == ai.pastureId) {
              dj.busInfoList.push(ai);
              break;
            }
          }
        }
      }
      //console.log('dest：',dest);
      return dest;
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep.el-table .disabledCheck .cell .el-checkbox__inner{
    display: none !important;
}

.fieldList {
  .el-dialog__header {
    background-color: #f4f4f4;
  }

  .el-dialog__footer {
    text-align: center;
  }

  .selectWidth { 
    width: 100%;
  }
}
.head-title {
  font-size: 16px;
  color: #999;
  margin-bottom: 20px;
}
.head-title span {
  color: red;
}
</style>

<!--  -->
<template>
    <div class="main">
        <el-form ref="form" :model="form" :rules="rules" label-width="120px">
            <div class="iputiirt">监管顾问</div>
            <el-row type="flex" justify="space-between">
                <el-col :span="11">
                    <el-form-item label="监管员：" prop="supervisorName">
                        <el-select clearable v-model="form.supervisorName" filterable remote reserve-keyword placeholder="请输入监管员名称/手机号码" :remote-method="remoteMethod" class="selectWidth" @change="onChange">
                            <el-option v-for="item in selectList" :key="item.userId" :label="labelName(item)" :value-key="item.userId" :value="item"></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="11">
                    <el-form-item label="客户经理：" prop="loanOfficerName">
                        <el-select v-model="form.loanOfficerName" clearable @change="onConfirmsLoanOfficer">
                            <el-option v-for="it in showshowLoanList" :key="it.userId" :label="it.corprateName || it.nickName || it.userName" :value="it" />
                        </el-select>
                        <span style="margin-left:10px" v-if="investigationInfo.pastorName">畜牧师：{{ investigationInfo.pastorName }}</span>
                    </el-form-item>
                </el-col>
            </el-row>

            <div class="iputiirt">保险公司</div>

            <el-row type="flex">
                <el-col :span="11">
                    <el-form-item label="保险公司：" prop="insuranceCompanyName">
                        <el-input v-model.trim="form.insuranceCompanyName" placeholder="请输入保险公司" maxlength="30" clearable :disabled="disabled" />
                    </el-form-item>
                </el-col>
                <div class="qiangti" style="line-height:40px;margin-left:10px">注：输入活畜所上保险公司名称，如有多个，则中间用分号（；）隔开,若没有保险公司，则填“无”。</div>
            </el-row>
            <div class="iputiirt">硬件设备</div>
            <div class="iputiirt" style="padding: 0 30px;">
                <span class="marrt20">硬件设备信息</span>
                <el-button type="primary" size="mini" class="lobtb" @click="hardware" >
                    <i class="el-icon-thumb el-icon--right"></i>
                    查看硬件设备
                </el-button>
            </div>
            <div class="iputiirt">有耳标</div>
            <div class="iputiirt" style="padding: 0 30px;">
                <span class="marrt20">抵押活畜信息</span>
                <el-button type="primary" size="mini" class="lobtb" @click="selectAnimalShow">
                    <i class="el-icon-thumb el-icon--right"></i>
                    选择抵押活畜
                </el-button>
            </div>

            <div v-for="(item, index) in satisticsList" :key="index" class="smidfieym" style="padding: 0 30px;">
                <div class="smidf">养殖场名称：{{ item.pastureName }}</div>
                <div class="smidfmid">养殖场地址：{{ item.pastureAddress }}</div>
                <el-table :data="item.busInfoList" border>
                    <el-table-column label="序号" type="index" align="cneter"></el-table-column>
                    <el-table-column label="活畜类别" align="center">
                        <template slot-scope="scope">
                            <span>{{ scope.row.typeName }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="活畜品种" prop="varietiesName" align="center">
                        <template slot-scope="scope">
                            <span>{{ scope.row.varietiesName }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="活畜类型" align="center">
                        <template slot-scope="scope">
                            <span>{{ scope.row.categoryName }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="活畜数量" min-width="100px">
                        <template slot-scope="scope">
                            <span>{{ scope.row.livestockTotal }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="抵押数量" prop="superviseTotal" align="center"></el-table-column>
                    <el-table-column label="保险金额（元/头）" prop="insuranceAmount" width="170px" align="center">
                        <template slot-scope="scope">
                            <el-input v-model="scope.row.insuranceAmount" @blur="switchShow(2, scope.row)" type="number" placeholder="请输入保险金额"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="保险总金额（元）" prop="insuranceTotalAmount" :formatter="rounding" align="center"></el-table-column>
                </el-table>
                <div class="tablebew">
                    <div>抵押活畜数量：{{ mortgageToast(item.busInfoList) }}</div>
                    <div>抵押活畜保险金额：{{ mortgageToastMoney(item.busInfoList) }}元</div>
                </div>
            </div>
            <div class="iputiirt">无耳标</div>
            <div class="iputiirt" style="padding: 0 30px;">
                <el-form-item label="选择养殖场" label-width="85px">
                    <el-select v-model="pastureID" clearable @change="selectPasture">
                        <el-option v-for="it in pastureList" :key="it.pastureId" :label="it.pastureName" :value="it.pastureId" />
                    </el-select>
                </el-form-item>
            </div>
            <div v-for="(item, index) in noEarTagSatisticsList" :key="item.pastureID" class="smidfieym" style="padding: 0 30px;">
                <div class="smidf">养殖场名称：{{ item.pastureName }}</div>
                <div class="smidfmid">养殖场地址：{{ item.pastureAddress }}</div>
                <el-table :data="item.busInfoList" border>
                    <el-table-column label="序号" type="index" align="cneter"></el-table-column>
                    <el-table-column label="活畜类别" align="center">
                        <template slot-scope="scope">
                            <span>
                                <el-select v-model="scope.row.typeName" placeholder="请选择" @change="selectTypeName(scope.row.typeName, scope.$index, scope.row)">
                                    <el-option v-for=" typeName in animalsCategory" :key="typeName.livestockId" :label="typeName.livestockName" :value="typeName.livestockName"></el-option>
                                </el-select>
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column label="活畜品种" prop="varietiesName" align="center">
                        <template slot-scope="scope">
                            <span>
                                <el-select v-model="scope.row.varietiesName" placeholder="请选择" @change="selelctValue(animalsVarieties, scope.row.varietiesName, scope.$index, 1, scope.row)">
                                    <el-option v-for=" typeName in animalsVarieties" :key="typeName.varietiesId" :label="typeName.varietiesName" :value="typeName.varietiesName"></el-option>
                                </el-select>
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column label="活畜类型" align="center">
                        <template slot-scope="scope">
                            <span>
                                <el-select v-model="scope.row.categoryName" placeholder="请选择" @change="selelctValue(animalsType, scope.row.categoryName, scope.$index, 2, scope.row)">
                                    <el-option v-for=" typeName in animalsType" :key="typeName.varietiesId" :label="typeName.categoryName" :value="typeName.categoryName"></el-option>
                                </el-select>
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column label="活畜数量" min-width="100px">
                        <template slot-scope="scope">
                            <span>
                                <el-input v-model.number="scope.row.livestockTotal" @blur="switchShow(1, scope.row)" type="number" placeholder="请输入活畜数量"></el-input>
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column label="抵押数量" prop="superviseTotal" align="center"></el-table-column>
                    <el-table-column label="保险金额（元/头）" prop="insuranceAmount" width="170px" align="center">
                        <template slot-scope="scope">
                            <el-input v-model="scope.row.insuranceAmount" @blur="switchShow(2, scope.row)" type="number" placeholder="请输入保险金额"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="保险总金额（元）" prop="insuranceTotalAmount" :formatter="rounding" align="center"></el-table-column>
                    <el-table-column label="操作" align="center">
                        <template slot-scope="scope">
                            <el-button icon="el-icon-minus" size="mini" type="text" @click="deleDom(scope.$index)" v-show="scope.$index != 0">删除</el-button>
                            <el-button v-show="item.busInfoList.length - 1 == scope.$index" icon="el-icon-plus" size="mini" type="text" @click="addDom">新增</el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <div class="tablebew">
                    <div>抵押活畜数量：{{ mortgageToast(item.busInfoList) }}</div>
                    <div>抵押活畜保险金额：{{ mortgageToastMoney(item.busInfoList) }}元</div>
                </div>
            </div>
            <div class="tablebewtosa">
                <div>抵押活畜总数量：{{ allNum }}</div>
                <div>抵押活畜保险总金额：{{ allMoney }}元</div>
            </div>
        </el-form>

        <select-animal :selectAnimalFormData="selectAnimalFormData" @close="close" v-if="selectAnimalFormData.open" @refresh="refresh"></select-animal>
        <el-dialog title="硬件设备" :visible.sync="hardwareOpen" width="1000px" :modal-append-to-body="false" :append-to-body="true" :close-on-click-modal="false" @close="hardwareOpen = false">
            <monitor :applyId="form.applyId" v-if="hardwareOpen"></monitor>
        </el-dialog>
    </div>
</template>

<script>
import { searchUser } from "@/api/system/user.js"; // 查询搜索用户  可以手机号、昵称等等信息搜索
import {
    getPastureList,
    byinvestigationId,
    loanOfficerList,
    superviseLivestockList,
    checkActivateLivestock,
    livestockNoTagAndAllList
} from "@/api/ffs/farmingSupervisionSheet/livingSupervisionApi";
import {
    livestockList,
    varietiesList,
    animalTypeList,
} from "@/api/ffs/farmingSupervisionSheet/farmingSiteManagement";
import selectAnimal from "./selectAnimal.vue";
import monitor from "@/views/ffs/supervisionSheet/siteManagement/monitor.vue";
export default {
    dicts: ["ffs_supervise_have_eratag"],
    name: "insstepThree",
    components: {
        selectAnimal,
        monitor,
    },
    props: {
        infoData: {
            type: Object,
            default: () => { },
        },
    },
    data() {
        return {
            selectAnimalFormData: {
                open: false,
                id: "",
            },
            hardwareOpen: false,
            disabled: false,
            pastureID: "", //养殖场id
            pastureUserId: "", //养殖场uiserID
            earType: "",
            satisticsListCopy: [], //编辑存储数据
            form: {
                supervisorId: "", //监管员id
                supervisorName: "", //监管员名称
                supervisorPhone: "", //监管员联系方式
                loanOfficerId: "", //信贷员id
                loanOfficerName: "", //信贷员名称
                loanOfficerPhone: "", //信贷员手机号
                insuranceCompanyName: "", //保险公司名称
                livestockList: [], //监管单关联活畜列表
                satisticsList: [], //监管单关联活畜统计列表
                haveEartag: 1, //有无耳标
                noEarTagSatisticsList: [],//无耳标数据
            },
            satisticsList: [], //监管单关联活畜统计列表
            noEarTagSatisticsList: [],//无耳标数据
            tableList: [],
            pastureList: [], //养殖场数据
            animalsCategory: [], //活畜信息
            animalsType: [], //获取类型
            animalsVarieties: [], //活畜品种
            // 表单校验
            rules: {
                loanOfficerName: [
                    { required: true, message: "请选择客户经理", trigger: "blur" },
                ],
                supervisorName: [
                    { required: true, message: "请选择监管员", trigger: "blur" },
                ],
                insuranceCompanyName: [
                    { required: true, message: "请填写保险公司", trigger: "blur" },
                ],
            },
            investigationInfo: { pastorName: "" },
            selectList: [],
            showshowLoanList: [],
        };
    },
    created() {
        if (this.infoData.operation == "edit") {
            this.$store.commit("SET_ADD_FROM", this.infoData);
            this.satisticsListCopy = JSON.parse(
                JSON.stringify(this.infoData.satisticsList)
            );
            this.getSuperviseAimialList(this.infoData.superviseId);
            this.earType = this.infoData.haveEartag;
            this.form.noEarTagSatisticsList = this.infoData.noEarTagSatisticsList || []
            if (this.form.noEarTagSatisticsList.length > 0) {
                this.pastureID = this.form.noEarTagSatisticsList[0]?.pastureId
                let pastureAddress = this.form.noEarTagSatisticsList[0]?.pastureAddress
                let pastureName = this.form.noEarTagSatisticsList[0]?.pastureName
                let obj = {
                    pastureAddress: pastureAddress,
                    pastureName: pastureName,
                    pastureID: this.pastureID,
                    busInfoList:  JSON.parse(JSON.stringify(this.form.noEarTagSatisticsList))
                };
                obj.busInfoList.forEach(item => {
                    item.superviseTotal = item.mortgageCount;
                    item.livestockTotal = item.livestockCount;
                    item.insuranceAmount = parseFloat(item.insuranceAmount).toFixed(
                        2
                    );
                })
                this.noEarTagSatisticsList.push(obj)
            }
        }
        this.getPasture();
        this.getCategory();
        this.initData();
        this.form = this.$store.getters.supervision.addFrom;
    },
    computed: {
        labelName() {
            return (val) => {
                if (!val) return;
                if (val.nickName) return val.nickName;
                if (val.userName) return val.userName;
                if (val.corprateName) return val.corprateName;
                if (val.phonenumber) return val.phonenumber;
            };
        },
        mortgageToast() {
            let num = 0;
            return (sourceData) => {
                num = 0;
                sourceData.forEach((item) => {
                    num = num + (item.superviseTotal || 0) * 1;
                });
                return num.toFixed(2);
            };
        },
        mortgageToastMoney() {
            let num = 0;
            return (sourceData) => {
                num = 0;
                sourceData.forEach((item) => {
                    num += item.insuranceTotalAmount * 1;
                });
                return num.toFixed(2);
            };
        },
        rounding() {
            return (row, column, val) => {
                if (!val) return "0.00";
                return parseFloat(val).toFixed(2);
            };
        },
        allMoney() {
            let num = 0;
            this.satisticsList.forEach((im) => {
                const itsuperviseTotal = im.busInfoList;
                itsuperviseTotal.forEach((sm) => {
                    num += sm.insuranceTotalAmount * 1;
                });
            });
            this.noEarTagSatisticsList.forEach((im) => {
                const itsuperviseTotal = im.busInfoList;
                itsuperviseTotal.forEach((sm) => {
                    num += sm.insuranceTotalAmount * 1;
                });
            });
            return parseFloat(num).toFixed(2);
        },
        allNum() {
            let num = 0;
            this.satisticsList.forEach((im) => {
                const itsuperviseTotal = im.busInfoList;
                itsuperviseTotal.forEach((sm) => {
                    num += sm.superviseTotal * 1;
                });
            });
            this.noEarTagSatisticsList.forEach((im) => {
                const itsuperviseTotal = im.busInfoList;
                itsuperviseTotal.forEach((sm) => {
                    num += (sm.superviseTotal || 0) * 1;
                });
            })
            return parseFloat(num);
        },
    },
    methods: {
        hardware() {
            this.hardwareOpen = true;
        },

        handlerSave() {
            if (this.noEarTagSatisticsList.length > 0) {
                this.form.haveEartag = 2
            } else {
                this.form.haveEartag = 1
            }
            this.pastureList.map((item) => {
                if (this.pastureID == item.pastureId) {
                    this.pastureUserId = item.userId;
                }
            });
            if (
                this.form.haveEartag == 2 &&
                this.noEarTagSatisticsList?.[0]?.busInfoList?.length > 0
            ) {
                this.noEarTagSatisticsList[0].busInfoList.forEach((item) => {
                    item.mortgageCount = item.superviseTotal;
                    item.livestockCount = item.livestockTotal;
                    item.pastureId = this.pastureID;
                    item.pastureUserId = this.pastureUserId;
                    item.pastureName = this.noEarTagSatisticsList[0].pastureName;
                    item.pastureAddress = this.noEarTagSatisticsList[0].pastureAddress;
                });
                this.form.noEarTagSatisticsList = this.noEarTagSatisticsList[0].busInfoList;
            } else {
                this.form.noEarTagSatisticsList = []
            }
        },
        // 选择活畜
        selectTypeName(value, index, row) {
            this.animalsCategory.forEach((item) => {
                if (item.livestockName == value) {
                    this.noEarTagSatisticsList[0].busInfoList[index].typeId = item.livestockId;
                    this.getVarieties(item.livestockId);
                    this.getType(item.livestockId);
                }
            });
            row.varietiesName = "";
            row.varietiesId = "";
            row.categoryName = "";
            row.categoryId = "";
        },
        //选择活畜品种及类型
        selelctValue(list, value, index, type, row) {
            list.forEach((item) => {
                if (type == 1) {
                    if (item.varietiesName == value) {
                        this.noEarTagSatisticsList[0].busInfoList[index].varietiesId =
                            item.varietiesId;
                    }
                } else {
                    if (item.categoryName == value) {
                        this.noEarTagSatisticsList[0].busInfoList[index].categoryId =
                            item.categoryId;
                    }
                }
            });
            this.handelAnimals(row, type);
        },
        //判断活畜是否重复
        handelAnimals(row, type) {
            let ids = row.typeId + row.varietiesId + row.categoryId;
            this.noEarTagSatisticsList[0].busInfoList.forEach((item, index) => {
                if (this.noEarTagSatisticsList[0].busInfoList.length - 1 > index) {
                    let name = item.typeId + item.varietiesId + item.categoryId;
                    if (ids == name) {
                        if (type == 1) {
                            row.varietiesName = "";
                            row.varietiesId = "";
                        } else {
                            row.categoryName = "";
                            row.categoryId = "";
                        }
                        this.$message({
                            message: "不能选择相同的活畜！",
                            type: "error",
                        });
                        return;
                    }
                }
            });
        },
        //获取活畜信息
        getCategory() {
            livestockList({ pageNum: 1, pageSize: 100000 }).then((res) => {
                this.animalsCategory = res.result;
            });
        },
        // 活畜类型
        getVarieties(typeId) {
            animalTypeList({
                pageNum: 1,
                pageSize: 100000,
                categoryType: typeId,
            }).then((res) => {
                this.animalsType = res.result;
            });
        },
        //或者品种
        getType(typeId) {
            varietiesList({
                pageNum: 1,
                pageSize: 100000,
                categoryType: typeId,
            }).then((res) => {
                this.animalsVarieties = res.result;
            });
        },

        // 查询养殖场信息 this.infoData.subjectUserId
        getPasture() {
            getPastureList({
                userId: this.infoData.subjectUserId || this.infoData.applyId,
            }).then((res) => {
                if (res.code == 200) {
                    this.pastureList = res.result.list || [];
                }
            });
        },
        //无耳标选择养殖场
        selectPasture() {
            let pastureAddress = "";
            let pastureName = "";
            this.pastureList.forEach((item) => {
                if (this.pastureID == item.pastureId) {
                    pastureAddress = `${item.provinceName}${item.cityName}${item.countyName}`;
                    pastureName = item.pastureName;
                }
            });
            if (this.noEarTagSatisticsList.length > 0) {
                this.noEarTagSatisticsList[0].pastureAddress = pastureAddress;
                this.noEarTagSatisticsList[0].pastureName = pastureName;
            } else {
                let busInfoList = [
                    {
                        categoryId: "",
                        categoryName: "", //获取类型
                        typeName: "", //活畜类别
                        typeId: "",
                        varietiesName: "", //活畜品种
                        varietiesId: "",
                        insuranceAmount: "0", //保险金额
                        insuranceTotalAmount: "", //保险总金额
                        livestockCount: "",
                        mortgageCount: "",
                        superviseTotal: "", //抵押数量
                        livestockTotal: "", //活畜数量
                    },
                ];
                let obj = {
                    pastureAddress: pastureAddress,
                    pastureName: pastureName,
                    busInfoList: busInfoList,
                };
                this.form.noEarTagSatisticsList = []
                this.noEarTagSatisticsList.push(obj);
            }
            if (!this.pastureID) {
                this.noEarTagSatisticsList = []
            }
        },
        //添加dom
        addDom() {
            let busInfoList = {
                categoryId: "",
                categoryName: "", //获取类型
                typeName: "", //活畜类别
                typeId: "",
                varietiesName: "", //活畜品种
                varietiesId: "",
                insuranceAmount: "0", //保险金额
                insuranceTotalAmount: "", //保险总金额
                livestockCount: "",
                mortgageCount: "",
                superviseTotal: "", //抵押数量
                livestockTotal: "", //活畜数量
            };
            this.noEarTagSatisticsList[0].busInfoList.push(busInfoList);
        },
        //删除dom
        deleDom(index) {
            this.noEarTagSatisticsList[0].busInfoList.splice(index, 1);
        },

        //提交验证
        submitThree() {
            this.handlerSave();
            if (this.form.livestockList.length == 0 && this.noEarTagSatisticsList.length == 0) {
                this.$message({
                    message: "请选择抵押活畜信息",
                    type: "error",
                });
                return false;
            }
            if (this.form.satisticsList.length == 0 && this.noEarTagSatisticsList.length == 0) {
                this.$message({
                    message: "请保存选择的抵押活畜信息",
                    type: "error",
                });
                return false;
            }
            let insuranceAmountok = true;
            let categoryId = null;
            let typeId = null;
            let varietiesId = null;
            let livestockTotal = null;
            this.satisticsList.forEach((im) => {
                const itsuperviseTotal = im.busInfoList;
                itsuperviseTotal.forEach((sm) => {
                    if (sm.insuranceAmount < 0) {
                        insuranceAmountok = false;
                    }
                });
            });

            if (this.noEarTagSatisticsList.length > 0) {
                this.noEarTagSatisticsList.forEach((im) => {
                    const itsuperviseTotal = im.busInfoList;
                    itsuperviseTotal.forEach((sm) => {
                        categoryId = sm.categoryId;
                        typeId = sm.typeId;
                        varietiesId = sm.varietiesId;
                        livestockTotal = sm.livestockTotal;
                        if (sm.insuranceAmount < 0) {
                            insuranceAmountok = false;
                        }
                    });
                })
                if (!typeId) {
                    this.$message({
                        message: "请选择活畜类别",
                        type: "error",
                    });
                    return false;
                }
                if (!varietiesId) {
                    this.$message({
                        message: "请选择活畜品种",
                        type: "error",
                    });
                    return false;
                }
                if (!categoryId) {
                    this.$message({
                        message: "请选择活畜类型",
                        type: "error",
                    });
                    return false;
                }
                if (!livestockTotal) {
                    this.$message({
                        message: "请输入活畜数量",
                        type: "error",
                    });
                    return false;
                }
            }
            if (!insuranceAmountok) {
                this.$message({
                    message: "请输入活畜保险金额",
                    type: "error",
                });
                return false;
            }

            let threeOk = false;
            this.$refs["form"].validate((valid) => {
                if (!valid) return false;
                this.form.threeOk = valid;
                threeOk = valid;
                return true;
            });
            return threeOk;
        },
        switchShow(type, val) {
            this.$modal.loading("校验数据中...");
            if (type == 2) {
                if (val.insuranceAmount < 0) {
                    val.insuranceAmount = "";
                    this.$message({
                        message: "请输入正确有效的保险金额",
                        type: "error",
                    });
                    this.$modal.closeLoading();
                    return;
                }
                this.$modal.closeLoading();
            } else {
                if (val.livestockTotal < 0) {
                    val.livestockTotal = "";
                    this.$message({
                        message: "请输入正确有效的活畜数量",
                        type: "error",
                    });
                    val.superviseTotal = null;
                    this.$modal.closeLoading();
                    return;
                }
                this.$modal.closeLoading();
                val.superviseTotal = val.livestockTotal;
            }
            if (val.insuranceAmount && val.livestockTotal) {
                this.$modal.closeLoading();
                let tempVal = parseFloat(val.insuranceAmount).toFixed(3);
                val.insuranceAmount = tempVal.substring(0, tempVal.length - 1);
                val.insuranceTotalAmount =
                    val.insuranceAmount * 1 * (val.superviseTotal * 1);
            }
            this.$modal.closeLoading();
        },
        //远程搜索
        remoteMethod(data) {
            searchUser({ phonenumber: data, userType: "00" }).then((res) => {
                if (res.code == 200) {
                    this.selectList = res.result;
                }
            });
        },
        // 确定选择监管员
        onChange(val) {
            this.form.supervisorId = val.userId;
            this.form.supervisorName = val.nickName;
            this.form.supervisorPhone = val.phonenumber;
            this.form = this.$store.getters.supervision.addFrom;
        },
        //确认信贷员
        onConfirmsLoanOfficer(val) {
            this.form.loanOfficerId = val.userId;
            this.form.loanOfficerName = val.nickName;
            this.form.loanOfficerPhone = val.phonenumber;
            this.form = this.$store.getters.supervision.addFrom;
        },
        async initData() {
            // 获取调研单详情
            if (this.infoData.intentionListId) {
                byinvestigationId({ ids: [this.infoData.intentionListId] }).then(
                    (res) => {
                        if (res.code == 200) {
                            this.investigationInfo = res.result;
                        }
                    }
                );
            }
            // 信贷员列表
            let loanOffres = await loanOfficerList({
                tenantId: this.infoData.bankId,
                pageNum: 1,
                pageSize: 200,
            });
            if (loanOffres.code == "200") {
                const result = loanOffres.result.list;
                this.showshowLoanList = result;
            }
        },

        //关闭弹框
        close() {
            this.selectAnimalFormData.open = false;
        },
        //刷新页面
        /**
         * livestockList:选中的每一行
         * satisticsList_s, 提交返回的
         * satisticsList  我统计分组后的
         *
         */
        refresh(livestockList, satisticsList_s, satisticsList) {
            this.form.livestockList = livestockList || [];
            this.form.satisticsList = satisticsList_s || [];
            satisticsList.forEach((key) => {
                this.satisticsList.forEach((key1) => {
                    if (key.pastureName == key1.pastureName) {
                        key.pastureAddress = key1.pastureAddress;
                        key.busInfoList.forEach((item) => {
                            key1.busInfoList.forEach((item1) => {
                                if (
                                    item.typeId == item1.typeId &&
                                    item.categoryId == item1.categoryId &&
                                    item.varietiesId == item1.varietiesId
                                ) {
                                    item.insuranceAmount = item1.insuranceAmount;
                                    item.insuranceTotalAmount =
                                        item.mortgageCount * parseFloat(item.insuranceAmount);
                                }
                            });
                        });
                    }
                });
            });
            this.satisticsList = satisticsList || []; //这个是要渲染的
        },

        selectAnimalShow() {
            this.$modal.loading("请稍后...");
            let status = "1";
            if (this.infoData.operation == "edit") {
                status = "";
            }
            checkActivateLivestock({
                userId: this.infoData.subjectUserId || this.infoData.applyId,
                status: status,
                checkActive: 1,
            })
                .then((res) => {
                    this.$modal.closeLoading();
                    if (res.code == 200) {
                        const result = res.result.list || [];
                        if (result.length == 0) {
                            this.$confirm(
                                "【" +
                                this.infoData.applyName +
                                "】的养殖场没有绑定活畜，请先绑定活畜",
                                "提示",
                                {
                                    showCancelButton: false,
                                    confirmButtonText: "好的",
                                    type: "warning",
                                }
                            ).then(() => { });
                            return;
                        }
                        this.selectAnimalFormData.livestockList = this.removeDuplication(
                            this.form.livestockList
                        );
                        this.selectAnimalFormData.open = true;
                        this.selectAnimalFormData.applyName = this.infoData.applyName;
                        this.selectAnimalFormData.applyPhone =
                            this.infoData.subjectPhone || this.infoData.applyPhone;
                        this.selectAnimalFormData.subjectUserId =
                            this.infoData.subjectUserId || this.infoData.applyId;
                        this.selectAnimalFormData.title = "选择活畜";
                        this.selectAnimalFormData.operation = this.infoData.operation;
                    } else {
                        this.$message.error(res.message);
                    }
                })
                .catch((err) => {
                    this.$modal.closeLoading();
                });
        },
        mergeGroup(arr) {
            var map = {},
                dest = [];
            for (var i = 0; i < arr.length; i++) {
                var ai = arr[i];
                if (!map[ai.pastureId]) {
                    dest.push({
                        groupPastureId: ai.pastureId,
                        pastureName: ai.pastureName,
                        pastureAddress: ai.pastureAddress,
                        busInfoList: [ai],
                    });
                    map[ai.pastureId] = ai;
                } else {
                    for (var j = 0; j < dest.length; j++) {
                        var dj = dest[j];
                        if (dj.groupPastureId == ai.pastureId) {
                            dj.busInfoList.push(ai);
                            break;
                        }
                    }
                }
            }
            return dest;
        },

        //去重
        removeDuplication(list) {
            let obj = {};
            let peon = list.reduce((cur, next) => {
                obj[next.livestockId]
                    ? ""
                    : (obj[next.livestockId] = true && cur.push(next));

                return cur;
            }, []);
            return peon;
        },

        /** 查询列表 获取监管单关联的活畜列表 */
        getSuperviseAimialList(superviseId) {
            livestockNoTagAndAllList({ superviseId, pageNum: 1, pageSize: 9999 }).then(
                (res) => {
                    if (res.code == 200) {
                        const livestockList = res.result.tagLivestockList || [];

                        this.satisticsListCopy.forEach((item) => {
                            item.superviseTotal = item.mortgageCount;
                            item.livestockTotal = item.livestockCount;
                            item.insuranceAmount = parseFloat(item.insuranceAmount).toFixed(
                                2
                            );
                        });
                        const mergeGroup = this.mergeGroup(this.satisticsListCopy);

                        this.refresh(livestockList, this.satisticsListCopy, mergeGroup);
                    } else {
                        this.$message.error(res.message);
                    }
                }
            );
        },
    },
};
</script>
<style  scoped lang="scss">
.smidfieym {
    :deep(.el-input__inner) {
        border: none;
    }
}
</style>

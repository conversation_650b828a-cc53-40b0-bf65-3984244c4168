<template>
  <div>
    <el-dialog
      :title="dialogAdd.title"
      :visible.sync="dialogAdd.open"
      width="1050px"
      :close-on-click-modal="false"
      @close="close"
      class="fieldList"
    >
      <el-tabs v-model="activeName" type="card" @tab-click="handleTab" :stretch="true">
        <el-tab-pane v-for="(item, index) in tab" :label="item" :name="String(index)" :key="index"></el-tab-pane>
      </el-tabs>
      <!-- 基本信息 -->
      <model-first v-show="activeName==0" @setSecond="setSecond" ref="first"></model-first>
      <model-second v-show="activeName==1" @setThree="setSecond" ref="second"></model-second>
      <model-three v-show="activeName==2" @setFour="setSecond" ref="three"></model-three>
      <model-four v-show="activeName==3" @submit="submit" ref="four"></model-four>
    </el-dialog>
  </div>
</template>

<script>
import { selectOne } from "@/api/ffs/survey/index.js";
import { areaData } from "@/utils/mixin/area.js";
import modelFirst from "./modelFirst.vue";
import modelSecond from "./modelSecond.vue";
import modelThree from "./modelThree.vue";
import modelFour from "./modelFour.vue";
import { directAdd } from "@/api/ffs/survey/index.js";
export default {
  components: {
    modelFirst,
    modelSecond,
    modelThree,
    modelFour,
  },
  mixins: [areaData],
  props: {
    dialogAdd: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      activeName: undefined,
      tab: ["基本信息", "活畜信息", "贷款信息", "监管信息"],
      surveyObj: {},
    };
  },
  created() {
    this.getData();
  },

  methods: {
    handleTab(tab) {
      this.activeName = tab.name;
    },

    getData() {
      if (this.dialogAdd.id == "") {
        return;
      } else {
        selectOne({ ids: [this.diaAdd.id] }).then((res) => {
          if (res.code == 200) {
            if (!res.result.basic) {
              res.result.basic = {};
            }
            if (!res.result.info) {
              res.result.info = {};
            }
            if (!res.result.loan) {
              res.result.loan = {};
            }
            if (!res.result.supervise) {
              res.result.supervise = {};
            }
            this.surveyObj = res.result;
            this.activeName = "0";
          }
        });
      }
    },
    setSecond(value) {
      this.activeName = value;
    },
    close() {
      this.$emit("close");
    },
    handleData(obj) {
      if (obj.areaName.length > 0 && obj.areaID.length > 0) {
        obj.pastureProvinceName = obj.areaName[0];
        obj.pastureProvinceId = obj.areaID[0];
        obj.pastureCityName = obj.areaName[1];
        obj.pastureCityId = obj.areaID[1];;
        if (obj.areaID[2]) {
          obj.pastureCountyName = obj.areaName[2];
          obj.pastureCountyId =  obj.areaID[2];
        }
      }
    },
    submit() {
      if (!this.$refs.first.verification()) {
        this.activeName = "0";
        return;
      }
      if (!this.$refs.second.verification()) {
        this.activeName = "1";
        return;
      }
      if (!this.$refs.three.verification()) {
        this.activeName = "2";
        return;
      }
      let obj = {
        investigationType: 1,
        investigationWay: 1,
        status: 4,
        basic: this.$refs.first.form,
        info: this.$refs.second.form,
        loan: this.$refs.three.form,
        supervise: this.$refs.four.form,
      };
      
      this.handleData(obj.info);
      directAdd(obj).then((res) => {
        if (res.code == 200) {
          this.$message({
            type: "success",
            message: "提交成功",
          });
          this.$emit('refreshList')
          this.close();
        }
      });
    },
  },
};
</script>

<style lang="scss">
.fieldList {
  .el-dialog__header {
    background-color: #f4f4f4;
  }
  .el-dialog__footer {
    text-align: center;
  }
  .selectWidth {
    width: 100%;
  }
  &-box {
    &-title {
      font-weight: 700;
    }
    &-row {
      padding: 10px;
    }
  }
}
</style>
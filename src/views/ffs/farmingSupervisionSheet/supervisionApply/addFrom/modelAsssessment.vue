<template>
  <div>
    <el-dialog
      :title="dialogAsssessment.title"
      :visible.sync="dialogAsssessment.open"
      width="600px"
      :close-on-click-modal="false"
      @close="refuse"
      class="enterprise"
      center
    >
      <el-form
        :model="form"
        :rules="rules"
        ref="ruleForm"
        label-width="120px"
        class="demo-ruleForm"
      >
        <el-form-item label="监管建议" prop="approve">
          <el-radio v-model="form.approve" label="1" @change="radioChange">同意监管</el-radio>
          <el-radio v-model="form.approve" label="2" @change="radioChange">拒绝监管</el-radio>
        </el-form-item>

        <el-form-item label="监管方案文件" prop="rejectRegulatoryScheme" v-if="form.approve==2">
          <file-upload
            :fileType="['word', 'pdf', 'png', 'jpg']"
            :limit="1"
            v-model="form.rejectRegulatoryScheme"
          ></file-upload>
        </el-form-item>

        <el-form-item label="拒绝原因" prop="approveContent" v-if="form.approve==2">
          <el-input
            v-model="form.approveContent"
            type="textarea"
            rows="3"
            maxlength="200"
            placeholder="请输入拒绝原因，200字以内"
          />
        </el-form-item>

        <el-form-item label="放款银行" prop="bankName">
          <el-select
            :disabled="lendingBankList.length==0?true:false"
            class="inputWidth"
            v-model="form.bankName"
            placeholder="请选择放款银行"
            clearable
            @change="bankChange"
          >
            <el-option
              v-for="item in lendingBankList"
              :key="item.tenantId"
              :label="item.companyName"
              :value="item.tenantId"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="银行客户经理" prop="loanOfficerName">
          <el-select
            :disabled="showshowLoanList.length==0?true:false"
            class="inputWidth"
            v-model="form.loanOfficerName"
            placeholder="请选择银行客户经理"
            @change="loanOfficerChange"
          >
            <el-option
              v-for="item in showshowLoanList"
              :key="item.userId"
              :label="item.userName"
              :value="item.userId"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            rows="3"
            maxlength="200"
            placeholder="请输入备注信息，200字以内"
          />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="refuse">关闭</el-button>
        <el-button type="primary" @click="submitForm">提交</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { lendingBank, loanOfficerList } from "@/api/ffs/survey/index.js";

import {
  submitAssessnew,
  byinvestigationId,
  intentionInfo,
  submitOnlyAssessnew,
} from "@/api/ffs/supervisionSheet/supervisionApply.js";

export default {
  name: "modelAsssessment",
  props: {
    dialogAsssessment: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      form: {
        intentionListId: "",
        approve: "",
        approveContent: "",
        rejectRegulatoryScheme: "", //拒绝监管方案
        regulatoryScheme: "", //监管方案
        bankId: "",
        bankName: "",
        loanOfficerId: "",
        loanOfficerName: "",
        remark: "",
        tenantId: "",
        status: "5",
      },
      rules: {
        approve: [
          {
            required: true,
            message: "请选择监管建议",
            trigger: "blur",
          },
        ],
        rejectRegulatoryScheme: [
          {
            required: true,
            message: "请上传拒绝监管报告",
            trigger: "blur",
          },
        ],

        approveContent: [
          {
            required: true,
            message: "请填写拒绝原因",
            trigger: "blur",
          },
        ],

        bankId: [
          {
            required: true,
            message: "请上选择放款银行",
            trigger: "blur",
          },
        ],
        loanOfficerId: [
          {
            required: true,
            message: "请上选择银行客户经理",
            trigger: "blur",
          },
        ],
      },
      lendingBankList: [],
      showshowLoanList: [],
      investigationFrom: {},
    };
  },
  created() {
    this.form.intentionListId = this.dialogAsssessment.intentionListId;
    this.initData();
  },
  methods: {
    submitForm() {
      this.$refs["ruleForm"].validate(async (valid) => {
        if (!valid) {
          return;
        }
        if (this.form.approve == 2 && this.form.rejectRegulatoryScheme == "") {
          this.$message.error("请上传拒绝监管报告文件");
          return;
        }
        if (this.form.approve == 2 && this.form.approveContent == "") {
          this.$message.error("请填写拒绝原因");
          return;
        }
        if (this.form.approve == 1) {
          this.form.approveContent = "";
          this.form.rejectRegulatoryScheme = "";
        }
        this.$modal.loading("提交中，请稍候...");
        submitOnlyAssessnew(Object.assign(this.investigationFrom, this.form))
          .then((assessRes) => {
            this.$modal.closeLoading();
            if (assessRes.code == "200") {
              this.form.approveContent = "";
              this.$message({
                type: "success",
                message: "提交成功",
              });
              this.$emit("refresh");
              this.$emit("close");
            }
          })
          .catch((err) => {
            this.$modal.closeLoading();
          });
      });
    },
    radioChange() {
      this.$refs["ruleForm"].clearValidate();
    },
    refuse() {
      this.$emit("close");
    },

    async initData() {
      //调研单
      byinvestigationId({
        ids: [this.form.intentionListId],
      }).then((res) => {
        if (res.code == 200 && res.result) {
          this.investigationFrom = res.result;
        }
      });
      // 意向单

      intentionInfo({
        ids: [this.form.intentionListId],
      }).then(async (res) => {
        if (res.code == 200 && res.result) {
          const {
            bankId,
            bankName,
            loanOfficerId,
            loanOfficerName,
            loanOfficerPhone,
            tenantId,
          } = res.result || {};
          if (bankId && loanOfficerId) {
            this.form.bankId = bankId;
            this.form.bankName = bankName;
            this.form.tenantId = tenantId;
            this.form.loanOfficerId = loanOfficerId;
            this.form.loanOfficerName = loanOfficerName;
            this.form.loanOfficerPhone = loanOfficerPhone;
            return;
          }
          let bankListRes = await lendingBank({
            pageNum: 1,
            pageSize: 200,
            companyType: 3,
          });
          if (bankListRes.code == "200") {
            this.lendingBankList = bankListRes.result.list;
          }
        }
      });
    },

    async initLoanOff(tenantId) {
      let loanOffres = await loanOfficerList({
        tenantId,
        pageNum: 1,
        pageSize: 200,
      });
      if (loanOffres.code == "200") {
        this.showshowLoanList = loanOffres.result.list;
      }
      if (loanOffres.code == "200") {
        if (!loanOffres.result.list.length) {
          this.$message.error("该银行无客户经理");
          return;
        }
        this.showshowLoanList = loanOffres.result.list;
      }
    },

    // 选择银行
    bankChange(val) {
      let lendingBankList = this.lendingBankList;
      const lendingItem = lendingBankList.find((item) => {
        return val == item.tenantId;
      });
      if (!lendingItem) return;
      this.form.bankId = val;
      this.form.bankName = lendingItem.companyName;
      this.form.tenantId = val;
      this.form.loanOfficerId = "";
      this.form.loanOfficerName = "";
      this.initLoanOff(val);
    },
    //选择信贷员
    loanOfficerChange(val) {
      let showshowLoanList = this.showshowLoanList;
      const showsItem = showshowLoanList.find((item) => {
        return val == item.userId;
      });
      if (!showsItem) return;
      this.form.loanOfficerName = showsItem.userName;
      this.form.loanOfficerId = val;
    },
    close() {
      this.$emit("close");
    },
  },
};
</script>

<style lang="scss">
.fieldList {
  .el-dialog__header {
    background-color: #f4f4f4;
  }

  .el-dialog__footer {
    text-align: center;
  }

  .selectWidth {
    width: 100%;
  }
}
.upload-box {
  border: 1px dashed #e4e7ed;
  border-radius: 10px;
  padding: 30px;
}
.upload-box:hover {
  border: 1px dashed #1890ff;
}
.el-icon-upload {
  font-size: 30px;
  margin-right: 10px;
  color: #1890ff;
}
.el-icon-txt {
  display: block;
  font-size: 16px;
}
.inputWidth {
  width: 100%;
}
</style>

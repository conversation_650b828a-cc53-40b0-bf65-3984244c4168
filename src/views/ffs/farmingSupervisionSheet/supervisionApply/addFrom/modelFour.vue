<template>
  <div>
    <el-form ref="form" :model="form" class="demo-form-inline" :rules="rules">
      <div class="form-living-box">
        <div class="form-living-title">场地条件</div>
        <el-row>
          <el-col :span="12">
            <el-form-item label="监控设备" prop="equipmentMonitorSupervise" label-width="100px">
              <el-radio-group v-model="form.equipmentMonitorSupervise">
                <el-radio :label="1">有</el-radio>
                <el-radio :label="2">无</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="form.equipmentMonitorSupervise == 1">
            <el-form-item label="品牌" label-width="70px" prop="monitorBrand">
              <el-input  v-model="form.monitorBrand" placeholder="请输入监控品牌">
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="网络状况" prop="networkStatus" label-width="100px">
              <el-radio-group v-model="form.networkStatus">
                <el-radio
                  v-for="(item,index) in newWork"
                  :key="index"
                  :label="item.value"
                >{{item.text}}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否有牛劲夹栏" prop="cowNeckClip" label-width="140px">
              <el-radio-group v-model="form.cowNeckClip">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="2">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12" >
            <el-form-item label="养殖方式" prop="breedWay" label-width="100px">
                <el-checkbox-group v-model="form.breedWay">
                  <el-checkbox
                    v-for="(item,index) in loan"
                    :key="index"
                    :label="item.value"
                  >{{item.text}}</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否转场" prop="cutTo" label-width="140px">
              <el-radio-group v-model="form.cutTo">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="2">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>
    <div class="butn">
      <el-button type="primary" @click="submit" :loading="submitData">提交</el-button>
    </div>
  </div>
</template>

<script>
import { areaData } from "@/utils/mixin/area.js";
import { getDicts } from "@/api/system/dict/data.js";
export default {
  name: "",
  mixins: [areaData],
  data() {
    return {
     submitData:false,
     loan: [
        { text: "夏季散养", value: "1" },
        { text: "冬季圈养", value: "2" },
        { text: "全年圈养", value: "3" },
      ],
      newWork:[
         { text: "网络良好", value: 1},
        { text: "网络差", value: 2 },
        { text: "无网络", value: 3 },
      ],
      form: {
        equipmentMonitorSupervise: 1,
        monitorBrand: "",
        networkStatus: undefined,
        cowNeckClip: undefined,
        breedWay: [],
        cutTo: undefined,
      },
      rules: {
        monitorBrand: [
          {
            required: true,
            message: "请填写监控品牌",
            trigger: "blur",
          },
        ],
        networkStatus: [
          {
            required: true,
            message: "请选择网络状况",
            trigger: "blur",
          },
        ],
        cowNeckClip: [
          {
            required: true,
            message: "请选择",
            trigger: "blur",
          },
        ],
        breedWay: [
          {
            required: true,
            message: "请选择",
            trigger: "blur",
          },
        ],
        cutTo: [
          {
            required: true,
            message: "请选择",
            trigger: "blur",
          },
        ],
      },
    };
  },
    created(){
        this.form=Object(this.form,this.supervise)
    },
  methods: {
    submit() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
            this.$emit("submit")
            this.submitData=true
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.form-living {
  font-size: 16px;
  font-weight: 700;
  &-box {
    padding: 10px 10px;
  }
  &-title {
    margin-bottom: 22px;
    font-size: 14px;
    font-weight: 700;
  }
  &-subtitle {
    font-weight: 400;
    padding-left: 33px;
  }
}
.butn {
  text-align: center;
}
</style>
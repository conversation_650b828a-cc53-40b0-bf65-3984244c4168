<template>
  <div>
    <el-dialog
      title="调研详情"
      :visible.sync="dialog.open"
      width="70%"
      :close-on-click-modal="false"
      @close="close"
      class="fieldList"
      append-to-body
    >
      <el-tabs v-model="activeName" type="card" @tab-click="handleTab" :stretch="true">
        <el-tab-pane v-for="(item, index) in tab" :label="item" :name="String(index)" :key="index"></el-tab-pane>
      </el-tabs>
      <modelFrist :surveyObj="surveyObj" v-if="activeName==0&&surveyObj.investigationType==1"></modelFrist>
      <modelSecond :surveyObj="surveyObj" v-if="activeName==1&&surveyObj.investigationType==1"></modelSecond>
      <warehouseFrist :surveyObj="surveyObj" v-if="activeName==0&&surveyObj.investigationType==2"></warehouseFrist>
      <warehouseSecond :surveyObj="surveyObj" v-if="activeName==1&&surveyObj.investigationType==2"></warehouseSecond>
    </el-dialog>
  </div>
</template>

<script>
import { ByIntentionInfo } from "@/api/ffs/supervisionSheet/supervisionApply.js";
import modelFrist from "./modelFrist.vue";
import modelSecond from "./modelSecond.vue";
import warehouseFrist from "./warehouseFrist.vue";
import warehouseSecond from "./warehouseSecond.vue";

export default {
  components: {
    modelFrist,
    modelSecond,
    warehouseFrist,
    warehouseSecond,
  },
  props: {
    dialog: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      activeName: undefined,
      tab: ["基本信息", "调研详情"],
      surveyObj: {},
    };
  },
  created() {
    this.getData();
  },

  methods: {
    handleTab(tab) {
      this.activeName = tab.name;
    },
    getData() {
      ByIntentionInfo({ ids: [this.dialog.id] }).then((res) => {
        if (res.code == 200 && res.result) {
          if (!res.result.loan) {
            res.result.loan = {};
          }
          if (!res.result.supervise) {
            res.result.supervise = {};
          }
          if (!res.result.info) {
            res.result.info = {};
          }
          if (!res.result.basic) {
            res.result.basic = {};
          }
          this.surveyObj = res.result;
        }
        this.activeName = "0";
      });
    },

    close() {
      this.$emit("close");
    },
  },
};
</script>

<style lang="scss">
.fieldList {
  .el-dialog__header {
    background-color: #f4f4f4;
  }
  .el-dialog__footer {
    text-align: center;
  }
  .selectWidth {
    width: 100%;
  }
  &-box {
    &-title {
      font-weight: 700;
    }
    &-row {
      padding: 10px;
    }
  }
}
</style>
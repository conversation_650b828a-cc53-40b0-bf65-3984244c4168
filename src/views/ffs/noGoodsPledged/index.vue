<template>
    <div class="app-container">
      <el-card class="mb10 form_box" shadow="never" ref="formBox">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="140px">
          <el-row class="form_row">
          <el-col class="form_col">
              <el-form-item label="区域查询：" prop="areaDataRange" v-show="regionDom">
                  <RegCascaderTag v-model="queryParams.areaDataRange" :multiple="true"></RegCascaderTag>
              </el-form-item>
          <el-form-item label="被监管方：" prop="applyName">
            <el-input v-model="queryParams.applyName" placeholder="请输入被监管方名称" clearable @keyup.enter.native="handleQuery"/>
          </el-form-item>

          <el-form-item label="被监管方联系电话：" prop="applyPhone">
            <el-input
              v-model="queryParams.applyPhone"
              placeholder="请输入被监管方联系电话"
              oninput="value=value.replace(/\D/g,'').slice(0,11)"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>

          <el-form-item label="监管状态：" prop="superviseStatus">
            <el-select v-model="queryParams.superviseStatus" clearable>
              <el-option label="全部" value />
              <el-option label="待监管" value="1" />
              <el-option label="监管中" value="2" />
              <el-option label="已结束" value="3" />
            </el-select>
          </el-form-item>
          <el-form-item label="监管期限：" prop="supervisePeriodList">
          <el-select v-model="supervisePeriodList" multiple clearable>
            <el-option label="近七天到期" value="1" />
            <el-option label="已超出监管期限" value="2" />
          </el-select>
        </el-form-item>

            <el-form-item label="委托方：" prop="companyTenantIds">
                <el-cascader :props="props" @change="selecascader" ref="myCascader" :clearable="true" v-model="queryParams.companyTenantIds"></el-cascader>
            </el-form-item>

          <el-form-item label="合同编号：" prop="contractNo">
            <el-input v-model="queryParams.contractNo" placeholder="请输入合同编号" clearable @keyup.enter.native="handleQuery"/>
          </el-form-item>

          <el-form-item label="客户经理：" prop="loanOfficerName">
            <el-input v-model="queryParams.loanOfficerName" placeholder="请输入客户经理姓名" clearable @keyup.enter.native="handleQuery"/>
          </el-form-item>
          <el-form-item label="客户经理联系电话：" prop="loanOfficerPhone">
            <el-input
              v-model="queryParams.loanOfficerPhone"
              placeholder="请输入客户经理联系电话"
              oninput="value=value.replace(/\D/g,'').slice(0,11)"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>

          <el-form-item label="监管员：" prop="supervisorName">
            <el-input v-model="queryParams.supervisorName" placeholder="请输入监管员姓名" clearable @keyup.enter.native="handleQuery"/>
          </el-form-item>

          <el-form-item label="监管员联系电话：" prop="supervisorPhone">
            <el-input
              v-model="queryParams.supervisorPhone"
              placeholder="请输入监管员联系电话"
              oninput="value=value.replace(/\D/g,'').slice(0,11)"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="监管主体：" prop="supervisSubjectName">
              <cascaderSubject ref="childCascader" v-model="supervisSubjectName"  @getValue="getValue"></cascaderSubject>
            </el-form-item>
          <!-- <el-form-item label="监管时间：">
          <el-date-picker
            v-model="superviseTime"
            style="width: 215px"
            value-format="yyyy-MM-dd"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
        </el-form-item> -->
          <el-form-item label="创建时间：">
            <el-date-picker
              v-model="dateRange"
              style="width: 215px"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            ></el-date-picker>
          </el-form-item>
            <el-form-item label="硬件拆除情况：" prop="isSelectFileInsurance">
              <el-select v-model="queryParams.deviceFlag" clearable>
                <el-option label="是" :value="1" />
                <el-option label="否" :value="0" />
              </el-select>
            </el-form-item>
            <el-form-item label="服务费结清情况：" prop="isSelectFileInsurance">
              <el-select v-model="queryParams.superviseFeeFlag" clearable>
                <el-option label="是" :value="1" />
                <el-option label="否" :value="0" />
              </el-select>
            </el-form-item>
            <el-form-item label="监管单关闭时间：">
              <el-date-picker v-model="dateRangeClose" style="width: 215px" value-format="yyyy-MM-dd" type="daterange"
                range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
            </el-form-item>
            <el-form-item label="终止原因" prop="closeReasonQuery">
              <el-select v-model="queryParams.closeReasonQuery" placeholder="请选择终止原因" style="width: 100%;" @change="closeCheck">
                <el-option label="正常终止" value="1"></el-option>
                <el-option label="异常终止" value="2"></el-option>
                <el-option label="交接终止" value="4"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="监管单号：" prop="superviseId">
              <el-input v-model="queryParams.superviseId" placeholder="请输入监管单号" clearable
                @input="queryParams.superviseId=queryParams.superviseId.replace(/[^0-9]/g,'')"
                @keyup.enter.native="handleQuery" />
            </el-form-item>
          </el-col>
      </el-row>
          <el-row style="margin-left: 140px;">
          <el-col >
              <el-form-item >
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                <template v-if="toggleSearchDom">
                  <el-button type="text" @click="packUp" >
                  {{ toggleSearchStatus ? '收起' : '展开' }}
                  <i
                    :class="{ 'el-icon-arrow-down': !toggleSearchStatus, 'el-icon-arrow-up': toggleSearchStatus }"
                  ></i>
                </el-button>
                </template>

              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-card>
      <el-card shadow="never" class="list_table">
          <el-row class="mb8 form_btn">

        <el-col  class="form_btn_col">
            <el-date-picker ref="datePick" v-model="inspectTime" type="date" placeholder="选择日期" popper-class="date-style"
         @change="changeTime"  @blur="leaveBlur" value-format="yyyy-MM-dd" class="picker" :picker-options="dateButton" >
        </el-date-picker>
            <el-button
            style="margin-left: 300px;"
            type="primary"
            plain
            icon="el-icon-download"
            size="mini"
            @click="dayExportList"
          >日报导出</el-button>

          <el-button
            type="primary"
            plain
            icon="el-icon-download"
            size="mini"
            @click="exportList"
          >导出</el-button>
        </el-col>
        </el-row>
      <el-table v-loading="loading" :data="list" :height="tableHeight" ref="table" border :summary-method="getSummaries" show-summary >
        <el-table-column label="序号" type="index" fixed width="60"></el-table-column>
        <el-table-column
          label="监管状态"
          prop="superviseStatus"
          width="100px"
          align="center"
          fixed
        >
        <template slot-scope="scope">
          <div class="round">
              <div class="round-dot" :style="{background:handelColor(scope.row.superviseStatus)}"></div>
              <span :style="{color:handelColor(scope.row.superviseStatus)}">  {{ superviseStatusName(scope.row.superviseStatus) }}</span>
              <el-tooltip class="item" effect="dark" :content="handelText(scope.row)" placement="top" v-show="scope.row.superviseStatus==2&&scope.row.expireAndExceedFlag==1 " style="color:#FF9901">
            <i class="el-icon-warning"></i>
        </el-tooltip>
        <el-tooltip class="item" effect="dark" :content="scope.row.closeDesc" placement="top" v-show="(scope.row.closeReason==2||scope.row.closeReason==3)&& scope.row.superviseStatus==3" style="color:#FF4F34">
            <i class="el-icon-warning"></i>
        </el-tooltip>
              </div>
        </template>

        </el-table-column>
        <el-table-column label="被监管方" prop="applyName" width="120px" show-overflow-tooltip fixed></el-table-column>
        <el-table-column label="联系电话" prop="applyPhone" width="130px" align="center"></el-table-column>
        <el-table-column label="监管单号" prop="superviseId" width="180px"></el-table-column>
        <el-table-column label="合同编号" prop="contractNo" width="170px"></el-table-column>
        <el-table-column label="授信金额(万元)" prop="superviseAmount" width="140px"  align="right" sortable :sort-method="(a,b) => sortBy(a ,b , 'superviseAmount')"></el-table-column>
        <el-table-column label="已用金额(万元)"  width="120px"  align="right">
            <template slot-scope="scope">{{ (scope.row.superviseBalanceDue)}}</template>
        </el-table-column>
        <el-table-column label="可用金额(万元)" prop="superviseAmountReceive" width="140px"  align="right" sortable :sort-method="(a,b) => sortBy(a ,b , 'superviseAmountReceive')"></el-table-column>
        <el-table-column label="当前库存重量(kg)" prop="inventoryWeight" width="160px"  align="right" sortable :sort-method="(a,b) => sortBy(a ,b , 'inventoryWeight')"></el-table-column>
        <el-table-column label="累计出库重量(kg)" prop="outWarehouseWeight" width="160px"  align="right" sortable :sort-method="(a,b) => sortBy(a ,b , 'outWarehouseWeight')"></el-table-column>
        <el-table-column label="监管时间" prop="superviseEnd" width="190px" :formatter="superviseDate"></el-table-column>
        <el-table-column label="委托方" prop="bankName" width="200px" show-overflow-tooltip></el-table-column>
        <el-table-column label="客户经理" prop="loanOfficerName" width="120px" show-overflow-tooltip></el-table-column>
        <el-table-column label="客户经理电话" prop="loanOfficerPhone" width="130px" align="center"></el-table-column>
        <el-table-column label="监管员" prop="supervisorName" width="130px" show-overflow-tooltip></el-table-column>
        <el-table-column label="监管员电话" prop="supervisorPhone" width="130px"  align="center"></el-table-column>
        <el-table-column label="监管主体" prop="supervisSubjectName" align="center" width="150px"></el-table-column>
        <el-table-column label="创建时间" prop="createTime" width="160px"  align="center" sortable :sort-method="(a,b) => sortBy(a ,b , 'createTime')"></el-table-column>
        <el-table-column label="操作" align="left" fixed="right" width="260px">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              @click="handDetails(scope.row.superviseId,scope.row)"
              icon="el-icon-info"
            >详情</el-button>
            <el-button size="mini" type="text" v-show="scope.row.superviseStatus==3" icon="el-icon-eleme"
                       :disabled="(scope.row.deviceFlag==1)"
                       @click="deviceRemove(scope.row)" v-hasPermi="['ffs:supervision:noGood:cleanDevice']">硬件拆除</el-button>
            <el-button size="mini" type="text" v-show="scope.row.superviseStatus==3" icon="el-icon-eleme"
                       :disabled="(scope.row.superviseFeeFlag==1)"
                       @click="serviceFee(scope.row)" v-hasPermi="['ffs:supervision:noGood:cleanSuperviseFee']">服务费结算</el-button>

            <el-tooltip class="item" effect="dark" content="修改监管员" placement="top-start">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-coordinate"
                class="btn_color_t"
                @click="supervisorEdit(scope.row)"
                v-if="scope.row.superviseStatus == 2"
              >监管员</el-button>
            </el-tooltip>
            <el-tooltip class="item" effect="dark" content="修改紧急联系人" placement="top-start">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-s-custom"
                @click="contactsEdit(scope.row)"
                class="btn_color_four"
                v-if="scope.row.superviseStatus == 2"
              >联系人</el-button>
            </el-tooltip>
          <el-popover
          style="margin-left: 10px;"
            placement="left"
            width="50"
            trigger="click"
            popper-class="my-popover"
            v-show="haveMenu(['ffs:supervision:stop','ffs:noGood:supervision:uplodeFile'],[2],scope.row.superviseStatus)"
          >
            <el-row :gutter="24">
              <el-col :span="9">
                <el-tooltip class="item" effect="dark" content="关闭" placement="top-start">
                  <el-button
                    v-hasPermi="['ffs:supervision:stop']"
                    v-show="scope.row.superviseStatus==2"
                    icon="el-icon-circle-close"
                    size="mini"
                    type="text"
                    @click="settleFeed(scope.row)"
                  >关闭</el-button>
                </el-tooltip>
              </el-col>
            <el-col :span="15">
                <el-tooltip class="item" effect="dark" content="上传附件" placement="top-start">
                  <el-button
                    v-hasPermi="['ffs:noGood:supervision:uplodeFile']"
                    v-show="scope.row.superviseStatus==2"
                    icon="el-icon-circle-close"
                    size="mini"
                    type="text"
                    @click="uplodeFile(scope.row)"
                  >上传附件</el-button>
                </el-tooltip>
            </el-col>
            <el-col :span="15">
              <el-tooltip class="item" effect="dark" content="视频" placement="top-start">
                  <el-button
                  icon="el-icon-coordinate"
                  size="mini"
                  type="text"
                  @click="supervisorVideo(scope.row)"
                >视频</el-button>
              </el-tooltip>
            </el-col>
            </el-row>

          <div slot="reference" class="extend-btn btn_color_three">
              <!-- <el-button type="text">更多</el-button> -->
              <i class="el-icon-more"></i>
            </div>
        </el-popover>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
      </el-card>
      <model v-if="detailsFormData.open" :detailsFormData="detailsFormData" @close="close"></model>
      <updata-supervisor
      :supervisorData="supervisorData"
      @close="close"
      v-if="supervisorData.open"
      @refresh="refresh"
    />

    <updata-contacts
      :contactsData="contactsData"
      @close="close"
      v-if="contactsData.open"
      @refresh="refresh"
    />
          <!-- 监管单终止 -->
          <el-dialog
            class="delete"
            title="提示"
            :visible.sync="dialogVisible"
            width="450px"
            center="center"
            @close="handleClose">
            <el-form :model="ruleForm" ref="ruleForm" label-width="140px" :rules="rules" >
                <el-form-item label="终止原因" prop="closeReason">
                    <el-select v-model="ruleForm.closeReason" placeholder="请选择终止原因" style="width: 100%;" @change="closeCheck">
                        <el-option label="正常终止" value="1"></el-option>
                        <el-option label="异常终止" value="2"></el-option>
                        <el-option label="交接终止" value="4"></el-option>
                    </el-select>
                </el-form-item>
              <el-form-item label="硬件是否已拆除" prop="deviceFlag">
                <el-radio-group v-model="ruleForm.deviceFlag">
                  <el-radio :label="1">是</el-radio>
                  <el-radio :label="0">否</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="服务费是否已结清" prop="superviseFeeFlag">
                <el-radio-group v-model="ruleForm.superviseFeeFlag">
                  <el-radio :label="1">是</el-radio>
                  <el-radio :label="0">否</el-radio>
                </el-radio-group>
              </el-form-item>
                <el-form-item label="备注" prop="closeDesc">
                    <el-input type="textarea" v-model="ruleForm.closeDesc" placeholder="请输入备注"   maxlength="200" show-word-limit></el-input>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="submitForm">确 定</el-button>
            </span>
        </el-dialog>
        <uplodeFile ref="uplodeFile" @refresh="refresh"></uplodeFile>
         <!-- 视频查看 -->
         <videoModel  ref="videoModel"></videoModel>
    </div>
  </template>

  <script>
  import {
    superviseList,
    superviseInfo,
    exportZjlxSuperviseList,
    superviseClose, superviseEdit
  } from '@/api/ffs/supervisionSheet/livingSupervisionApi.js'
  import {
    superviseStatus,
  } from "@/views/ffs/supervisionSheet/livingSupervision/utils/formatVal.js";
  import {exportExcel,getDateTime} from "@/utils/east"
  import { tableUi } from "@/utils/mixin/tableUi.js";
  import  model from './components/model.vue'
  import updataSupervisor from "@/views/ffs/supervisionSheet/livingSupervision/components/updataSupervisor.vue";
import updataContacts from "@/views/ffs/supervisionSheet/livingSupervision/components/updataContacts.vue";
import uplodeFile from '@/views/ffs/fundFlowSupervision/components/uplodeFile'
import { checkPermi } from "@/utils/permission.js";
import {dailyReports} from "@/api/mes/viewingData/index.js"
import videoModel from './components/videoModel.vue'
import cascaderSubject from '@/components/CascaderSubject/index.vue'

import {
    ffsClient
  } from "@/utils/mixin/ffsClient.js";
  export default {
    mixins: [tableUi,ffsClient],
    components:{
        model,
        updataSupervisor,
        updataContacts,
        uplodeFile,
        videoModel,
        cascaderSubject
    },
    data() {
        var that=this
      return {
        dateButton: {
            disabledDate(time) {
            return time.getTime() > Date.now();
          },
      	time:'',
        shortcuts: [
          {
            text: "取消",
            onClick() {
                that.$refs.datePick.handleClose();
            },
          },
          {
            text: "确定",
            onClick() {
              that.selectDate()
              //通过$ref设置 点击确定时，关闭日期选择的弹窗
            },
          },
        ],
      },
        dialogVisible:false,
        contactsData: {
          open: false,
          editInfo: {},
        },
        inspectTime:'',
        supervisorData: {
          open: false,
          editInfo: {},
        },
        detailsFormData: {
          open: false,
          title: "",
          superviseId: "",
        },
        // 遮罩层
        loading: true,
        // 总条数
        total: 0,
        // 表格数据
        list: [],
        contractNoList:[],
        // 查询参数
        queryParams: {
          superviseId:'',
          closeReasonQuery:'',
          pageNum: 1,
          pageSize: 10,
          queryForm: "",
          applyPhone: "",
          superviseStatus: "",
          bankName: "",
          contractNo: "",
          loanOfficerName: "",
          loanOfficerPhone: "",
          supervisorName: "",
          supervisorPhone: "",
          pastorName: "",
          superviseType: "4", //1活体，2仓单   3资金流向4 无货质押
          areaDataRange:'',
          superviseStart: '',
          superviseEnd: '',
          supervisePeriod: '',
          deviceFlag: '',
          superviseFeeFlag: '',
          companyTenantIds:'',
          supervisSubjectTypt: '',
          supervisSubjectTyptId: '',
        },
        supervisSubjectName: "",
        superviseTime: [],
      supervisePeriodList:[],
        ruleForm:{
            closeReason:'',
            closeDesc:'',
            deviceFlag: null,
            superviseFeeFlag: null
        },
        dateRange: "",
        dateRangeClose:"",
        rules:{
        closeReason: [
            { required: true, message: '请选择终止原因', trigger: ['change','blur'] }
          ],
          closeDesc: [
            { required: false, message: '请输入备注', trigger: ['change','blur'] }
          ],
          deviceFlag:[{
            required: true,
            message: '请选择硬件是否已拆除',
            trigger: ['change', 'blur']
          }],
          superviseFeeFlag: [{
            required: true,
            message: '请选择服务费是否已结清',
            trigger: ['change', 'blur']
          }]
      },
      totalObj:{
        sxAmount:'',
        sxAmountCheckY:'',
      }
      };
    },
    async created() {
      if(this.$route.query?.sourceType==1) {
        this.queryParams.deviceFlag = 0
        this.queryParams.superviseStatus = '3'
      } else if(this.$route.query?.sourceType==2) {
        this.queryParams.superviseStatus = '3'
        this.queryParams.superviseFeeFlag = 0
      }
      this.getList();
    },
    computed: {
        //处理状态颜色
          handelColor(){
          return (value)=>{
              if(value==1){
                  return '#5672FA'
              }if(value==2){
                  return '#12AE63'
              }if(value==3){
                  return '#9DA1A8'
              }
          }
      },
      superviseStatusName() {
        return (val) => {
          return superviseStatus(val);
        };
      },

      superviseDate() {
        return (row, com, val) => {
          if (!row.superviseStart) return "-";
          return row.superviseStart + "至" + row.superviseEnd;
        };
      },
      handelText(){
        return (row)=>{
            let text=''
            if(row.expireFlag==1&&row.exceedFlag!=1){
                text=`请注意，${row.expireDay}天后到期！`
            }
            if(row.exceedFlag==1&&row.expireFlag!=1){
                text=`请注意，已超出监管时间${row.exceedDay}天！`
            }
            return text
        }
    },
    haveMenu() {
      return (permissionArr, statusArr, superviseStatus) => {
        const permission = checkPermi(permissionArr);
        if (!permission) {
          return permission;
        }
        return statusArr.find((item) => item == superviseStatus);
      };
    },
  },
    methods: {
      getSummaries(param) {
        const {
          columns,
          data
        } = param;
        console.log(param)
        const sums = [];
        columns.forEach((column, index) => {
          if (index === 0) {
            sums[index] = '合计';
            return;
          }
          // this.totalObj= {sxAmount,sxAmountCheckY,fwfAmount,fwfAmountCheckY,hxCountNumber,hxAmount,hxAmountCheckY}
          if (index === 6) {

            sums[index] = '￥' + this.totalObj.sxAmount + (this.totalObj.sxAmountCheckY==2?'亿':'万')+'元';
            return;
          }



        });

        return sums;
      },
      supervisorVideo(row){
        this.$refs.videoModel.openModel=true
        this.$refs.videoModel.getUrlList(row.tenantId)
      },
      editSuperviseFile(params){
        console.log(params)
        superviseEdit(params).then(res => {
          console.log(res)
          if(res.code ==200) {
            this.$message({
              type: 'success',
              message: '操作成功!'
            });
            this.getList();
          }
        })
      },
      //  硬件拆除
      deviceRemove(row){
        console.log(row)
        this.$confirm('请确认所有硬件均已拆除完毕', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let params = {
            deviceFlag: 1,
            superviseId: row.superviseId
          }
          console.log(params)
          this.editSuperviseFile(params)
        }).catch(() => {});
      },
      // 服务费结算
      serviceFee(row) {
        console.log(row)
        this.$confirm('请确认服务费已结清', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let params = {
            superviseFeeFlag: 1,
            superviseId: row.superviseId
          }
          this.editSuperviseFile(params)
        }).catch(() => {});
      },
        dayExportList(){
            this.$refs.datePick.pickerVisible =true;
        },
        changeTime(){},
        leaveBlur(){
            if(!this.$refs.datePick.pickerVisible){
            this.rowIndex=null
        }
        },
        selectDate(){
            if(!this.inspectTime){
                this.$message({
                    type: "error",
                    message: "请选择日期",
                    });
                return
            }
            this.$modal.loading("正在下载数据，请稍候...");
          dailyReports({startTime:this.inspectTime,endTime:this.inspectTime, contractNoList:this.contractNoList}).then(res => {
            this.$modal.closeLoading();
            const blob = new Blob([res], { type: "application/vnd.ms-excel" });
            const link = document.createElement("a");
            link.href = URL.createObjectURL(blob);
            link.download = '无货质押监管' +getDateTime();
            document.body.appendChild(link);
            link.click();
            window.setTimeout(function () {
                URL.revokeObjectURL(blob);
                document.body.removeChild(link);
            }, 1000);

        })

        this.$refs.datePick.handleClose();

    },

    changeTime(){
        this.$refs.datePick.pickerVisible =true;
    },
            //上传附件
    uplodeFile(row){
        this.$refs.uplodeFile.dialogVisible=true
        this.$refs.uplodeFile.superviseId=row.superviseId
        this.$refs.uplodeFile.photoUpload=row.photoUpload
    },
        sortBy(a,b,key){
        let at=a[key]
        let bt=b[key]
        if(key=='createTime'){
            return  at-bt
        }else{
            return parseFloat(at) - parseFloat(bt)
        }
    },
    closeCheck(){
        if(this.ruleForm.closeReason!=1){
            this.rules.closeDesc=[   { required: true, message: '请输入备注', trigger: ['change','blur'] }]
        }else{
            this.rules.closeDesc=[   { required: false, message: '请输入备注', trigger: ['change','blur'] }]
        }
    },
    handleClose() {
        this.$refs['ruleForm'].resetFields();
        this.dialogVisible=false
    },

    submitForm() {
        this.$refs['ruleForm'].validate((valid) => {
          if (valid) {
            superviseClose(this.ruleForm).then(res=>{
                if(res.code==200){
                    this.refresh();
                    this.$message.success('操作成功')
                    this.handleClose()
                }
            })
          }
        });
      },

      //监管单的关闭
    settleFeed(row) {
      this.dialogVisible=true
      this.closeCheck()
      this.ruleForm.superviseId=row.superviseId

    },
          //导出
      exportList(){
          let obj=this.queryParams
          const filename = "资金流向信息";
          exportExcel(exportZjlxSuperviseList,obj,filename)
      },
      //刷新页面
      refresh() {
        this.detailsFormData.open = false;
        this.getList();
      },

      /** 查询列表 */
      getList() {
        this.loading = true;
        superviseList(this.queryParams).then((res) => {
          this.loading = false;
          if (res.code == 200) {
            this.list = res.result.list || [];
            this.total = Number(res.result.total || 0);
            this.contractNoList=res.result.params.total.contractNoList
            const {sxAmount,sxAmountCheckY} =res.result.params.total
            this.totalObj= {sxAmount,sxAmountCheckY}
            setTimeout(()=>{
              this.$refs.table.doLayout()
            },100)
          } else {
            this.$message.error(res.message);
          }
        });
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.pageNum = 1;
        if (this.dateRange) {
          this.queryParams.createStartTime = this.dateRange[0] + " 00:00:00";
          this.queryParams.createEndTime = this.dateRange[1] + " 23:59:59";
        } else {
          this.queryParams.createStartTime = "";
          this.queryParams.createEndTime = "";
        }
        if (this.dateRangeClose) {
          this.queryParams.closeStartDate   = this.dateRangeClose[0]+ " 00:00:00";
          this.queryParams.closeEndDate  = this.dateRangeClose[1] + " 23:59:59";
        } else {
          this.queryParams.closeStartDate = "";
          this.queryParams.closeEndDate  = "";
        }

        if(this.superviseTime && this.superviseTime.length != 0) {
        this.queryParams.superviseStart = this.superviseTime[0] + " 00:00:00";
        this.queryParams.superviseEnd = this.superviseTime[1] + " 23:59:59";
      } else {
        this.queryParams.superviseStart = ''
        this.queryParams.superviseEnd = ''
      }

      if(this.supervisePeriodList?.length != 0) {
        this.queryParams.supervisePeriod = this.supervisePeriodList.join(',')
      } else {
        this.queryParams.supervisePeriod = ''
      }

        this.getList();
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.$refs.childCascader.clearValue()
        this.queryParams.supervisSubjectTypt = ''
        this.supervisSubjectName = '';
        this.queryParams.supervisSubjectTyptId = ''
        this.resetForm("queryForm");
        this.queryParams.startTime = "";
        this.queryParams.endTime = "";
        this.queryParams.deviceFlag=''
        this.queryParams.superviseFeeFlag = ''
        this.queryParams.superviseStatus = ''
        this.dateRange = "";
         this.dateRangeClose=''
        this.superviseTime = []
        this.supervisePeriodList = []
        this.queryParams.companyTenantIds=[]
        this.queryParams.closeReasonQuery = ''
        this.selecascader()
        this.handleQuery();
      },
      getValue(val) {
        console.log(val)
        let superveData = JSON.parse(val)
        this.queryParams.supervisSubjectTypt = superveData.supervisSubjectTypt
        this.queryParams.supervisSubjectTyptId = superveData.supervisSubjectTyptId
        console.log(this.queryParams)
      },

      //关闭弹框
      close() {
        this.detailsFormData.open = false;
        this.supervisorData.open = false;
        this.contactsData.open = false;
      },

      //详情
      handDetails(superviseId,row) {
        superviseInfo({ ids: [superviseId] }).then((res) => {
          if (res.code == 200) {
            this.detailsFormData.info = res.result || {};
            this.detailsFormData.info.tenantId=row.tenantId
            this.detailsFormData.superviseId = superviseId;
            this.detailsFormData.info.inventoryWeight=row.inventoryWeight
            this.detailsFormData.open = true;
          }
        });
      },
      // 修改监管员
      supervisorEdit(rowData) {
        this.supervisorData.title = "修改监管员";
        const info = {
          bankId: rowData.bankId,
          superviseId: rowData.superviseId,
          loanOfficerId: rowData.loanOfficerId,
          loanOfficerName: rowData.loanOfficerName,
          loanOfficerPhone: rowData.loanOfficerPhone,
          pastorId: rowData.pastorId,
          pastorName: rowData.pastorName,
          pastorPhone: rowData.pastorPhone,
          supervisorId: rowData.supervisorId,
          supervisorName: rowData.supervisorName,
          supervisorPhone: rowData.supervisorPhone,
        };
        this.supervisorData.editInfo = info;
        this.supervisorData.open = true;
      },
      // 修改紧急联系人
      contactsEdit(rowData) {
        this.contactsData.title = "修改紧急联系人";
        const info = {
          superviseId: rowData.superviseId,
          subjectLinkmanName: rowData.subjectLinkmanName || "",
          subjectLinkmanPhone: rowData.subjectLinkmanPhone || "",
          bankLinkmanName: rowData.bankLinkmanName || "",
          bankLinkmanPhone: rowData.bankLinkmanPhone || "",
          superviseLinkmanName: rowData.superviseLinkmanName || "",
          superviseLinkmanPhone: rowData.superviseLinkmanPhone || "",
        };
        this.contactsData.editInfo = info;
        this.contactsData.open = true;
      },
    },
  };
  </script>
  <style scoped lang="scss">
    ::v-deep .el-table__fixed-footer-wrapper table{
      height: 40px;
    }
    ::v-deep .el-table__footer-wrapper table{
      height: 40px;
    }
  .extend-btn {
  color: #1890ff;
  display: inline-block;
  .el-icon-arrow-down {
    margin-left: -10px;
  }
}
  </style>
  <style lang="scss">
  /* 日期控件样式 */
/* input框placeholder样式 */
     /* 日期选择框面板样式 */
     .form_btn_col{
        .el-date-editor.el-input, .el-date-editor.el-input__inner{
            // display: none;
            width: 0px !important;
            overflow: hidden;
            opacity: 1;
        }
     }
     .date-style.el-date-picker, .date-style.el-picker-panel{
        width: 340px !important;
        height: 380px !important;
        border: 1px solid rgba(255, 255, 255, 0.3);
        margin: 0;
        font-size: 1rem !important;
        font-family: 'HelveticaNeue-Medium, HelveticaNeue';
        font-weight: 500;
          /* 日期选择器左侧快捷方式样式重写 定位到底部 */
        .el-picker-panel__sidebar {
            width: 320px;
            height: 134px;
            padding: 0;
            display: flex;
            justify-content: right;
            align-items: center;
            margin-top: 283px;
            background: rgba(0, 0, 0, 0) !important;
            border-right: 1px solid rgba(0, 0, 0, 0);
        }

        .el-picker-panel__sidebar :nth-child(2) {
            background-color: #3AA0B8;
            border: none;
            color: #FFFFFF;
        }

        .el-picker-panel__sidebar>button {
            width: 56px;
            height: 24px;
            line-height: 20px;
            margin-right: 16px;
            border: 1px solid #6DE3FF;
            font-size: 12px;
            font-family: 'PingFangSC-Regular, PingFang SC';
            font-weight: 400;
            // color: #FFFFFF;
            padding: 0;
            text-align: center;
            border-radius: 2px;
        }

        /* 日期选择器日历部分样式*/
        .el-picker-panel__body-wrapper {
            width: 506px;
            height: 323px;
            display: flex;
            flex-wrap: wrap;
        }

        .el-picker-panel__body {
            margin: 0 !important;
        }
     }
</style>


<template>
    <div class="drawer_box">
        <div id="print">
            <div class="fc mt20 point_icon">
                <span>入库信息</span>
            </div>
              <el-descriptions class="mt20" :column="3" size="medium" border>
                <el-descriptions-item label='入库类型：'>{{ warehouseTypeHash[dataDetail.inventoryType] }}</el-descriptions-item>
                <el-descriptions-item label='入库单号：'>{{ dataDetail.inventoryCode }}</el-descriptions-item>
                <el-descriptions-item label='入库时间：'>{{ dataDetail.createTime }}</el-descriptions-item>
                <el-descriptions-item label='入库仓库：'>{{ dataDetail.inWarehouseName }}</el-descriptions-item>
                <el-descriptions-item label='出库仓库：' v-if="dataDetail.inventoryType == 12">{{ dataDetail.outWarehouseName }}</el-descriptions-item>
                <el-descriptions-item label='创建人：'>{{ dataDetail.createUserName }}</el-descriptions-item>
                <el-descriptions-item label='单据创建时间：'>{{ dataDetail.updateTime }}</el-descriptions-item>
            </el-descriptions>
            <div class="fc mt20 point_icon">
                <span>入库明细</span>
            </div>
            <el-table class="mt20" :data="tableData" max-height="500" :show-summary="true">
                <el-table-column type="index" align="center" label="序号"></el-table-column>
                <el-table-column prop="name" align="center" label="内部产品编码">
                    <template slot-scope="scope">
                        <span>{{ scope.row.productCode }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="productName" label="产品名称"></el-table-column>
                <el-table-column prop="weightingTypeName" align="center" label="称重类型">
                    <template slot-scope="scope">{{ handeltext[scope.row.weightingType] }}</template>
                </el-table-column>
                <el-table-column prop="unitWeightName" align="center" label="规格单位"></el-table-column>
                <el-table-column prop="inventoryNum" align="right" label="入库数量" :sum-text="'合计'"></el-table-column>
                <el-table-column prop="inventoryWeight" align="right" label="入库重量（kg）" :sum-text="'合计'"></el-table-column>
            </el-table>
        </div>
    </div>
</template>
<script>
import { inventoryInfo } from "@/api/ffs/noGoodsPledged.js";;
export default {
    data() {
        return {
            tableData: [],
            tableMaterialsData: [],
            warehouseTypeHash: {
                '11': '急冻入库',
                '12': '成品入库',
                '13': '结余入库',
                '14': '盘盈入库'
            },
            currentItem: {},
            handeltext: {
                1: '定重',
                2: '抄码'
            },
            dataDetail: {}
        }
    },
    props: {
        dataInfo: Object,
        isShowInfo: String,
        tenantId:String
    },
    watch: {
        dataInfo() {
            this.getList();
        }
    }, 
    created() {
        if (this.dataInfo.inventoryId) {
            this.getList()
        }
    },
    methods: {
        getList() {
            inventoryInfo({
                inventoryId: this.dataInfo.inventoryId,
                tenantId:this.tenantId
            }).then(res => {
                if (res.code == 200) {
                    this.dataDetail = res.result;
                    this.tableData = res.result.inventoryProductList.map((item) => {
                        if (item.weightingType == 2) {
                            item.unitWeightName = item.unitWeight + '-' + item.unitWeightEnd + '/' + item.unitName;
                        } else {
                            item.unitWeightName = item.unitWeight + '/' + item.unitName;
                        }
                        item.productName = item.productTypeName == '结余产品' ? item.productTypeName + '（结余产品）' :item.productName
                        return item
                    })
                }
            })
        },
    }
}
</script>


<style lang="scss" scoped>
.el-row {
    font-size: 14px !important;
    margin-top: 20px;
    &:last-child {
        margin-bottom: 20px;
    }
    .el-col-8{
        margin-top: 20px;
    }
}
.card-title {
    margin-bottom: 15px;
    margin-top: 20px;
}
.fast {
    width: 8px;
    height: 18px;
    background: #409eff;
    margin-right: 10px;
}

.header {
    &-title {
        font-size: 20px;
    }
}
</style>
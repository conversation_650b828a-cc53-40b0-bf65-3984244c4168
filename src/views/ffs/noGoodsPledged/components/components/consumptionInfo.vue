<template>
    <div class="">
        <el-dialog title="详情" :visible.sync="visibleStatus" width="1200px" :before-close="handleClose" :append-to-body="true">
        <el-descriptions :title="info.supplierName" :column="3" size="medium" border>
            <template slot="extra">
              <!-- <el-button plain size="small" v-if="info.contractUrl" class="add_btn" @click="handleCommand('contractUrl')">查看采购合同</el-button> -->
            </template>
        </el-descriptions>
      <el-row :gutter="20" class="fcc">
          <el-col >
              <el-descriptions  :column="3" size="medium" border>
                  <el-descriptions-item label='入厂编号：'>{{ info.checkInCode }}</el-descriptions-item>
                  <el-descriptions-item label='入厂时间：'>{{ info.checkInTime }}</el-descriptions-item>
                  <el-descriptions-item label='供应商类型：'>{{ handelType(info.supplierType,supplierTypeList) }}</el-descriptions-item>
                  <el-descriptions-item label='联系人：'>{{ info.supplierContactName }}</el-descriptions-item>
                  <el-descriptions-item label='联系电话：'>{{ info.supplierContactPhone }}</el-descriptions-item>
                  <el-descriptions-item :label='info.supplierType==1 ? "企业营业执照号：" : "身份证号码："'>
                    {{ info.supplierCertNo }}</el-descriptions-item>
              </el-descriptions>
          </el-col>
      </el-row>
      <el-row>
        <el-tabs v-model="activeName">
          <el-tab-pane label="结算单" name="1" />
          <el-tab-pane label="检斤单" name="2" />
          <el-tab-pane label="补扣明细" name="3" />
          <el-tab-pane label="消费信息" name="4"/>
        </el-tabs>
      </el-row>

      <!-- 已结算 -->
      <settementModel :activeName="activeName" ref="modelEnd"></settementModel>
      </el-dialog>
    </div>
  </template>
  
  <script>
  import { suffix } from '@/utils/validate'
  import {settlementInfo } from '@/api/ffs/noGoodsPledged.js'
  import settementModel from './settementModel.vue'
  export default {
    dicts: ["supplementary_payment", "deduction_items"],
    components: {
        settementModel
    },

    data() {
      return {
        statusList: [
          { label: "取消", value: "0" },
          { label: "已结算", value: "1" },
          { label: "待结算", value: "2" },
        ],
        supplierTypeList: [
          { label: "企业", value: 1 },
          { label: "中间商", value: 2 },
          { label: "养殖户", value: 3 },
        ],

        visibleStatus: false,
        info: {},
        activeName: "1",
      };
    },

    computed: {
      handelType() {
        return (value, list) => {
          let name = "";
          list.forEach((item) => {
            if (item.value == value) {
              name = item.label;
            }
          });
          return name;
        };
      },
    },

    methods: {
     showModel(id){
        settlementInfo({settlementCode:id}).then(res=>{
            if(res.code==200){
                this.visibleStatus=true
                this.info=res.result
                this.$nextTick(()=>{
                    this.$refs.modelEnd.settlementCode=id
                    this.$refs.modelEnd.getInfo();
                    this.$refs.modelEnd.getWeigth();
                    this.$refs.modelEnd.getButcherFee();
                })
            }
        })
     },
      handleClose(){
        this.visibleStatus=false
      },
      handleCommand(command) {
        if (command === 'contractUrl') {
          if (!this.info.contractUrl) {
            this.$message({
              message: "您还未签署采购合同",
            });
          } else {
              const fileSuffix = suffix(this.info.contractUrl)
              if (fileSuffix == 'png' || fileSuffix == 'jpg' || fileSuffix == 'jpeg') {
                  this.showImg(this.info.contractUrl)
              } else {
                  window.open(this.info.contractUrl, '_blank')
              }
          }
        }
      },
    },
  };
  </script>
  
  <style lang="scss" scoped>
  .el-row {
    font-size: 14px !important;
    margin-top: 20px;
    &:last-child {
      margin-bottom: 20px;
    }
  }
  .card-title {
    margin-bottom: 15px;
  }
  .fast {
    width: 8px;
    height: 18px;
    background: #409eff;
    margin-right: 10px;
  }
  .model {
    width: 100%;
    height: 100%;
    padding: 0 30px;
    &-text {
      color: rgb(102, 102, 102);
    }
    .header {
      &-title {
        font-size: 18px;
      }
    }
  }
  </style>
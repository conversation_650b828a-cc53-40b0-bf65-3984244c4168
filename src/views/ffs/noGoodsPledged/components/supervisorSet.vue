<template>
  <div>
    <el-table v-loading="loading" :data="list" border>
      <el-table-column label="角色" prop="roleName" align="center"></el-table-column>
      <el-table-column label="姓名" prop="name" align="center"></el-table-column>
      <el-table-column label="联系方式" prop="mobile" align="center"></el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  name: "supervisorSet",
  props: {
    info: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      // 遮罩层
      loading: false,
      // 总条数
      total: 0,
      // 表格数据
      list: [],
    };
  },
  created() {
    console.log("监管员info：", this.info);
  },
  watch: {
    info: {
      immediate: true,
      handler(val) {
        if (val) {
          const list = [
            {
              roleName: "监管员",
              name: val.supervisorName,
              mobile: val.supervisorPhone,
            },
            {
              roleName: "客户经理",
              name: val.loanOfficerName,
              mobile: val.loanOfficerPhone,
            },
          ];
          if (val.pastorName) {
            list.push({
              roleName: "畜牧师",
              name: val.pastor<PERSON><PERSON>,
              mobile: val.pastorPhone,
            });
          }
          this.list = list;
        }
      },
    },
  },

  methods: {
    //关闭弹框
    close() {
      this.$emit("close");
    },
  },
};
</script>

<style lang="scss">
.fieldList {
  .el-dialog__header {
    background-color: #f4f4f4;
  }

  .el-dialog__footer {
    text-align: center;
  }

  .selectWidth {
    width: 100%;
  }
}
</style>

<template>
  <div>
    <el-row :gutter="10" >
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="100px">
        <el-form-item label="还款人：" prop="userName">
          <el-input v-model="queryParams.userName" placeholder="请输入还款人姓名" clearable @keyup.enter.native="handleQuery"/>
        </el-form-item>
        <el-form-item label="审核状态：" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择" clearable>
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="交易时间：">
          <el-date-picker
            v-model="time"
            value-format="yyyy-MM-dd"
            style="width: 220px"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-row>
    <div style="text-align: right;">
        <el-button 
        type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
        @click="newInformation"
          >新增还款</el-button>
          <el-button
            type="primary"
            icon="el-icon-search"
            plain
            size="mini"
            @click="handleExportQuot"
            v-hasPermi="['ffs:fund:supervision:exportQuo']"
            v-show="total > 0"
          >导出</el-button>
    </div>
    <el-table :data="list" stripe style="width: 100%;margin-top:10px" v-loading="loading" border>
      <el-table-column type="index" label="序号" width="60"></el-table-column>
      <el-table-column prop="repaymentCode" label="交易流水号" width="180"></el-table-column>
      <el-table-column prop="repaymentAmt"  align="right" label="还款金额(本金/元)" width="140px"></el-table-column>
      <el-table-column prop="repaymentTime" label="还款时间" align="center"></el-table-column>
      <el-table-column prop="repaymentPic" label="还款凭证" align="center">
        <template slot-scope="scope">
          <div
            style="cursor: pointer;color:#1890ff"
            v-show="scope.row.repaymentPic"
            @click="bigImg(scope.row.repaymentPic)"
          >查看凭证</div>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="上传时间" width="160" align="center"></el-table-column>
      <el-table-column prop="userName" label="还款人"  show-overflow-tooltip></el-table-column>
      <el-table-column prop="createBy" label="操作人"  show-overflow-tooltip></el-table-column>
      <el-table-column prop="statusName" label="状态" align="center"></el-table-column>
      <el-table-column prop="operation" label="操作" align="center">
        <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-s-custom"
              @click="examine(scope.row)"
              v-if="scope.row.status == 2"
            >审核</el-button>
            <span   v-if="scope.row.status!= 2">--</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <el-image-viewer
      :zIndex="3001"
      v-if="showViewer"
      :on-close="() => { showViewer = false;}"
      :url-list="imgList"
    />
    <newInfo ref="addNewInfo" :contractNo='info.contractNo' :paymentItemInfo='paymentItemInfo'></newInfo>
  </div>
</template>

<script>
import { paymentHistoryTable } from "@/api/ffs/superviseLhApi.js";
import newInfo from "@/views/nlbao/operate/newInfo"
import ElImageViewer from "element-ui/packages/image/src/image-viewer";
import { exportQuot } from "@/views/ffs/fundFlowSupervision/components/detailsForm/exportQuot.js";
export default {
  name: "paymenHistory",
  components: {
    ElImageViewer,
    newInfo
  },
  props: {
    info: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      outEnterHoseData: {
        open: false,
        id: "",
        title: "",
      },
      queryParams: {
        repaymentCode: "",
        status:'',
        userName: "",
        pageNum: 1,
        pageSize: 10,
        repaymentSatrtTime: "",
        repaymentEndTime: "",
      },

      time: undefined,

      // 遮罩层
      loading: false,
      // 总条数
      total: 0,
      // 表格数据
      list: [],
      imgList: [],
      showViewer: false,
      paymentItemInfo: {},

      // 还款状态
      options: [
        { label: '已还款', value: 1 },
        { label: '待审核', value: 2 },
        { label: '已驳回', value: 0 }
      ]
    };
  },
  created() {
    this.queryParams.quotaCode = this.info.contractNo;
    this.getList();
  },


  methods: {
    bigImg(url) {
      this.imgList = [];
      this.imgList.push(this.picPath(url))
      this.showViewer = true;
    },
    /** 查询还款记录 */
    getList() {
      paymentHistoryTable(this.queryParams).then((res) => {
        this.loading = false;
        if (res.stautscode == 200) {
          this.list = res.data.list || [];
          const statusHash = {
            1: '已还款',
            2: '待审核',
            0: '已驳回',
          }
          this.list.forEach(item => {
            item.statusName = statusHash[item.status]
          })
          this.total = Number(res.data.totalRow || 0);
        } else {
          this.$message.error(res.message);
        }
      });
    },
    handleExportQuot() {
        exportQuot(this, 2, this.info.contractNo);
    },

    /** 搜索按钮操作 */
    handleQuery() {
      if (this.time && this.time.length > 0) {
        this.queryParams.repaymentSatrtTime = this.time[0];
        this.queryParams.repaymentEndTime = this.time[1];
      } else {
        this.queryParams.repaymentSatrtTime = "";
        this.queryParams.repaymentEndTime = "";
      }
      this.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.time = [];
      this.queryParams.repaymentSatrtTime = "";
      this.queryParams.repaymentEndTime = "";
      this.resetForm("queryForm");
      this.handleQuery();
    },
     newInformation() {
      this.paymentItemInfo = {}
      this.$refs.addNewInfo.dialogFormVisible = true
    },
    //关闭弹框
    close() {
      this.outEnterHoseData.open = false;
      this.paymentItemInfo = {}
      this.$emit("close");
    },
    // 还款审核 
    examine(item) {
      this.$refs.addNewInfo.dialogFormVisible = true;
      this.paymentItemInfo = item
    }
  },
};
</script>

<style lang="scss">
.fieldList {
  .el-dialog__header {
    background-color: #f4f4f4;
  }

  .el-dialog__footer {
    text-align: center;
  }

  .selectWidth {
    width: 100%;
  }
}
</style>

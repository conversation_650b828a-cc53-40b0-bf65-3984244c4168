<template>
  <div>
    <el-dialog
      :title="superviseLogData.title"
      :visible.sync="superviseLogData.open"
      width="1250px"
      :close-on-click-modal="false"
      @close="close"
      class="fieldList"
      append-to-body
    >
      <span class="qihsd mlt20" style="float:right;overflow: hidden">状态：{{info.status==0?'正常':'异常'}}</span>
      <el-descriptions :column="4" :contentStyle="CS" :label-style="LS">
        <div slot="title">
          <div>{{info.storehouseName}} {{info.storehouseNo}}</div>
          <div class="cksdare">{{info.storehouseAddress}}</div>
        </div>

        <el-descriptions-item label="巡检员">{{info.createUserName}}</el-descriptions-item>
        <el-descriptions-item label="巡检时间">{{info.createTime}}</el-descriptions-item>

        <el-descriptions-item label="巡检员签字">
          <el-image
            class="qianmad"
            :src="picPath(info.autographImages)"
            :preview-src-list="srcList"
            @click="bigImage(picPath(info.autographImages))"
          ></el-image>
        </el-descriptions-item>
        <el-descriptions-item label="巡检说明" v-if="info.remark">{{info.remark}}</el-descriptions-item>
      </el-descriptions>

      <el-row class="filitem ">
        <el-col >
          <el-image
            style="width: 150px; height: 120px; padding: 0 5px"
            v-for="(item,index) in info.imgList"
            :key="index"
            :src="item"
            :preview-src-list="srcList"
            @click="bigImage(item)"
          ></el-image>

           <video
            style="width: 150px; height: 120px; padding: 0 5px"
            v-for="(item,index) in info.videList"
            :key="index"
            :src="item"
            controls="controls"
          ></video>
        </el-col>
      </el-row>

      <span slot="footer" class="dialog-footer">
        <el-button type="info" @click="close">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { superviseLogInfo } from "@/api/ffs/mortgage/supervision";
import { getFilePath } from "@/utils/east.js";
export default {
  name: "inspectionInfo",
  props: {
    superviseLogData: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      CS: {
        "max-width": "300px", //最小宽度
        "word-break": "break-all", //过长时自动换行
      },
      LS: {
        "word-break": "keep-all",
        color: "#000",
      },

      queryParams: { fileName: "", superviseType: "" },
      form: {
        fileName: "",
        datetime: "",
        imagesArr: [
          { url: "dev/2022/09/4a1662446333102.jpg" },
          { url: "dev/2022/09/4a1662446333102.jpg" },
        ],
        videosArr: [{ url: "dev/2022/09/ar1662446361190.mp4" }],
      },

      // 遮罩层
      loading: true,
      // 总条数
      total: 3,
      // 表格数据
      list: [
        {
          fileName: "嚯哈哈",
          orderNum: "13201860552ABCD3",
          mobile: "13201860552",
          money: "30.00",
          createTime: "2022-12-12 10:10:11",
        },
      ],
      srcList: [],
      info: {
        patrolLivestockList: [{ patrolPos: "" }],
      },
    };
  },
  created() {
    this.getDetails();
  },
  computed: {
    patrolStatusName() {
      return (val) => {
        if (val == 0) {
          return "正常";
        } else {
          return "异常";
        }
      };
    },
  },
  methods: {
    bigImage(url) {
      this.srcList = [];
      this.srcList.push(url);
    },
    /** 查询日志记录详情 */
    getDetails(id) {
      superviseLogInfo({ id: this.superviseLogData.logId }).then((res) => {
        this.loading = false;
        if (res.code == 200) {
          const dataList = res.result || {};

          const itemFile = getFilePath(dataList.detailsImages);
          const imgList = [];
          const videList = [];
          itemFile.forEach((file) => {
            const fileName = file.slice(file.lastIndexOf(".") + 1);
            if (
              fileName == "mp4" ||
              fileName == "m4v" ||
              fileName == "avi" ||
              fileName == "flv"
            ) {
              videList.push(file);
            } else {
              imgList.push(file);
            }
            dataList.videList = videList;
            dataList.imgList = imgList;
          });
          this.info = dataList;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    close() {
      this.$emit("close");
    },
  },
};
</script>

<style lang="scss">
.fieldList {
  .el-dialog__header {
    background-color: #f4f4f4;
  }

  .el-dialog__footer {
    text-align: center;
  }

  .selectWidth {
    width: 100%;
  }
}
.head-title {
  font-size: 16px;
  color: #999;
  margin-bottom: 20px;
}
.qihsd {
  color: red;
}
.qianmad {
  height: 50px;
  margin-top: -20px;
}
.mlt20 {
  margin-left: 20px;
}
.filitem {
  padding: 5px 15px;
}
.f {
  display: flex;
}
.cksdare {
  font-size: 12px;
  color: #666;
  font-weight: 500;
  margin-top: 2px;
}
</style>

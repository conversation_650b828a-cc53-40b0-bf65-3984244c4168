<template>
  <div class="app-container tabs_box">
      <el-row >
        <el-form
          :model="queryParams"
          ref="queryForm"
          size="small"
          :inline="true"
          label-width="120px"
        >
          <el-form-item label="出库单号" prop="inventoryCode">
            <el-input v-model="queryParams.inventoryCode" placeholder="请输入出库单号" clearable @keyup.enter.native="handleQuery"/>
          </el-form-item>
          <el-form-item label="出库类型" prop="inventoryType">
            <el-select v-model="queryParams.inventoryType" clearable>
              <el-option
                v-for="(item,index) in warehouseTypeList"
                :label="item.text"
                :value="item.value"
                :key="index"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="出库仓库" prop="outWarehouseId">
          <el-select v-model="queryParams.outWarehouseId" clearable>
            <el-option
              v-for="(item, index) in warehouseList"
              :label="item.warehouseName"
              :value="item.warehouseId"
              :key="index"
            />
          </el-select>
        </el-form-item>
          <el-form-item label="单据状态" prop="inventoryStatus">
            <el-select v-model="queryParams.inventoryStatus" clearable>
              <el-option
                v-for="(item,index) in inventoryStatusList"
                :label="item.text"
                :value="item.value"
                :key="index"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="出库时间" >
            <el-date-picker
              v-model="dateEnter"
              style="width: 240px"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            ></el-date-picker>
          </el-form-item>
    
            <el-form-item label=" ">
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>
      </el-row>
    <el-card class="table_box" shadow="never">
        <el-row class="mb20">
        <div style="display: flex;justify-content: end;">
          <el-button
            class="default_btn"
            icon="el-icon-download"
            size="mini"
            @click="exportList"
          >导出数据</el-button>
        </div>
      </el-row>
      <!-- 表格数据 -->
      <el-table :data="tableData" ref="myTable" style="width: 100%" v-loading="loading" border>
        <el-table-column show-overflow-tooltip align="center" type="index" label="序号" width="50"></el-table-column>
        <el-table-column show-overflow-tooltip align="center" prop="inventoryCode" label="出库单号">
          <template slot-scope="scope">{{ scope.row.inventoryCode }}</template>
        </el-table-column>
        <el-table-column show-overflow-tooltip align="center" label="出库类型">
          <template slot-scope="scope">{{ handeltext(scope.row.inventoryType) }}</template>
        </el-table-column>
        <el-table-column show-overflow-tooltip align="center" prop="warehouseName" label="出库仓库">
          <template slot-scope="scope">{{ scope.row.warehouseName }}</template>
        </el-table-column>
        <el-table-column show-overflow-tooltip align="right" prop="inventoryNum" label="出库数量">
          <template slot-scope="scope">{{ scope.row.inventoryNum }}</template>
        </el-table-column>
        <el-table-column
          show-overflow-tooltip
          align="right"
          prop="inventoryWeight"
          label="出库重量（kg）"
          width="130"
        >
          <template slot-scope="scope">{{ scope.row.inventoryWeight }}</template>
        </el-table-column>
        <el-table-column show-overflow-tooltip align="center" label="单据状态">
          <template slot-scope="scope">
            <span
              :class="{
								orange: scope.row.inventoryStatus == 2,
								blue: scope.row.inventoryStatus == 3,
								green: scope.row.inventoryStatus == 1,
							}"
            >{{ handelInventoryStatustext(scope.row.inventoryStatus) }}</span>
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip align="center" prop="createUserName" label="操作人">
          <template slot-scope="scope">{{ scope.row.createUserName }}</template>
        </el-table-column>
        <el-table-column show-overflow-tooltip align="center" prop="createTime" label="出库日期">
          <template slot-scope="scope">{{ scope.row.createTime }}</template>
        </el-table-column>
        <el-table-column show-overflow-tooltip align="center" label="操作" >
          <template slot-scope="scope">
            <el-button
              class="text_btn"
              size="mini"
              icon="el-icon-warning-outline"
              @click="stockInfo(scope.row)"
              type="text"
            >查看</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
        class="pagination-box"
      />
    </el-card>
    <el-dialog title="详情" :visible.sync="modelShow" width="1200px" :before-close="handleClose" :append-to-body="true">
        <outBoundInfo ref="model" :dataInfo="currentItem" :tenantId="queryParams.tenantId"></outBoundInfo>
    </el-dialog>
  </div>
</template>
<script>
import outBoundInfo from './components/outBoundInfo.vue'
import { inventoryPage ,inventoryExport} from "@/api/ffs/noGoodsPledged.js";
import { exportExcel } from "@/utils/east";
export default {
    components:{
        outBoundInfo
    },
    props:{
        info:{
            type:Object,
            default:{}
        },
        wareList:{
            type:Array,
            default:[]
        }
    },
  data() {
    return {
        warehouseList:[],
        modelShow:false,
        currentItem:{},
      queryParams: {
        inventoryCode: "",
        outWarehouseId: "",
        inventoryStatus: '',
        businessType: 2,
        pageNum: 1,
        pageSize: 10,
      },
      tableData: [{}],
      loading: true,
      dateEnter: [],
      total: 0,
      warehouseTypeList: [
        { text: "领料出库", value: 21 },
        { text: "销售出库", value: 22 },
        { text: "盘亏出库", value: 23 },
        { text: "急冻出库", value: 24 },
      ],
      inventoryStatusList: [
        { text: "已出库", value: 1 },
        { text: "待出库", value: 2 },
        { text: "进行中", value: 3 },
      ],
    };
  },

  computed: {
    handeltext() {
      return (value) => {
        let name = "";
        this.warehouseTypeList.forEach((item) => {
          if (item.value == value) {
            name = item.text;
          }
        });
        return name;
      };
    },
    handelInventoryStatustext() {
      return (value) => {
        let name = "";
        this.inventoryStatusList.forEach((item) => {
          if (item.value == value) {
            name = item.text;
          }
        });
        return name;
      };
    },
  },
  watch:{
    wareList:{
        handler(){
            this.warehouseList=this.wareList
        },
        deep:true,
        immediate:true
    }
  },
  created() {
    this.queryParams.tenantId=this.info.tenantId
    this.getList();
  },
  methods: {
    refresh() {
      this.getList();
    },
    //列表查询
    getList() {
        inventoryPage(this.queryParams).then((res) => {
        	if (res.code == 200) {
        		this.tableData = res.result.list;
        		this.total = Number(res.result.total);
        this.loading = false;
        	}
        });
    },
    reset() {
      this.resetForm("queryForm");
    },
    //重置
    resetQuery() {
      this.dateEnter = [];
      this.reset();
      this.handleQuery();
    },
    //刷新页面
    refreshList() {
      this.getList();
    },
    handelData(startTime, endTime, list) {
      if (list?.length > 0) {
        this.queryParams[startTime] = list[0];
        this.queryParams[endTime] = list[1];
      } else {
        delete this.queryParams[startTime];
        delete this.queryParams[endTime];
      }
    },
    //搜索
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.handelData("startTime", "endTime", this.dateEnter);
      this.getList();
    },
    stockInfo(row) {
        this.currentItem = row
        this.modelShow=true
    },
    handleClose(){
        this.modelShow=false
    },
    exportList() {
        exportExcel(inventoryExport,this.queryParams,'出库明细')
    },
  },
};
</script>
      
<style lang="scss" scoped>
.tab {
  padding: 0 20px;
  color: #333333;
  span {
    margin: 0 10px;
    cursor: pointer;
  }
}
</style>
      
<template>
  <div>
    <el-descriptions title="紧急联系人" :column="2" border>
      <el-descriptions-item label="被监管方紧急联系人">{{form.subjectLinkmanName||'无'}}</el-descriptions-item>
      <el-descriptions-item label="被监管方紧急联系人电话">{{form.subjectLinkmanPhone||'无'}}</el-descriptions-item>
      <el-descriptions-item label="委托方紧急联系人">{{form.bankLinkmanName||'无'}}</el-descriptions-item>
      <el-descriptions-item label="委托方紧急联系人电话">{{form.bankLinkmanPhone||'无'}}</el-descriptions-item>
      <el-descriptions-item label="监管方紧急联系人">{{form.superviseLinkmanName||'无'}}</el-descriptions-item>
      <el-descriptions-item label="监管方紧急联系人电话">{{form.superviseLinkmanPhone||'无'}}</el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<script>
export default {
  name: "emergencyContact",
  props: {
    info: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      form: {},
    };
  },
  watch: {
    info: {
      immediate: true,
      handler(val) {
        if (val) {
          this.form = val;
        }
      },
    },
  },
  created() {},
  methods: {},
};
</script>

<style lang="scss">
.fieldList {
  .el-dialog__header {
    background-color: #f4f4f4;
  }

  .el-dialog__footer {
    text-align: center;
  }

  .selectWidth {
    width: 100%;
  }
}
</style>

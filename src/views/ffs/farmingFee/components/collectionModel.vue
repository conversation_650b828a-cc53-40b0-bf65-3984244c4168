<template>
  <div>
    <el-dialog
      :title="dialogCollection.title"
      :visible.sync="dialogCollection.open"
      width="600px"
      :close-on-click-modal="false"
      @close="close"
      class="serviceFee"
    >
      <el-row class="row">服务费总金额(元)：{{data.superviseServiceFee}}</el-row>
      <el-row class="row"> 实收总金额(元)：{{data.receiveServiceFee}}</el-row>
      <el-row class="row"> 应收金额(元)：{{data.remainServiceFee}}</el-row>
      <el-form :model="form" ref="modelForm"  :rules="rules" label-width="110px" class="row">
        <el-form-item label="服务费金额" prop="currentSettlementFee">
          <el-input type="number" v-model="form.currentSettlementFee" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="服务费有效期" prop="dateF">
            <el-date-picker
            v-model="form.dateF"
            value-format="yyyy-MM-dd"
            type="date"
            placeholder="选择开始日期">
            </el-date-picker>
        </el-form-item>
        <el-form-item label="" prop="dataS">
            <el-date-picker
            v-model="form.dataS"
            value-format="yyyy-MM-dd"
            type="date"
            placeholder="选择截至日期">
            </el-date-picker>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="close">关闭</el-button>
        <el-button type="primary" @click="submitForm">提交</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { serviceFeeAdd ,recordList} from "@/api/ffs/farmingServiceFee";
import dayjs from "dayjs";
export default {
  props: {
    dialogCollection: {
      type: Object,
      default: {},
    },
  },
  data() {

    return {
      superviseId:'',
      data:{},
      form: {
        currentSettlementFee:null,
        dateF:'',
        dataS:'',
      },
      rules: {
        currentSettlementFee: [
          {
            required: true,
            message: "请输入服务费金额",
            trigger: "blur",
          },
          {
            validator: this.check,
            trigger: "blur",
          },
        ],
        dateF: [
          {
            required: true,
            message: "请输入开始时间",
            trigger: "blur",
          },
        ],
        dataS: [
          {
            required: true,
            message: "请输入截至时间",
            trigger: "blur",
          },
        ]
      },
    };
  },
  created() {
    this.data=this.dialogCollection.row
    this.getList()

  },

  methods: {
    getList(){
        recordList({settlementServiceFeeId:this.data.settlementServiceFeeId}).then(res=>{
            if(res.code==200){
                if(!Array.isArray(res.result)){
                    res.result.list[0].validEndTime?this.form.dateF= dayjs(res.result.list[0].validEndTime).add(1, 'day').format('YYYY-MM-DD'):'';
                }else{
                    this.form.dateF=this.data.superviseStart
                }
            }
        })
    },
   check(rule, value, callback) {
      if (value <= 0) {
        callback(new Error("金额不能小于等于0"));
      } if(value>parseFloat(this.data.remainServiceFee)){
           callback(new Error("服务费金额不能大于应收服务费"));
      }
      else {
        callback();
      }
    },
    close() {
      this.$emit("close");

    },
    submitForm() {
      this.$refs["modelForm"].validate(async (valid) => {
        if (!valid) {
          return;
        }
        let obj={
          superviseId:this.data.superviseId,
          superviseServiceFee:this.data.superviseServiceFee,
          remainServiceFee:this.data.remainServiceFee,
          currentSettlementFee:this.form.currentSettlementFee,
          validStartTime:this.form.dateF,
          validEndTime:this.form.dataS
        }
        serviceFeeAdd(obj).then(res=>{
          if(res.code==200){
             if (res.code == 200) {
            this.$message({
            type: "success",
            message: "操作成功",
          });
          this.$emit("refresh");
          this.close();
          }
          }
        })
      });
    },
  },
};
</script>

<style lang="scss">
.serviceFee {
  .row {
    padding: 10px 20px;
  }
  .font {
    display: inline-block;
    padding: 10px 100px;
    color: red;
  }
  .el-dialog__header {
    background-color: #f4f4f4;
  }
  .el-dialog__footer {
    text-align: center;
  }
  .el-dialog__body {
    padding: 10px 30px;
  }
  .selectWidth {
    width: 100%;
  }
}
</style>

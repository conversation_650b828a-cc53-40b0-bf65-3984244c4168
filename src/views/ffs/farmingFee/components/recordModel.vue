<template>
  <div>
    <el-dialog
      :title="dialog.title"
      :visible.sync="dialog.open"
      width="1050px"
      :close-on-click-modal="false"
      @close="close"
      class="serviceFees"
    >
         <el-row >应收服务费(元)：<span class="font">{{params.superviseServiceFee}}</span></el-row>
         <el-row >已收服务费(元)：<span class="font">{{params.receiveServiceFee}}</span></el-row>
          <el-table :data="tableData" stripe style="width: 100%" v-loading="loading">
        <el-table-column type="index" label="序号" align="center"></el-table-column>
        <el-table-column prop="currentSettlementFee" label="支付金额(元)" align="center"  />
        <el-table-column prop="operatorName" label="操作人" align="center"></el-table-column>

        <el-table-column prop="operatorTime" label="操作时间" align="center"></el-table-column>
          </el-table>
           <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-dialog>
  </div>
</template>

<script>
import { recordList } from "@/api/ffs/farmingServiceFee";
import modelThreeVue from '../../survey/operate/addFrom/modelThree.vue';
export default {

  props: {
    dialog: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      loading:true,
      total:0,
     tableData:[],
     params:{},
     queryParams:{
      settlementServiceFeeId:'',
      pageNum:1,
      pageSize:10
     }
    };
  },
  created() {
    this.queryParams.settlementServiceFeeId=this.dialog.id
    this.params=this.dialog.row
    this.getList();
  },

  methods: {

    getList() {
     recordList(this.queryParams).then(res=>{
      this.loading=false
      if(res.code==200){
        this.total=Number(res.result.total)
        this.tableData=res.result.list
      }
     })
    },

    close() {
      this.$emit("close");
    },

  },
};
</script>

<style lang="scss">

.serviceFees {
    .font{
        display: inline-block;
        padding: 10px 0;
        color: red;
    }
  .el-dialog__header {
    background-color: #f4f4f4;
  }
  .el-dialog__footer {
    text-align: center;
  }
  .el-dialog__body {
    padding: 10px 30px;
  }
  .selectWidth {
    width: 100%;
  }

}
</style>

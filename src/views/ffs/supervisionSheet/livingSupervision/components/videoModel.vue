<template>
  <div>
    <el-dialog
      title="视频播放"
      :visible.sync="videoModel.open"
      width="800px"
      :close-on-click-modal="false"
      @close="close"
      class="fieldList"
    >
      <div style="   display: flex;margin-bottom: 20px;">
        <el-form ref="form" label-width="80px" :inline="true">
          <el-form-item >
            <el-radio-group v-model="type" @change="selectRadio">
              <el-radio-button :label="'1'">在线视频</el-radio-button>
              <el-radio-button :label="'2'">离线视频</el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="切换视频">
            <el-select v-model="valueUrl" placeholder="请选择" @change="selectUrl">
              <div v-if="type==1">
                <el-option
                  v-for="(item,index) in options"
                  :key="index"
                  :label="item.mountLocation"
                  :value="item.videoId"
                ></el-option>
              </div>
              <div v-if="type==2">
                <el-option
                  v-for="(item,index) in options"
                  :key="index"
                  :label="item.mountLocation"
                  :value="item.videoId"
                ></el-option>
              </div>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <!-- 在线视频 -->
      <video
        autoplay
        controls
        width="100%"
        height="500px"
        muted
        id="myVideo"
        ref="video"
        v-show="type==1&&deviceType!=3"
      ></video>
      <!-- 离线视频 -->
      <video
        autoplay
        controls
        width="100%"
        height="500px"
        muted
        id="myVideo"
        ref="video"
        v-show="type==2"
        :src="offLineUrl"
      ></video>
      <!-- 萤石云播放 -->
      <div id="playWindDom" v-show="deviceType==3" style="height: 500px;">
        <!-- <div id="playWind" v-if="deviceType==3" style="height: 500px;"></div> -->
      </div>

    </el-dialog>
  </div>
</template>

<script>
import flvjs from "flv.js";
import { getPlayUrl,getVideoUrl,ezvizUrl } from "@/api/ffs/supervisionSheet/siteManagement.js";
export default {
  props: {
    videoModel: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      flvPlayer: undefined,
      deviceType: null,
      options: [],
      valueUrl: "",
      offLineUrl:'',
      type: "1",
    };
  },
  created() {
    this.$nextTick(() => {
      this.handelUrl();
    });
  },
  methods: {
    handelUrl() {
      if (this.videoModel.onlineVideoUrl?.length > 0) {
        this.type = "1";
        this.options = this.videoModel.onlineVideoUrl;
        this.deviceType=this.options[0].deviceType
        this.valueUrl=this.options[0].videoId
       this.getPlay(this.options[0].deviceCode,this.options[0].channelCode,this.options[0].ezvizId)
      } else {
        this.type = "2";
        this.options = this.videoModel.offlineVideoUrl;
        this.valueUrl=this.options[0].videoId
        this.offLineUrl=this.options[0].videoUrl
      }
    },
    selectRadio() {
        this.deviceType=null
        this.flv_destroy()
      if (this.type == "1") {
        this.options = this.videoModel.onlineVideoUrl;
        if (this.options.length > 0) {
            this.deviceType=this.options[0].deviceType
        this.valueUrl=this.options[0].videoId
        this.getPlay(this.options[0].deviceCode,this.options[0].channelCode,this.options[0].ezvizId)
        } else {
          this.$message({
            message: "没有在线视频",
            type: "error",
          });
        }
      }
      if (this.type == "2") {
        this.options = this.videoModel.offlineVideoUrl;
        if( this.options.length>0){
            this.options = this.videoModel.offlineVideoUrl;
            this.valueUrl=this.options[0].videoId
            this.offLineUrl=this.options[0].videoUrl
        }else{
            this.$message({
            message: "没有离线视频",
            type: "error",
          });
        }

      }
    },
    selectUrl() {
        this.options.forEach(item=>{
            if(item.videoId==this.valueUrl){
                if(this.type==='1'){
                    this.flv_destroy()
                    this.deviceType=item.deviceType
                    this.getPlay(item.deviceCode,item.channelCode,item.ezvizId)
                }
                if(this.type==='2'){
                    this.offLineUrl=item.videoUrl
                }
            }
        })
    },

    //获取播放地址
    getPlay(deviceCode,channelCode,ezvizId){
        if(this.deviceType==2){
            getVideoUrl({deviceCode:deviceCode}).then(res=>{
                this.loading=false
                if(res.code==200){
                    this.playerVideo(res.result.data);
                }
            })
        }
        if(this.deviceType==3){
            ezvizUrl({deviceCode:deviceCode,channelCode:channelCode,ezvizId:ezvizId}).then(res=>{
                this.loading=false
                if(res.code==200){
                   this.createDom(res.result.accessToken,res.result.url)
                }
            })
        }
        if(this.deviceType==1) {
            getPlayUrl({deviceCode:deviceCode,channelCode:channelCode}).then(res=>{
                this.loading=false
            if(res.code==200){
                this.loading=false
                this.playerVideo(res.result.httpsFmp4);
            }
        })
        }
    },

    playerVideo(url) {
      if (flvjs.isSupported()) {
        this.$nextTick(() => {
          var videoElement = document.getElementById("myVideo");
          let typeValue = this.deviceType == 2 ? "flv" : "mp4";
          this.flvPlayer = flvjs.createPlayer({
            type: typeValue,
            url: url, //你的url地址
            isLive: true,
            hasAudio: false,
            hasVideo: true,
            withCredentials: false,
            autoplay: false,
            cors: true,
          });
          this.flvPlayer.attachMediaElement(videoElement);
          this.flvPlayer.load();
          this.flvPlayer.play();
        });
      } else {
        this.$message({
          type: "warning",
          message: "加载失败",
        });
      }
    },

    flv_destroy() {
      if (this.flvPlayer) {
        this.flvPlayer.pause();
        this.flvPlayer.unload();
        this.flvPlayer.detachMediaElement();
        this.flvPlayer.destroy();
        this.flvPlayer = null;
      }
      if( document.getElementById('playWind')){
        this.EZOPENDemo.stop()
        document.getElementById('playWindDom').innerHTML= "";

      }
    },
    // 萤石云播放
    createDom(accessToken,url){
        document.getElementById('playWindDom').innerHTML= '<div id="playWind" v-if="deviceType==3" style="height: 500px;"></div>';
     var domain = "https://open.ys7.com";
    //   window.EZOPENDemo = EZOPENDemo;
      const ezopenInit = () => {
       this.EZOPENDemo = new EZUIKit.EZUIKitPlayer({
          id: 'playWind',
          width: '760',
          height: '450',
          template: "pcLive",
          url: url,
          accessToken: accessToken
        });
      }
      ezopenInit()
    },
    close() {
      this.flv_destroy();
      this.$emit("close");
    },
  },
};
</script>

<style lang="scss" scoped>
.videlBox {
  display: flex;
  justify-content: flex-end;
}
.videoDom{
        video::-webkit-media-controls-timeline {
        display: none;
      }
      }
</style>

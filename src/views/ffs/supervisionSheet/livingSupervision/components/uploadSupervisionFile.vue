<template>
  <div>
    <el-dialog
      :title="uploadFileData.title"
      :visible.sync="uploadFileData.open"
      :close-on-click-modal="false"
      @close="close"
      width="60%"
      class="fieldList filebox"
    >
      <div class="fileconter">
        <el-form :model="form" status-icon :rules="rules" ref="ruleForm" label-position="top">
          <el-row  >
            <el-col :span="8"  v-if='uploadFileData.type!=2'>
              <el-form-item label="三方协议：" prop="fileTripleAgreement">
                <file-upload
                  :fileType="fileType"
                  :fileSize="100"
                  :limit="1"
                  v-model="form.fileTripleAgreement"
                  :isShowTip="false"
                  expandName="三方协议"
                  :delBtn="uploadFileData.superviseStatus!=3"
                  :disabled="uploadFileData.superviseStatus==3"
                >
                  <el-button type="primary" plain size="mini">
                    <div class="loadname">上传三方协议</div>
                    <i class="el-icon-upload loadicon"></i>
                  </el-button>
                </file-upload>
              </el-form-item>
            </el-col>
            <el-col :span="8" v-if='uploadFileData.type!=2'>
              <el-form-item label="监管合同：" prop="fileSuperviseContract">
                <file-upload
                  :fileType="fileType"
                  :fileSize="100"
                  :limit="1"
                  v-model="form.fileSuperviseContract"
                  :isShowTip="false"
                  expandName="监管合同"
                  :delBtn="uploadFileData.superviseStatus!=3"
                  :disabled="uploadFileData.superviseStatus==3"
                >
                  <el-button type="primary" plain size="mini">
                    <div class="loadname">上传监管合同</div>
                    <i class="el-icon-upload loadicon"></i>
                  </el-button>
                </file-upload>
              </el-form-item>
            </el-col>

            <el-col :span="8" v-if='uploadFileData.type!=2'>
              <el-form-item label="监管方案：" prop="regulatoryScheme">
                <file-upload
                  :fileType="fileType"
                  :fileSize="100"
                  :limit="1"
                  :delButton="false"
                  v-model="form.regulatoryScheme"
                  :isShowTip="false"
                  expandName="监管方案"
                  :delBtn="uploadFileData.superviseStatus!=3"
                  :disabled="uploadFileData.superviseStatus==3"
                >
                  <el-button type="primary" plain size="mini">
                    <div class="loadname">上传监管方案</div>
                    <i class="el-icon-upload loadicon"></i>
                  </el-button>
                </file-upload>
              </el-form-item>
            </el-col>

            <el-col :span="8" v-if='uploadFileData.type==2' >
              <el-form-item label="保险单：" prop="fileInsurance" :rules="{required: true, message: '请上传保险单', trigger: 'change'}">
                <file-upload
                  :fileType="fileType"
                  :fileSize="5"
                  :limit="1"
                  v-model="form.fileInsurance"
                  :isShowTip="false"
                  expandName="保险单"
                  :delBtn="uploadFileData.superviseStatus!=3"
                  :disabled="uploadFileData.superviseStatus==3"
                >
                  <el-button type="primary" plain size="mini">
                    <div class="loadname">上传保险单</div>
                    <i class="el-icon-upload loadicon"></i>
                  </el-button>
                </file-upload>
              </el-form-item>
            </el-col>
            <el-col :span="8" v-if='uploadFileData.type==2' >
              <el-form-item label="保险单到期日期：" prop="fileInsuranceEndTime"  :rules="{required: true, message: '请选择保险单到期日期', trigger: 'change'}">
                <el-date-picker
                    v-model="form.fileInsuranceEndTime"
                    type="date"
                    class="inputWidth"
                    value-format="yyyy-MM-dd"
                    placeholder="选择日期">
                </el-date-picker>
              </el-form-item>
            </el-col>


            <el-col :span="8"  v-if='uploadFileData.type!=2'>
              <el-form-item label="防疫记录本：" prop="fileVaccineRecord">
                <file-upload
                  :fileType="fileType"
                  :fileSize="100"
                  :limit="1"
                  v-model="form.fileVaccineRecord"
                  :isShowTip="false"
                  expandName="防疫记录本"
                  :delBtn="uploadFileData.superviseStatus!=3"
                  :disabled="uploadFileData.superviseStatus==3"
                >
                  <el-button type="primary" plain size="mini">
                    <div class="loadname">上传防疫记录本</div>
                    <i class="el-icon-upload loadicon"></i>
                  </el-button>
                </file-upload>
              </el-form-item>
            </el-col>
            <el-col :span="8"  v-if='uploadFileData.type!=2'>
              <el-form-item label="村镇证明：" prop="fileGazhaTestify">
                <file-upload
                  :fileType="fileType"
                  :fileSize="100"
                  :limit="1"
                  v-model="form.fileGazhaTestify"
                  :isShowTip="false"
                  expandName="村镇证明"
                  :delBtn="uploadFileData.superviseStatus!=3"
                  :disabled="uploadFileData.superviseStatus==3"
                >
                  <el-button type="primary" plain size="mini">
                    <div class="loadname">上传村镇证明</div>
                    <i class="el-icon-upload loadicon"></i>
                  </el-button>
                </file-upload>
              </el-form-item>
            </el-col>

            <el-col :span="8" v-if="!uploadFileData.fileList.intentionListId&& uploadFileData.type!=2">
              <el-form-item label="意向单：" prop="fileIntentionList">
                <file-upload
                  :fileType="fileType"
                  :fileSize="100"
                  :limit="1"
                  v-model="form.fileIntentionList"
                  :isShowTip="false"
                  expandName="意向单"
                  :delBtn="uploadFileData.superviseStatus!=3"
                  :disabled="uploadFileData.superviseStatus==3"
                >
                  <el-button type="primary" plain size="mini">
                    <div class="loadname">上传意向单</div>
                    <i class="el-icon-upload loadicon"></i>
                  </el-button>
                </file-upload>
              </el-form-item>
            </el-col>

            <el-col :span="8" v-if="!uploadFileData.fileList.intentionListId&& uploadFileData.type!=2">
              <el-form-item label="调研表：" prop="fileCreditInvestigation">
                <file-upload
                  :fileType="fileType"
                  :fileSize="100"
                  :limit="1"
                  v-model="form.fileCreditInvestigation"
                  :isShowTip="false"
                  expandName="调研表"
                  :delBtn="uploadFileData.superviseStatus!=3"
                  :disabled="uploadFileData.superviseStatus==3"
                >
                  <el-button type="primary" plain size="mini">
                    <div class="loadname">上传调研表</div>
                    <i class="el-icon-upload loadicon"></i>
                  </el-button>
                </file-upload>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <span v-if='uploadFileData.type==2' class="qiangti">所有大小不超过5MB 格式为 doc/pdf/png/jpg的文件</span>
        <span v-else class="qiangti">所有大小不超过100MB 格式为 doc/pdf/png/jpg的文件</span>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="close">取消</el-button>
        <el-button type="primary" @click="submitForm" v-if="uploadFileData.superviseStatus!=3">提交</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { editSuperviseFile } from "@/api/ffs/supervisionSheet/livingSupervisionApi.js";
export default {
  name: "uploadSupervisionFile",
  props: {
    uploadFileData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      fileType: ["doc", "pdf", "png", "jpg", "jpeg"],
      form: {},
      rules: {},
    };
  },
  created() {
    this.form = this.uploadFileData.fileList;
  },
  computed: {
    labelName() {
      return (val) => {
        if (!val) return;
        if (val.nickName) return val.nickName;
        if (val.userName) return val.userName;
        if (val.corprateName) return val.corprateName;
        if (val.phonenumber) return val.phonenumber;
      };
    },
  },
  methods: {
    close() {
      this.$emit("close");
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["ruleForm"].validate((valid) => {
        if (valid) {
          this.$modal.loading("正在提交中，请耐心等待...");

          editSuperviseFile(this.form)
            .then((response) => {
              this.$modal.closeLoading();
              if (response.code == 200) {
                this.$modal.msgSuccess("提交成功");
                this.open = false;
                this.$emit("refresh");
                this.$emit("close");
              }
            })
            .catch(() => {
              this.$modal.closeLoading();
            });
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.fieldList {
  .el-dialog__header {
    background-color: #f4f4f4;
  }

  .el-dialog__footer {
    text-align: center;
  }

  .selectWidth {
    width: 100%;
  }
  .loadicon {
    font-size: 18px;
    color: #1983e6;
    margin-left: 10px;
    vertical-align: text-bottom;
  }
  .loadicon:hover {
    color: #fff !important;
  }
  .fileconter {
    padding-left: 40px;
  }
}
.loadname {
  display: inline-block;
  width: 120px;
}
</style>


<style  lang="scss">
.filebox {
  .filelistbx {
    width: 180px !important;
  }
}
</style>

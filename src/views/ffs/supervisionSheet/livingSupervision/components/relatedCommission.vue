<template>
  <div>
    <el-dialog
      :title="commissionData.title"
      :visible.sync="commissionData.open"
      :close-on-click-modal="false"
      @close="close"
      class="fieldList"
      center
    >
      <el-form :model="form" status-icon :rules="rules" ref="ruleForm" label-width="95px">
        <el-row :gutter="100">
          <el-col :span="24">
            <el-form-item label="分佣规则：" prop="supervisorName">
              <el-select
                v-model="form.supervisorName"
                filterable
                remote
                clearable
                reserve-keyword
                placeholder="请选择分佣规则/输入搜索关键字"
                :remote-method="remoteMethod"
                class="selectWidth"
                @change="onChange"
              >
                <el-option
                  v-for="item in selectList"
                  :key="item.comsiOpeId"
                  :label="item.comsiName"
                  :value="item.comsiOpeId"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <ul class="commission" v-if="selectData.comsiName">
        <li>
          <span class="commission-title">分佣规则名称：</span>
          <span class="commission-content">{{selectData.comsiName||'--'}}</span>
        </li>
        <li>
          <span class="commission-title">监管类型：</span>
          <span class="commission-content">活体监管</span>
        </li>
        <li>
          <span class="commission-title">运营中心分佣比例：</span>
          <span class="commission-content">约{{selectData.opeComsiValue||0}}%</span>
        </li>
        <li style="color: #ffba00;">
          <span >备注：</span>
          <span class="commission-content">运营中心分佣=运营中心总佣金-畜牧师佣金-合伙人佣金，所得金额以实际分佣金额为准！</span>
        </li>
        <li>
          <span class="commission-title">区域合伙人分佣比例：</span>
          <span class="commission-content">{{selectData.partnerComsiValue||0}}%</span>
        </li>
        <li>
          <span class="commission-title">监管员分佣：</span>
          <span class="commission-content">{{selectData.supervisorComsiValue||0}}元/头/月</span>
        </li>
      </ul>

      <span slot="footer" class="dialog-footer">
        <el-button @click="close">取消</el-button>
        <el-button type="primary" @click="submitForm">提交</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  commissionRule,
  relationRule,
  selectBySuperviseId,
} from "@/api/ffs/supervisionSheet/livingSupervisionApi.js";
export default {
  name: "updataSupervisor",
  props: {
    commissionData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      selectList: [],
      showshowLoanList: [],
      form: {
        superviseId: "",
        comsiPlatId: "",
        supervisorName: "",
        subjectType: "1", //1活体，2仓单 3资金流向
        opeIds: "",
      },
      selectData: "",
      rules: {
        opeIds: [
          {
            required: true,
            message: "请选择分佣规则",
            trigger: "blur",
          },
        ],
      },
    };
  },
  created() {
    const { superviseId, superviseType } = this.commissionData.editInfo;
    this.form.superviseId = superviseId;
    this.form.superviseType = superviseType;
    this.getRelatedCommission();
  },
  computed: {},
  methods: {
    close() {
      this.$emit("close");
    },
    // 查询分佣绑定信息
    getRelatedCommission() {
      selectBySuperviseId({
        ids: [this.commissionData.editInfo.superviseId],
      }).then((res) => {
        console.log("查询分佣绑定信息：", res);
        if (res.code == 200) {
          const result = res.result || [];
          this.remoteMethod("");
          if (result.length != 0) {
            this.changeSelectVal(result[0]);
          }
        }
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["ruleForm"].validate((valid) => {
        if (valid) {
          const {
            comsiName,
            opeComsiValue,
            partnerComsiValue,
            supervisorComsiValue,
          } = this.selectData;
          this.$confirm(
            `
           <strong class='cmomstxss'>是否确定使用所选分佣规则确定后，监管单则按照所选规则进行分佣：</strong>
           <div> </div>

            <div>分佣规则名称：${comsiName||'--'}</div>
             <div>监管类型：活体监管</div>
            <div>运营中心分佣比例：约${opeComsiValue||0}%</div>
            <div>区域合伙人分佣比例：${partnerComsiValue||0}%</div>
            <div>监管员分佣：${supervisorComsiValue||0}元/头/月</div>
           `,
            "信息确认",
            {
              distinguishCancelAndClose: true,
              dangerouslyUseHTMLString: true,
              confirmButtonText: "确认提交",
              cancelButtonText: "重新选择",
            }
          ).then(() => {
            this.$modal.loading("正在提交中，请耐心等待...");
            relationRule(this.form)
              .then((response) => {
                this.$modal.closeLoading();
                if (response.code == 200) {
                  this.$modal.msgSuccess("提交成功");
                  this.open = false;
                  this.$emit(
                    "refresh",
                    this.form.receiveAmount,
                    this.form.appendServiceFee
                  );
                  this.$emit("close");
                  this.$emit("closeAddBaseForm");
                }
              })
              .catch(() => {
                this.$modal.closeLoading();
              });
          });
        }
      });
    },

    // 确定选择分佣规则
    onChange(val) {
      if (val == "") {
        this.selectData = "";
        return;
      }
      const selectVal = this.selectList.find((item) => item.comsiOpeId == val);
      this.changeSelectVal(selectVal);
    },

    changeSelectVal(data) {
      this.selectData = data;
      this.form.opeIds = data?.comsiOpeId;
      this.form.comsiPlatId = data?.comsiPlatId;
      this.form.supervisorName = data?.comsiName;
    },

    //远程搜索
    remoteMethod(data) {
      commissionRule({
        pageNum: 1,
        pageSize: 999,
        commission: "LIVE_ANIMAL_SUPERVISE", //LIVE_ANIMAL_SUPERVISE 活体、  WAREHOUSE_SUPERVISE  仓单
        commissionType: 1, //1分佣规则，2是提现规则
        comsiName: data,
      }).then((res) => {
        if (res.code == 200) {
          this.selectList = res.result.list;
        }
      });
    },
  },
};
</script>

<style lang="scss" >
.fieldList {
  .el-dialog__header {
    background-color: #f4f4f4;
  }

  .el-dialog__footer {
    text-align: center;
  }

  .selectWidth {
    width: 100%;
  }
}
.commission {
  background: #eee;
  border-radius: 10px;
  list-style: none;
  padding: 20px 10px 10px 10px;
  &-title {
    width: 170px;
    font-size: 14px;
    display: inline-block;
  }
  &-content {
    flex: 1;
    font-size: 14px;
  }
}
.commission li {
  margin-bottom: 10px;
}
.cmomstxss {
  display: inline-block;
  margin-bottom: 20px;
}
</style>

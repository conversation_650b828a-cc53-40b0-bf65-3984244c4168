<template>
    <div class="PosttowStep">
        <el-form :model="form" :rules="rules" ref="ruleForm" label-width="165px">
            <el-form-item label="三方协议：" prop="livestockFileTripleAgreement">
                <file-upload :fileType="fileType" :fileSize="100" :limit="1" v-model="form.livestockFileTripleAgreement" :isShowTip="false" expandName="三方协议" :preview="true">
                    <el-button type="primary" plain>
                        <div class="loadname">上传三方协议</div>
                        <i class="el-icon-upload loadicon"></i>
                    </el-button>
                </file-upload>
            </el-form-item>
            <el-form-item label="监管合同：" prop="livestockFileSuperviseContract" v-if="superviseType==1">
                <file-upload :fileType="fileType" :fileSize="100" :limit="1" v-model="form.livestockFileSuperviseContract" :isShowTip="false" expandName="监管合同" :preview="true">
                    <el-button type="primary" plain>
                        <div class="loadname">上传监管合同</div>
                        <i class="el-icon-upload loadicon"></i>
                    </el-button>
                </file-upload>
            </el-form-item>
            <el-form-item label="监管方案：" prop="livestockFileRegulatoryScheme">
                <file-upload :fileType="fileType" :fileSize="100" :limit="1" v-model="form.livestockFileRegulatoryScheme" :isShowTip="false" expandName="监管方案" :preview="true">
                    <el-button type="primary" plain>
                        <div class="loadname">上传监管方案</div>
                        <i class="el-icon-upload loadicon"></i>
                    </el-button>
                </file-upload>
            </el-form-item>
            <el-form-item label="保险单：" prop="livestockFileInsurance" v-if="superviseType==1">
                <file-upload :fileType="fileType" :fileSize="100" :limit="1" v-model="form.livestockFileInsurance" :isShowTip="false" expandName="保险单" :preview="true">
                    <el-button type="primary" plain>
                        <div class="loadname">上传保险单</div>
                        <i class="el-icon-upload loadicon"></i>
                    </el-button>
                </file-upload>
            </el-form-item>
            <el-form-item label="防疫记录本：" prop="livestockFileVaccineRecord" v-if="superviseType==1">
                <file-upload :fileType="fileType" :fileSize="100" :limit="1" v-model="form.livestockFileVaccineRecord" :isShowTip="false" expandName="防疫记录本" :preview="true">
                    <el-button type="primary" plain>
                        <div class="loadname">上传防疫记录本</div>
                        <i class="el-icon-upload loadicon"></i>
                    </el-button>
                </file-upload>
            </el-form-item>
            <el-form-item label="村镇证明：" prop="livestockFileGazhaTestify" v-if="superviseType==1">
                <file-upload :fileType="fileType" :fileSize="100" :limit="1" v-model="form.livestockFileGazhaTestify" :isShowTip="false" expandName="村镇证明" :preview="true">
                    <el-button type="primary" plain>
                        <div class="loadname">上传村镇证明</div>
                        <i class="el-icon-upload loadicon"></i>
                    </el-button>
                </file-upload>
            </el-form-item>
            <el-form-item label="监管意向：" prop="livestockFileIntentionList">
                <file-upload :fileType="fileType" :fileSize="100" :limit="1" v-model="form.livestockFileIntentionList" :isShowTip="false" expandName="监管意向" :preview="true">
                    <el-button type="primary" plain>
                        <div class="loadname">上传监管意向</div>
                        <i class="el-icon-upload loadicon"></i>
                    </el-button>
                </file-upload>
            </el-form-item>
            <el-form-item label="调研表：" prop="livestockFileCreditInvestigation">
                <file-upload :fileType="fileType" :fileSize="100" :limit="1" v-model="form.livestockFileCreditInvestigation" :isShowTip="false" expandName="调研表" :preview="true">
                    <el-button type="primary" plain>
                        <div class="loadname">上传调研表</div>
                        <i class="el-icon-upload loadicon"></i>
                    </el-button>
                </file-upload>
            </el-form-item>
            <el-form-item label="贷款单：" prop="fileLoan" v-if="superviseType==2">
                <file-upload :fileType="fileType" :fileSize="100" :limit="1" v-model="form.fileLoan" :isShowTip="false" expandName="贷款单" :preview="true">
                    <el-button type="primary" plain>
                        <div class="loadname">上传贷款单</div>
                        <i class="el-icon-upload loadicon"></i>
                    </el-button>
                </file-upload>
            </el-form-item>
            <el-form-item label="租赁合同：" prop="fileLeaseContract" v-if="superviseType==2">
                <file-upload :fileType="fileType" :fileSize="100" :limit="1" v-model="form.fileLeaseContract" :isShowTip="false" expandName="租赁合同" :preview="true">
                    <el-button type="primary" plain>
                        <div class="loadname">上传租赁合同</div>
                        <i class="el-icon-upload loadicon"></i>
                    </el-button>
                </file-upload>
            </el-form-item>
            <el-form-item label="其它：" prop="fileOther" v-if="superviseType==2">
                <file-upload :fileType="fileType" :fileSize="100" :limit="1" v-model="form.fileOther" :isShowTip="false" expandName="其它" :preview="true">
                    <el-button type="primary" plain>
                        <div class="loadname">上传其它文件</div>
                        <i class="el-icon-upload loadicon"></i>
                    </el-button>
                </file-upload>
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
export default {
    props:{
        superviseInfo:{
            type:Object,
            default:{}
        },
        info: {
            type: Object,
            default: () => { },
        },
    },
    data() {
        return {
            fileType: ["doc", "pdf", "png", "jpg", "jpeg"],
            superviseType:'',//监管类型
            form: {
                livestockFileIntentionList:'',//监管意向
                livestockFileCreditInvestigation:'',//调研表
                livestockFileRegulatoryScheme:'',//监管方案
                livestockFileTripleAgreement:'',//三方协议
                livestockFileSuperviseContract:'',//监管合同
                livestockFileVaccineRecord:'',//防疫记录
                livestockFileInsurance:'',//保险单
                livestockFileGazhaTestify:'',//村镇证明
                fileLoan:'',//贷款单
                fileLeaseContract:'',//租赁合同
                fileOther:'',//其他文件
            },
            rules: {
                livestockFileTripleAgreement:[
                { required: true, message: "请上传三方协议", trigger: "blur" },
                ]
            },
        };
    },

    created() { 
        this.superviseType=this.superviseInfo.superviseType
    },

    methods: {
        //处理数据
        handeerObj(){
            Object.keys(this.form).forEach(key=>{
                this.form[key]=this.info[key]
            })
        },
        submitForm() {
            return new Promise((resolve, reject)=>{
                this.$refs["ruleForm"].validate((valid) => {
                    if (valid) {
                            resolve(true)
                        }else{
                            resolve(false)
                        }
                    });
                })
        
      },
    },
};
</script>

<style lang="scss" >
.PosttowStep{
    .el-button{
        width: 100% !important;
    }
    .el-upload{
        width: 100% !important;
    }
}

.loadname {
    display: inline-block;
    width: 120px;
}
</style>
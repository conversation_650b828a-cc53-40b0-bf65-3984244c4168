<template>
    <div>
      <el-dialog
        title="续单"
        :visible.sync="postPone.open"
        :close-on-click-modal="false"
        @close="close"
        append-to-body
        width="600px"
        class="postPone"
      >
         <oneStep v-show="step==1" :info="info" :superviseInfo="superviseInfo" ref="oneStep"></oneStep>
         <towStep v-show="step==2" :info="info" :superviseInfo="superviseInfo" ref="towStep"></towStep>
        <span slot="footer" class="dialog-footer">
          <el-button @click="close">取消</el-button>
          <el-button @click="nextStep" type="primary" v-show="step==1">下一步</el-button>
          <el-button @click="step=1" type="primary"  v-show="step==2">上一步</el-button>
          <el-button type="primary" @click="submitForm" v-show="step==2">提交</el-button>
        </span>
      </el-dialog>
    </div>
  </template>

  <script>
  import {
    delayEndTime,
    queryEndTime,
  } from "@/api/ffs/supervisionSheet/livingSupervisionApi.js";
  import {delayEndTime as farmingDelayEndTime, queryEndTime as farmingQueryEndTime} from '@/api/ffs/farmingSupervisionSheet/livingSupervisionApi'
  import oneStep from './oneStep.vue'
  import towStep from './towStep.vue'
  export default {
    components:{
        oneStep,
        towStep
    },
    props: {
      postPone: {
        type: Object,
        default: () => {},
      },
    },
    data() {
      return {
        step:1,
        superviseInfo:{},
        info:{}
      };
    },
    created() {
      this.superviseInfo=this.postPone.info||{}
      this.getInfo()
    },
    methods: {
      close() {
        this.$emit("close");
      },
      nextStep(){
        this.$refs.oneStep.submitForm().then(res=>{
            if(res){
                this.step=2
            }else{
                this.step=1
            }
        })

      },
      /** 提交按钮 */
      submitForm() {
        let obj={...this.$refs.towStep.form,...this.$refs.oneStep.form}
        this.$refs.towStep.submitForm().then(res=>{
            if(res){
              if(this.postPone.sourceType==1) {
                delayEndTime(obj).then(res=>{
                  if(res.code==200){
                    this.$modal.msgSuccess("提交成功");
                    this.$emit('refresh')
                    this.close()
                  }
                })
              } else {
                farmingDelayEndTime(obj).then(res=>{
                  if(res.code==200){
                    this.$modal.msgSuccess("提交成功");
                    this.$emit('refresh')
                    this.close()
                  }
                })
              }

            }
        })
        },
      getInfo(){
        console.log(this.postPone.sourceType)
        if(this.postPone.sourceType==1) {
          queryEndTime({superviseId:this.superviseInfo.superviseId}).then(res=>{
            if(res.code==200){
              this.info=res.result||{}
              this.$nextTick(()=>{
                this.$refs.oneStep.echoInfo()
                this.$refs.towStep.handeerObj()
              })
            }
          })
        } else {
          farmingQueryEndTime({superviseId:this.superviseInfo.superviseId}).then(res=>{
            if(res.code==200){
              this.info=res.result||{}
              this.$nextTick(()=>{
                this.$refs.oneStep.echoInfo()
                this.$refs.towStep.handeerObj()
              })
            }
          })
        }


      }
  }
  };
  </script>

  <style lang="scss" >
  .postPone {
    .el-dialog__body {
      padding-right: 80px;
    }

    .el-dialog__header {
      background-color: #f4f4f4;
    }

    .el-dialog__footer {
      text-align: center;
    }

    .selectWidth {
      width: 100% !important;
    }
  }
  </style>

<template>
  <div>
    <el-row :gutter="10" class="mb8">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="90px">
        <el-row class="form_row">
         <el-col class="form_col">
        <el-form-item label="耳标编号：" prop="earTagNo">
          <el-input v-model="queryParams.earTagNo" placeholder="请输入耳标编号" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>

        <el-form-item label="活畜分类：" prop="typeId">
          <el-select v-model="queryParams.typeId" placeholder="请选择活畜品种">
            <el-option label="全部" value></el-option>
            <el-option
              v-for="item in livestock"
              :key="item.livestockId"
              :label="item.livestockName"
              :value="item.livestockId"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="活畜品种：" prop="categoryId">
          <el-select v-model="queryParams.categoryId" placeholder="请选择活畜品种">
            <el-option label="全部" value></el-option>
            <el-option
              v-for="item in livestockCategoryList"
              :key="item.categoryId"
              :label="item.categoryName"
              :value="item.categoryId"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="活畜种类：" prop="varietiesId">
          <el-select v-model="queryParams.varietiesId" placeholder="请选择活畜种类">
            <el-option label="全部" value></el-option>
            <el-option
              v-for="item in livestockVarietiesList"
              :key="item.varietiesId"
              :label="item.varietiesName"
              :value="item.varietiesId"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="养殖场名称：" prop="pastureName">
          <el-input v-model="queryParams.pastureName" placeholder="请输入养殖场名称" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        </el-col>
        </el-row>
        <el-row style="margin-left: 90px;">
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          <template >
                <el-button type="text" @click="packUp" >
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <i
                  :class="{ 'el-icon-arrow-down': !toggleSearchStatus, 'el-icon-arrow-up': toggleSearchStatus }"
                ></i>
              </el-button>
              </template>
        </el-form-item>
        </el-row>
      </el-form>
    </el-row>

    <el-table v-loading="loading" :data="list" border>
      <el-table-column label="序号" type="index" fixed width="50"></el-table-column>
      <el-table-column label="耳标编号" prop="earTagNo"></el-table-column>
      <el-table-column label="活畜分类" prop="typeName"></el-table-column>
      <el-table-column label="活畜品种" prop="categoryName"></el-table-column>
      <el-table-column label="活畜种类" prop="varietiesName"></el-table-column>
      <el-table-column label="活畜月龄" prop="livestockAge" :formatter="ageName"></el-table-column>
      <el-table-column label="出生日期" prop="birthday"></el-table-column>
      <el-table-column label="所属养殖场" prop="pastureName"></el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import {
  livestockCategory,
  livestockVarieties,
  livestockCategoryTable,
} from "@/api/xmb/trade/market/publishAnimal";
import { getDicts } from "@/api/system/dict/data.js";
import { superviseLivestockList } from "@/api/ffs/supervisionSheet/livingSupervisionApi";
import { searchUi } from "@/utils/mixin/searchUi.js";
export default {
  name: "animalInfo",
  mixins: [searchUi],
  props: {
    info: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      queryParams: {
        superviseId: "",
        categoryId: "",
        varietiesId: "",
        pastureName: "",
        typeId: "",
        earTagNo: "",
        pageNum: 1,
        pageSize: 10,
      },
      form: {},

      ageList: [],

      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 表格数据
      list: [],
      livestockCategoryList: [],
      livestockVarietiesList: [],
      livestock: [],
    };
  },
  created() {
    this.initData();
  },
  watch: {
    info(val) {
      if (val) {
        this.queryParams.superviseId = val.superviseId;
        this.form = val;
        this.getList();
      }
    },
  },
  computed: {
    // 活畜月龄
    ageName() {
      return (r,c,val) => {
        let dictLabel = "";
        if (!this.ageList) return;
        this.ageList.forEach((item) => {
          if (val == item.dictValue) {
            dictLabel = item.dictLabel;
          }
        });
        return dictLabel;
      };
    },
  },
  methods: {
    /** 查询列表 获取监管单关联的活畜列表 */
    getList() {
      superviseLivestockList(this.queryParams).then((res) => {
        this.loading = false;
        if (res.code == 200) {
          this.list = res.result.list || [];
          this.total = Number(res.result.total || 0);
        } else {
          this.$message.error(res.message);
        }
      });
    },

    initData() {
      // 活畜月龄
      getDicts("livestock_age").then((res) => {
        this.ageList = res.data || [];
      });

      // 分类
      livestockCategory({
        pageSize: 9999,
        pageNum: 1,
      }).then((res) => {
        if (res.code == 200) {
          this.livestockCategoryList = res.result;
        }
      });

      // 品种
      livestockVarieties({
        pageSize: 9999,
        pageNum: 1,
      }).then((res) => {
        if (res.code == 200) {
          this.livestockVarietiesList = res.result;
        }
      });

      // 活畜类别
      livestockCategoryTable({
        pageSize: 9999,
        pageNum: 1,
      }).then((res) => {
        if (res.code == 200) {
          this.livestock = res.result;
        }
      });
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },

    //关闭弹框
    close() {
      this.$emit("close");
    },
  },
};
</script>

<style lang="scss">
.fieldList {
  .el-dialog__header {
    background-color: #f4f4f4;
  }

  .el-dialog__footer {
    text-align: center;
  }

  .selectWidth {
    width: 100%;
  }
}
</style>

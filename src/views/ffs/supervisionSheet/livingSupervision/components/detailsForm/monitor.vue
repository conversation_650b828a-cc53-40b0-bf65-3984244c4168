<template>
  <div class="app-container">
    <el-card style="padding:10px 0">
      <el-row :gutter="20" style="padding:10px 0;font-size:16px ;font-weight: 700;">
        <el-col :span="6">养殖场名称：{{pastureData.pastureName}}</el-col>
        <el-col
          :span="8"
        >养殖场地址：{{pastureData.provinceName}}{{pastureData.cityName}}{{pastureData.countyName}}</el-col>
        <el-col :span="10">
          <div class="monitor-census">
            <span>视频监控总数：{{tableData.length}}</span>
            <!-- <span>在线：19</span>
            <span>离线：19</span>-->
          </div>
        </el-col>
      </el-row>
      <div class="monitor-conter" v-for="(item ,index) in tableData" :key="index">
        <div class="monitor-conter-imge">
          <div class="monitor-conter-imge-play" @click="pay(item)">
            <i class="el-icon-video-play"></i>
          </div>
          <div class="monitor-conter-imge-number">摄像头编号：{{item.deviceCode}}</div>
        </div>
      </div>
    </el-card>
    <videoModel v-if="videoOpen.open" :videoOpen="videoOpen" @close="close"></videoModel>
  </div>
</template>
      <script>
import {
  pastureVideoList,
  pastureById,
} from "@/api/ffs/supervisionSheet/siteManagement.js";
import videoModel from "@/views/ffs/supervisionSheet/siteManagement/components/videoModel.vue";

export default {
  components: {
    videoModel,
  },
  data() {
    return {
      monitor: {
        open: false,
        id: "",
      },
      videoOpen:{
        open:false,
        url:''
      },
      animalsCategory: [], //活畜类别
      animalsVarieties: [], //品种
      animalsType: [], //类型
      videoPlayer: "",
      tableData: [],
      pastureData: {},
      flvPlayer: "",
      queryParams: {
        pastureId: "",
        userPhone: "",
      },
    };
  },
  computed: {},

  created() {
    this.queryParams.pastureId = this.$route.query.pastureId;
    this.getPasture();
  },

  methods: {
    //查询养殖场信息
    getPasture() {
      pastureById({ ids: [this.queryParams.pastureId] }).then((res) => {
        if (res.code == 200) {
          this.pastureData = res.result;
          this.queryParams.userPhone = res.result.userName;
          this.getList();
        }
      });
    },
    //列表查询
    getList() {
      pastureVideoList(this.queryParams).then((res) => {
        if (res.code == 200) {
          this.tableData = res.result.list;
          this.loading = false;
        }
      });
    },
    refresh() {
      this.getList();
    },
    //页面返回
    goback() {
      this.$tab.closeOpenPage();
      this.$router.go(-1);
    },
    reset() {
      this.resetForm("queryForm");
    },
    close() {
      this.monitor.open = false;
      this.videoOpen.open=false
    },
    addMonitor() {
      this.monitor.open = true;
    },
    //视频播放
    pay(item) {
      this.videoOpen.open= true;
      this.videoOpen.url=item
    },
  },
};
</script>
<style lang="scss" scoped>
video::-webkit-media-controls-timeline {
  display: none;
}
.clearfix {
  display: flex;
  justify-content: space-between;
}
.monitor {
  &-census {
    font-weight: 400;
    display: flex;
    width: 100%;
    justify-content: flex-end;
    span {
      padding: 0 10px;
    }
  }
  &-conter {
    &-imge {
      position: relative;
      display: inline-block;
      overflow: hidden;
      box-sizing: border-box;
      width: 300px;
      height: 300px;
      margin: 20px 20px;
      background: rgba(242, 243, 245, 1);
      &-onLine {
        position: absolute;
        right: 10px;
        top: 10px;
        color: #13ce66;
      }
      &-number {
        position: absolute;
        left: 10px;
        bottom: 10px;
      }
      &-play {
        font-size: 70px;
        position: absolute;
        left: 50%;
        top: 50%;
        color: black;
        transform: translate(-50%, -50%);
      }
    }
  }
}
</style>
      
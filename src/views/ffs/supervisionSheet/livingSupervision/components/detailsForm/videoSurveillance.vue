<!--  -->
<template>
    <div class="main">
      <monitor :headBar="false" :info="info" />
    </div>
  </template>
  
  <script>
  import monitor from "@/views/ffs/supervisionSheet/siteManagement/monitor.vue";
  export default {
    name: "videoSurveillance",
    props: {
      info: {
        type: Object,
        default: () => {},
      },
    },
    components: {
      monitor,
    },
    data() {
      return {};
    },
    watch: {
      info(val) {
        if (val) {
          // console.log("applyName：", this.info);
        }
      },
    },
    created() {},
    mounted() {},
  };
  </script>
  <style lang="scss" scoped>
  /* @import url(); 引入css类 */
  </style>
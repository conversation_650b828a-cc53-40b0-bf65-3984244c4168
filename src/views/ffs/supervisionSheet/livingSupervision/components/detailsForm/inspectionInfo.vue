<template>
  <div>
    <el-dialog
      :title="inspectionInfomData.title"
      :visible.sync="inspectionInfomData.open"
      width="1250px"
      :close-on-click-modal="false"
      @close="close"
      class="fieldList"
      append-to-body
    >
      <el-descriptions title="基本信息" :column="3" :contentStyle="CS" :label-style="LS">
        <div slot="title" style="display:flex;justify-content: space-between ;width: 1210px;">
          <span>基本信息</span>
          <span class="qihsd mlt20">巡检结果：{{patrolStatusName(info.patrolStatus)}}</span>
        </div>
        <el-descriptions-item label="巡检位置">{{info.patrolLivestockList[0].patrolPos}}</el-descriptions-item>
        <el-descriptions-item label="耳标掉标情况">{{info.patrolLivestockList[0].eartagSituation}}</el-descriptions-item>
        <!-- <el-descriptions-item label="pda盘点数量">{{info.patrolLivestockList[0].patrolLivestockCount}}</el-descriptions-item> -->
        <el-descriptions-item label="现有基础母牛总数量">{{info.patrolLivestockList[0].catagory3Count}}</el-descriptions-item>
        <el-descriptions-item label="现有牛犊数量">{{info.patrolLivestockList[0].patrolCalfCount}}</el-descriptions-item>
        <el-descriptions-item label="保踹母牛">{{info.patrolLivestockList[0].catagory1Count}}</el-descriptions-item>
        <el-descriptions-item label="预计出栏">{{info.patrolLivestockList[0].planSlaughter}}</el-descriptions-item>
        <el-descriptions-item label="出栏说明">{{info.patrolLivestockList[0].slaughterRemark}}</el-descriptions-item>
        <el-descriptions-item label="其它母牛">{{info.patrolLivestockList[0].patrolOtherCount}}</el-descriptions-item>
        <el-descriptions-item label="淘汰母牛">{{info.patrolLivestockList[0].catagory2Count}}</el-descriptions-item>
        <el-descriptions-item label="死亡数量">{{info.patrolLivestockList[0].deadCount}}</el-descriptions-item>
      </el-descriptions>

      <el-descriptions :column="3" :contentStyle="CS" :label-style="LS">
        <el-descriptions-item label="被监管方是否有特殊情况">{{ patrolLivestock.specialType==1? '是' : '否' }}</el-descriptions-item>
        <el-descriptions-item v-if="patrolLivestock.specialType==1" label="类型">{{ formartSpecialType(patrolLivestock.specialDetail)}} <span v-if="patrolLivestock.specialDetail.includes(5)">({{ patrolLivestock.specialOther }})</span></el-descriptions-item>
      </el-descriptions>
      <el-descriptions :column="3" :contentStyle="CS" :label-style="LS">
        <el-descriptions-item label="是否有新增贷款">{{ patrolLivestock.newlyLoansCheck==1? '是' : '否' }}</el-descriptions-item>
        <el-descriptions-item v-if="patrolLivestock.newlyLoansCheck==1" label="贷款银行">{{ patrolLivestock.newlyLoansBank}}</el-descriptions-item>
        <el-descriptions-item v-if="patrolLivestock.newlyLoansCheck==1" label="贷款金额">{{ patrolLivestock.newlyLoansAmount}}万元</el-descriptions-item>
        <el-descriptions-item v-if="patrolLivestock.newlyLoansCheck==1" label="贷款类型">{{ patrolLivestock.newlyLoansType==1? '信用贷款': '抵押贷款'}}</el-descriptions-item>
        <el-descriptions-item v-if="patrolLivestock.newlyLoansCheck==1 && patrolLivestock.newlyLoansType==2" label="抵押物">{{ patrolLivestock.newlyLoansGoods}}</el-descriptions-item>
        <el-descriptions-item v-if="patrolLivestock.newlyLoansCheck==1" label="贷款期限">{{ formartLoansDate(patrolLivestock.newlyLoansDate)}}</el-descriptions-item>
      </el-descriptions>

      <el-descriptions :column="3" :contentStyle="CS" :label-style="LS">
        <div slot="title" style="display:flex;justify-content: space-between ;width: 1210px;">
          <span>饲草料使用情况</span>
        </div>
        <el-descriptions-item label="牧草">{{ formartGrass(patrolLivestock.grassUse) }}</el-descriptions-item>
        <el-descriptions-item label="合计用量">{{ patrolLivestock.grassUseDosage || '--'}} 吨/年</el-descriptions-item>
      </el-descriptions>

      <el-descriptions :column="3" :contentStyle="CS" :label-style="LS">
        <el-descriptions-item label="饲料品牌">{{ patrolLivestock.feedBrand || '--' }}</el-descriptions-item>
        <el-descriptions-item label="合计用量">{{ patrolLivestock.feedUse || '--'}} 吨/年</el-descriptions-item>
        <el-descriptions-item label="品种">{{ formartFeedVariety(patrolLivestock.feedVariety)}}</el-descriptions-item>
      </el-descriptions>
      <el-descriptions :column="3" :contentStyle="CS" :label-style="LS">
        <el-descriptions-item label="玉米品种">{{ formartMaizeCategory(patrolLivestock.maizeCategory)}}</el-descriptions-item>
        <el-descriptions-item label="合计用量">{{ patrolLivestock.maizeDosage || '--'}} 吨/年</el-descriptions-item>
      </el-descriptions>
      <el-descriptions :column="3" :contentStyle="CS" :label-style="LS">
        <div slot="title" style="display:flex;justify-content: space-between ;width: 1210px;">
          <span>饲草料当前储备情况</span>
        </div>
        <el-descriptions-item label="牧草">{{ patrolLivestock.grassReserve || '--'}} 吨</el-descriptions-item>
        <el-descriptions-item label="饲料">{{ patrolLivestock.feedReserve || '--'}} 吨</el-descriptions-item>
        <el-descriptions-item label="玉米">{{ patrolLivestock.maizeReserve || '--'}} 吨</el-descriptions-item>
        <el-descriptions-item label="青贮">{{ patrolLivestock.greenReserve || '--'}} 吨</el-descriptions-item>
      </el-descriptions>

      <el-descriptions :column="3" :contentStyle="CS" :label-style="LS">
        <div slot="title" style="display:flex;justify-content: space-between ;width: 1210px;">
          <span>草场、基础设施情况</span>
        </div>
        <el-descriptions-item label="草场有无变化">{{ patrolLivestock.meadowChange == 1 ? '是' : '否'}}</el-descriptions-item>
        <el-descriptions-item v-if="patrolLivestock.meadowChange==1" label="自有">{{ patrolLivestock.meadowOwn || '--'}} 亩</el-descriptions-item>
        <el-descriptions-item v-if="patrolLivestock.meadowChange==1" label="租赁">{{ patrolLivestock.meadowLease || '--'}} 亩</el-descriptions-item>
      </el-descriptions>

      <el-descriptions :column="3" :contentStyle="CS" :label-style="LS">
        <el-descriptions-item label="棚圈有无变化">{{ patrolLivestock.penChange == 1 ? '是' : '否'}}</el-descriptions-item>
        <el-descriptions-item v-if="patrolLivestock.penChange==1" label="自有">{{ patrolLivestock.penOwn || '--'}} 平米</el-descriptions-item>
        <el-descriptions-item v-if="patrolLivestock.penChange==1" label="租赁">{{ patrolLivestock.penLease || '--'}} 平米</el-descriptions-item>
      </el-descriptions>

      <el-descriptions :column="3" :contentStyle="CS" :label-style="LS">
        <div slot="title" style="display:flex;justify-content: space-between ;width: 1210px;">
          <span>农牧机械设备</span>
        </div>
        <el-descriptions-item label="设备类型">{{ formartMechanical(patrolLivestock.mechanical)}}</el-descriptions-item>
      </el-descriptions>

      <el-descriptions title="设备信息" :column="3" :contentStyle="CS" :label-style="LS">
        <el-descriptions-item
          label="监控设备"
        >{{info.patrolLivestockList[0].equipmentMonitorStatus==1?'正常':'异常'}}</el-descriptions-item>
        <el-descriptions-item label="监控设备说明">{{info.patrolLivestockList[0].equipmentMonitorDesc}}</el-descriptions-item>
      </el-descriptions>

      <el-descriptions title="" :column="3" :contentStyle="CS" :label-style="LS">
        <el-descriptions-item
          label="其它设备"
        >{{info.patrolLivestockList[0].equipmentOtherStatus==1?'正常':'异常'}}</el-descriptions-item>
        <el-descriptions-item label="其他设备说明">{{info.patrolLivestockList[0].equipmentOtherDesc}}</el-descriptions-item>
      </el-descriptions>


      <el-descriptions title="巡检员信息" :column="3" :contentStyle="CS" :label-style="LS">
        <el-descriptions-item label="被监管方签字">
          <el-image
            class="qianmad"
            :src="picPath(info.patrolLivestockList[0].patrolSign)"
            :preview-src-list="srcList"
            @click="bigImage(picPath(info.patrolLivestockList[0].patrolSign))"
          ></el-image>
        </el-descriptions-item>
        <el-descriptions-item label="巡检员">{{info.patrolUserName}}</el-descriptions-item>
        <el-descriptions-item label="巡检员联系电话">{{info.patrolUserPhone}}</el-descriptions-item>
        <el-descriptions-item label="巡检日期">{{info.patrolLivestockList[0]?.patrolTime?.split(' ')[0]}}</el-descriptions-item>
      </el-descriptions>

      <div>
        <el-image
          class="video-img"
          v-for="(item,index) in info.patrolLivestockList[0].patrolImgArr"
          :key="item"
          :src="item"
          fit='fill'
          :preview-src-list="srcList"
          @click="bigImage(item)"
        ></el-image>

        <video
          class="video-img"
          v-for="(item,index) in info.patrolLivestockList[0].patrolVideoArr"
          :key="item"
          :src="item"
          controls="controls"
        ></video>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button type="info" @click="close">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { inspectionInfo } from "@/api/ffs/supervisionSheet/livingSupervisionApi";
import { getFilePath } from "@/utils/east.js";
export default {
  name: "inspectionInfo",
  props: {
    inspectionInfomData: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      CS: {
        "max-width": "300px", //最小宽度
        "word-break": "break-all", //过长时自动换行
      },
      LS: {
        "word-break": "keep-all",
        color: "#000",
      },

      queryParams: { fileName: "", superviseType: "" },
      form: {
        fileName: "",
        datetime: "",
        imagesArr: [
          { url: "dev/2022/09/4a1662446333102.jpg" },
          { url: "dev/2022/09/4a1662446333102.jpg" },
        ],
        videosArr: [{ url: "dev/2022/09/ar1662446361190.mp4" }],
      },

      // 遮罩层
      loading: true,
      // 总条数
      total: 3,
      // 表格数据
      list: [
        {
          fileName: "嚯哈哈",
          orderNum: "13201860552ABCD3",
          mobile: "13201860552",
          money: "30.00",
          createTime: "2022-12-12 10:10:11",
        },
      ],
      srcList: [],
      info: {
        patrolLivestockList: [{ patrolPos: "" }],
      },
      patrolLivestock: {}
    };
  },
  created() {
    this.getDetails();
  },
  computed: {
    patrolStatusName() {
      return (val) => {
        if (val == 0) {
          return "正常";
        } else {
          return "异常";
        }
      };
    },
    // 特殊情况
    formartSpecialType(){
      return (val) => {
        if(!val) return
        const specialTypeMapping = { 1: "被起诉", 2: "死亡", 3: "重大疾病", 4: "意外（车祸、受灾）" , 5: "其他"};
        return val.split(',').map(type => specialTypeMapping[type]).join('、');
      }
    },
    // 贷款期限
    formartLoansDate(){
      return (value) => {
        if(!value) return
        const loanLabels = {
          1: "6个月",
          2: "9个月",
          3: "12个月",
          4: "24个月",
          5: "36个月",
        };
        const label = loanLabels[value] || "其他";
        return label;
      }
      
    },
    // 牧草
    formartGrass(){
      return (val) =>{
        if(!val) return
        const grassMapping = { 6: "玉米秸秆", 7: "青贮", 8: "黄贮", 9: "青干草", 10: "燕麦草", 11: "其它秸秆"};
        return val.split(',').map(type => grassMapping[type]).join('、');
      }
    },
    // 饲料品种
    formartFeedVariety(){
      return (val) =>{
        if(!val) return
        const feedVarietyMapping = { 12: "精补料", 13: "浓缩料", 14: "预混料"};
        return val.split(',').map(type => feedVarietyMapping[type]).join('、');
      }
    },
    formartMaizeCategory(){
      return (val) =>{
        if(!val) return
        const maizeCategoryMapping = { 15: "玉米粒", 16: "玉米面", 17: "玉米压片"};
        return val.split(',').map(type => maizeCategoryMapping[type]).join('、');
      }
    },
    // 农机设备类型
    formartMechanical(){
      return (val) =>{
        if(!val) return
        const mechanicalMapping = { 18: "搅拌机", 19: "撒料车", 20: "打草机", 21: "捆草机", 22: "铲车"};
        return val.split(',').map(type => mechanicalMapping[type]).join('、');
      }
    }
  },
  methods: {
    bigImage(url) {
      this.srcList = [];
      this.srcList.push(url);
    },
    /** 查询巡检记录详情 */
    getDetails(id) {
      inspectionInfo({ ids: [this.inspectionInfomData.patrolId] }).then(
        (res) => {
          this.loading = false;
          if (res.code == 200) {
            res.result.patrolLivestockList[0].patrolImgArr = getFilePath(
              res.result.patrolLivestockList[0].patrolImg
            );
            res.result.patrolLivestockList[0].patrolVideoArr = getFilePath(
              res.result.patrolLivestockList[0].patrolVideo
            );

            console.log("查询巡检记录详情------", res);
            this.info = res.result;
            if(res.result?.patrolLivestockList?.length>0){
              this.patrolLivestock = res.result.patrolLivestockList[0]
            }
          } else {
            this.$message.error(res.message);
          }
        }
      );
    },
    close() {
      this.$emit("close");
    },
  },
};
</script>

<style lang="scss">
:deep(.el-descriptions__header){
  margin-bottom: 10px;
}
.fieldList {
  .el-dialog__header {
    background-color: #f4f4f4;
  }

  .el-dialog__footer {
    text-align: center;
  }

  .selectWidth {
    width: 100%;
  }
}
.head-title {
  font-size: 16px;
  color: #999;
  margin-bottom: 20px;
}
.qihsd {
  color: red;
}
.qianmad {
  height: 50px;
  margin-top: -20px;
}
.mlt20 {
  margin-left: 20px;
}
.f {
  display: flex;
}
.video-img {
  width: 220px;
  height: 180px;
  // padding: 0 5px;
  background: #eee;
  border-radius: 8px;
  overflow: hidden;
  margin-right: 20px;
  margin-bottom: 30px;
  img,video{
    width: 100% !important;
    height: 100% !important;
  }
}
</style>

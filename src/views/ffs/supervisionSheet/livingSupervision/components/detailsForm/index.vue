<template>
  <div>
    <el-dialog
      :title="detailsFormData.title"
      :visible.sync="detailsFormData.open"
      width="1300px"
      :close-on-click-modal="false"
      @close="close"
      class="fieldList"
      :modal="modal"
    >
      <div class="syaddsya">
        <el-tag type="danger" effect="dark">{{superviseStatusName(info.superviseStatus)}}</el-tag>
      </div>
      <el-descriptions title="监管信息" :column="4">
        <el-descriptions-item label="监管单号">{{detailsFormData.superviseId}}</el-descriptions-item>
        <el-descriptions-item label="合同编号">{{info.contractNo}}</el-descriptions-item>
        <el-descriptions-item label="服务费率(%)">{{info.superviseServiceRate}}</el-descriptions-item>
      </el-descriptions>
      <el-descriptions title :column="4" v-show="info.superviseStatus==3">
        <el-descriptions-item label="关闭人">{{info.closeUserName}}</el-descriptions-item>
        <el-descriptions-item label="关闭时间">{{info.closeTime}}</el-descriptions-item>
        <el-descriptions-item label="关闭原因">{{info.closeDesc}}</el-descriptions-item>
        <el-descriptions-item label="硬件是否已拆除">{{info.deviceFlag == 1 ? '是' : '否'}}</el-descriptions-item>
        <el-descriptions-item label="服务费是否已结清">{{info.superviseFeeFlag == 1 ? '是' : '否'}}</el-descriptions-item>


      </el-descriptions>

      <el-row :gutter="20" type="flex" justify="space-between" class="mbt20">
        <el-col :span="5">
          <el-card shadow="hover" class="carbix">
            <div class="carbixtx">授信金额（万）</div>
            <div class="carbixmys">{{info.superviseAmount}}</div>
          </el-card>
        </el-col>
        <el-col :span="5">
          <el-card shadow="hover" class="carbix">
            <div class="carbixtx">待还款金额（万）</div>
            <div class="carbixmys">{{info. superviseBalanceDue}}</div>
          </el-card>
        </el-col>
        <el-col :span="5">
          <el-card shadow="hover" class="carbix">
            <div class="carbixtx">抵押活畜数量（头）</div>
            <div class="carbixmys">{{info.superviseLivestockCount||'--'}}</div>
          </el-card>
        </el-col>
        <el-col :span="5">
          <el-card shadow="hover" class="carbix">
            <div class="carbixtx">抵押活畜保险总额（万元）</div>
            <div class="carbixmys">{{info.superviseInsuranceTotalAmount||'--'}}</div>
          </el-card>
        </el-col>
        <el-col :span="5">
          <el-card shadow="hover" class="carbix">
            <div class="carbixtx">监管周期（月）</div>
            <div class="carbixmys">{{info.superviseLimit||'--'}}</div>
          </el-card>
        </el-col>
      </el-row>

      <div class="msabxs">
        <div>
          <span class="msaitirts">监管时间：</span>
          <span class="msaitimss">{{info.superviseStart}}至{{info.superviseEnd}}</span>
        </div>

        <div>
          <span class="msaitirts">监管员：</span>
          <span class="msaitimss">{{info.supervisorName}}</span>
        </div>
        <div>
          <span class="msaitirts">客户经理：</span>
          <span class="msaitimss">{{info.loanOfficerName}}</span>
        </div>
        <div v-if="info.pastorName">
          <span class="msaitirts">畜牧师：</span>
          <span class="msaitimss">{{info.pastorName}}</span>
        </div>
        <div>
          <span class="msaitirts">创建时间：</span>
          <span class="msaitimss">{{info.createTime}}</span>
        </div>
      </div>

      <div class="head-title">
        <el-tabs v-model="stepsActive" type="card" stretch>
          <el-tab-pane v-for="(tab,index) in tbasList" :key="index" :label="tab"></el-tab-pane>
        </el-tabs>
      </div>
      <basic-info v-show="stepsActive==0" :info="info" />
      <animal-info v-show="stepsActive==1" :info="info" />
      <supervisor-set v-show="stepsActive==2" :info="info" />
      <!-- <video-surveillance v-show="stepsActive==3" :info="info" /> -->
      <daily-inspection v-show="stepsActive==3" :info="info" />
      <emergency-contact v-show="stepsActive==5" :info="info" />
      <inventoryInfo v-show="stepsActive==4" :superviseId="detailsFormData.superviseId"></inventoryInfo>
      <postPoneRecord v-show="stepsActive==6" :superviseId="detailsFormData.superviseId"></postPoneRecord>
      <clockIn v-if="stepsActive==7" :source="false" :applyId="detailsFormData.applyId"></clockIn>
      <paymen-history v-if="stepsActive==8" :info="info" />
      <span slot="footer" class="dialog-footer" v-show="!detailsFormData.disable">
        <el-button type="info" @click="close">取消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { superviseInfo } from "@/api/ffs/supervisionSheet/livingSupervisionApi";
import basicInfo from "./basicInfo"; //基本信息
import animalInfo from "./animalInfo"; //活畜信息
import supervisorSet from "./supervisorSet"; //监管员设置
import videoSurveillance from "./videoSurveillance"; //视频监控
import dailyInspection from "./dailyInspection"; //日常巡检
import emergencyContact from "./emergencyContact"; //紧急联系人
import inventoryInfo from './inventoryInfo.vue';//盘点
import postPoneRecord from './postPoneRecord.vue'
import paymenHistory from "@/views/ffs/mortgage/supervision/components/detailsForm/paymenHistory"; //还款记录
import clockIn from '../../../../../xmb/clockIn/index.vue'  // 每日打卡

import {
  superviseStatus,
} from "@/views/ffs/supervisionSheet/livingSupervision/utils/formatVal.js";
export default {
  name: "detailsFormIndex",
  components: {
    basicInfo,
    animalInfo,
    dailyInspection,
    emergencyContact,
    supervisorSet,
    videoSurveillance,
    inventoryInfo,
    postPoneRecord,
    clockIn,
    paymenHistory,
  },
  props: {
    modal:{
        type:Boolean,
        default:true
    },
    detailsFormData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      tbasList: [
        "基本信息",
        "活畜信息",
        "监管员",
        "日常巡检",
        "盘点记录",
        "紧急联系人",
        '续单记录',
        '每日打卡',
        '还款记录'
      ],
      form: {
        fileName: "",
        datetime: "",
      },
      stepsActive: "0",
      info: {
        bankInfo: {},
      },
    };
  },
  created() {
    this.getInfo();
  },
  computed: {
    superviseStatusName() {
      return (val) => {
        return superviseStatus(val);
      };
    },
  },
  methods: {
    // 查询监管单详情
    getInfo() {
      superviseInfo({ ids: [this.detailsFormData.superviseId] }).then((res) => {
        if (res.code == 200) {
          this.info = res.result || {};
          // this.stepsActive = "8";
          this.$nextTick(() =>{
            this.stepsActive = this.detailsFormData.stepsActive || '0'
            console.log(this.stepsActive)
          })
        }
      });
    },

    close() {
      this.$emit("close");
    },
  },
};
</script>

<style lang="scss">
.fieldList {
  .el-dialog__header {
    background-color: #f4f4f4;
  }

  .el-dialog__footer {
    text-align: center;
  }

  .selectWidth {
    width: 100%;
  }
}
.carbix {
  text-align: center;
}
.carbixtx {
  color: #999;
  margin-bottom: 14px;
}
.carbixmys {
  color: #333;
  font-weight: 600;
}
.msabxs {
  display: flex;
  justify-content: flex-end;
  margin-top: 30px;
  border-bottom: 1px solid #eee;
  padding-bottom: 20px;
}
.msabxs > div {
  margin-left: 30px;
}
.msaitirts {
  color: #333;
}
.msaitimss {
  color: #999;
}
.mbt20 {
  margin-top: 20px;
}
.syaddsya {
  position: absolute;
  right: 20px;
}
</style>

<template>
  <div>
    <el-dialog
      :title="addBaseForm.title"
      :visible.sync="addBaseForm.open"
      width="1300px"
      :close-on-click-modal="false"
      @close="close"
      class="fieldList"
    >
      <el-form :model="form" status-icon :rules="addFormRules" ref="ruleForm" label-width="110px">
        <div class="patit">被监管方</div>
        <el-row :gutter="100">
          <el-col :span="8">
            <el-form-item label="被监管方类型" prop="subjectType">
              <el-radio-group v-model="form.subjectType" @change="radioChange">
                <el-radio :label="1">企业</el-radio>
                <el-radio :label="2">个人</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="会员名称：" prop="applyName" v-if="form.subjectType==2">
              <el-select
                v-model="form.applyName"
                filterable
                remote
                reserve-keyword
                placeholder="请输入被监管方名称/手机号码"
                :remote-method="remoteMethod"
                class="selectWidth"
                @change="onChange"
              >
                <el-option
                  v-for="item in selectList"
                  :key="item.userId"
                  :label="labelName(item)"
                  :value="item.userId"
                ></el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="企业名称：" prop="applyName" v-if="form.subjectType==1">
              <el-select
                v-model="form.applyName"
                filterable
                remote
                reserve-keyword
                placeholder="请输入被监管方名称"
                :remote-method="supervisorEnterMethod"
                class="selectWidth"
                @change="onChangeSupervisorEnter"
              >
                <el-option
                  v-for="(item,index) in supervisorEnterList"
                  :key="item.tenantId"
                  :label="item.companyName"
                  :value="item.tenantId"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="form.subjectType">
            <el-form-item label="联系电话：" prop="applyPhone">
              <!-- <el-input
                v-model="form.applyPhone"
                clearable
                placeholder="请输入被监管方联系电话"
                :readonly="addBaseForm.superviseType==1"
                type="number"
                oninput="if(value.length>11)value=value.slice(0,11)"
              ></el-input>-->

              <el-input
                v-model="form.applyPhone"
                clearable
                placeholder="请输入被监管方联系电话"
                type="number"
                oninput="if(value.length>11)value=value.slice(0,11)"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24" v-if="supervisorEnterInfo.tenantId&&form.subjectType==1">
            <div style="margin-left:30px">
              <el-descriptions :column="3">
                <el-descriptions-item
                  label="企业类型"
                >{{companyTypeName(supervisorEnterInfo.companyType)}}</el-descriptions-item>
                <el-descriptions-item label="营业执照编号">{{supervisorEnterInfo.businessLicenseNo}}</el-descriptions-item>
                <el-descriptions-item
                  label="地址"
                >{{supervisorEnterInfo.provinceName}}{{supervisorEnterInfo.cityName}}{{supervisorEnterInfo.countyName}}{{supervisorEnterInfo.detailAddress}}</el-descriptions-item>
                <el-descriptions-item label="法人">{{supervisorEnterInfo.realName}}</el-descriptions-item>
                <el-descriptions-item label="法人电话">{{supervisorEnterInfo.phoneNumber}}</el-descriptions-item>
                <el-descriptions-item label="身份证号">{{supervisorEnterInfo.idCard}}</el-descriptions-item>
                <el-descriptions-item
                  label="负责人姓名"
                  v-if="supervisorEnterInfo.corprateName"
                >{{supervisorEnterInfo.corprateName}}</el-descriptions-item>
                <el-descriptions-item
                  label="负责人电话"
                  v-if="supervisorEnterInfo.adminPhonenumber"
                >{{supervisorEnterInfo.adminPhonenumber}}</el-descriptions-item>
              </el-descriptions>
            </div>
          </el-col>
        </el-row>

        <div class="patit">委托方信息</div>
        <el-row :gutter="100">
          <el-col :span="8">
            <el-form-item label="企业名称：" prop="bankName">
              <el-select
                v-model="form.bankName"
                filterable
                remote
                reserve-keyword
                placeholder="请输入企业名称"
                :remote-method="remoteEnterprise"
                class="selectWidth"
                @change="onChangeEnterprise"
              >
                <el-option
                  v-for="item in enterpriseList"
                  :key="item.tenantId"
                  :label="item.companyName"
                  :value="item.tenantId"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24" v-if="form.bankName">
            <div style="margin-left:30px">
              <el-descriptions :column="3">
                <el-descriptions-item label="企业类型">银行</el-descriptions-item>
                <el-descriptions-item label="营业执照编号">{{enterpriseInfo.businessLicenseNo}}</el-descriptions-item>
                <el-descriptions-item
                  label="地址"
                >{{enterpriseInfo.provinceName}}{{enterpriseInfo.cityName}}{{enterpriseInfo.countyName}}{{enterpriseInfo.detailAddress}}</el-descriptions-item>
                <el-descriptions-item label="法人">{{enterpriseInfo.realName}}</el-descriptions-item>
                <el-descriptions-item label="法人电话">{{enterpriseInfo.phoneNumber}}</el-descriptions-item>
                <el-descriptions-item label="身份证号">{{enterpriseInfo.idCard}}</el-descriptions-item>
                <el-descriptions-item
                  label="负责人姓名"
                  v-if="enterpriseInfo.corprateName"
                >{{enterpriseInfo.corprateName}}</el-descriptions-item>
              </el-descriptions>
            </div>
          </el-col>
        </el-row>
        <div class>
          <span class="patit">调研信息</span>
          <span class="qiangti">(大小不超过100MB 格式为 doc/pdf/png/jpg的文件)</span>
        </div>

        <el-row class="filerow">
          <el-col :span="8">
            <el-form-item label="意向单：" prop="fileIntentionList">
              <file-upload
                :fileType="['doc', 'pdf','png', 'jpg', 'jpeg']"
                :fileSize="100"
                :limit="1"
                v-model="form.fileIntentionList"
                :isShowTip="false"
              >
                <el-button type="primary" plain size="mini" class="lobtb">
                  上传意向单
                  <i class="el-icon-upload loadicon"></i>
                </el-button>
              </file-upload>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="调研表：" prop="fileCreditInvestigation">
              <file-upload
                :fileType="['doc', 'pdf','png', 'jpg', 'jpeg']"
                :fileSize="100"
                :limit="1"
                v-model="form.fileCreditInvestigation"
                :isShowTip="false"
              >
                <el-button type="primary" plain size="mini" class="lobtb">
                  上传调研表
                  <i class="el-icon-upload loadicon"></i>
                </el-button>
              </file-upload>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="监管方案：" prop="regulatoryScheme">
              <file-upload
                :fileType="['doc', 'pdf','png', 'jpg', 'jpeg']"
                :fileSize="100"
                :limit="1"
                v-model="form.regulatoryScheme"
                :isShowTip="false"
              >
                <el-button type="primary" plain size="mini" class="lobtb">
                  上传监管方案
                  <i class="el-icon-upload loadicon"></i>
                </el-button>
              </file-upload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <span slot="footer" class="dialog-footer" v-show="!addBaseForm.disable">
        <el-button @click="close">取消</el-button>
        <el-button type="primary" @click="submitForm">{{addBaseForm.btnTxt}}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  getPstureByApplyPhone,
  pstureModel,
} from "@/views/ffs/supervisionSheet/livingSupervision/utils/hasPasture.js";
import { searchUser } from "@/api/system/user.js"; // 查询搜索用户  可以手机号、昵称等等信息搜索
import { listEnterprise } from "@/api/ffs/supervisionSheet/livingSupervisionApi.js"; // 搜索企业
export default {
  name: "tablelFormAddModelForm",
  props: {
    addBaseForm: {
      type: Object,
      default: {},
    },
  },
  data() {
    const isMobile = (rule, value, callback) => {
      if (!/^1[3456789]\d{9}$/.test(value)) {
        callback(new Error("请输入正确有效的手机号"));
      } else {
        callback();
      }
    };

    return {
      selectList: [],
      enterpriseList: [],
      supervisorEnterList: [],
      supervisorEnterInfo: {},
      enterpriseInfo: {},
      form: {
        bankId: "",
        bankName: "",
        loanOfficerName: "",
        loanOfficerPhone: "",

        subjectType: "", //被监管方类型：1企业，2个人

        applyPhone: "", //被监管方手机号
        applyName: "", //被监管方名称
        subjectUserId: "",

        fileIntentionList: "", //意向表附件
        fileCreditInvestigation: "", //调研表附件
        regulatoryScheme: "", //监管方案附件

        operation: "add",
        creditInvestigationId: "",
        intentionListId: "",
      },
      addFormRules: {
        applyPhone: [
          {
            required: true,
            message: "被监管方联系电话不能为空",
            trigger: "blur",
          },
          {
            validator: isMobile,
            message: "请输入正确有效的手机号",
            trigger: "blur",
          },
        ],
        applyName: [
          {
            required: true,
            message: "被监管方会员/企业名称不能为空",
            trigger: "blur",
          },
        ],
        subjectType: [
          {
            required: true,
            message: "被监管方类型不能为空",
            trigger: "blur",
          },
        ],
        bankName: [
          {
            required: true,
            message: "企业名称不能为空",
            trigger: "blur",
          },
        ],
        fileIntentionList: [
          {
            required: false,
            message: "意向表附件不能为空",
            trigger: "blur",
          },
        ],
        fileCreditInvestigation: [
          {
            required: false,
            message: "调研表附件不能为空",
            trigger: "blur",
          },
        ],

        regulatoryScheme: [
          {
            required: false,
            message: "监管方案附件不能为空",
            trigger: "blur",
          },
        ],
      },
    };
  },
  created() {},
  computed: {
    labelName() {
      return (val) => {
        if (!val) return;
        if (val.corprateName) return val.corprateName;
        if (val.nickName) return val.nickName;
        if (val.userName) return val.userName;
        if (val.phonenumber) return val.phonenumber;
      };
    },
    companyTypeName() {
      return (val) => {
        if (val == 1) {
          return "养殖企业";
        } else if (val == 2) {
          return "屠宰企业";
        } else if (val == 3) {
          return "银行";
        } else if (val == 4) {
          return "保险公司";
        } else if (val == 5) {
          return "行业协会";
        } else if (val == 6) {
          return "物流公司";
        } else if (val == 7) {
          return "个体工商户";
        } else if (val == 8) {
          return "合作社";
        }
      };
    },
  },
  methods: {
    //选择被监管方类型
    radioChange() {
      this.form.applyPhone = "";
      this.form.applyName = "";
      this.supervisorEnterInfo = {};
      this.selectList = [];
      this.enterpriseList = [];
    },
    // 确定选择被监管方--个人
    async onChange(val) {
      const selectVal = this.selectList.find((item) => item.userId == val);
      this.form.applyPhone = selectVal.phonenumber; //被监管方名称电话
      this.form.subjectUserId = selectVal.userId;
      this.form.applyName = selectVal.corprateName||selectVal.nickName || selectVal.userName||selectVal.phonenumber;
      if (!val) {
        if (this.addBaseForm.superviseType == 1) {
          await pstureModel(this, "", this.selectList[0].nickName);
          return;
        }
      }

      if (this.addBaseForm.superviseType == 1) {
        await getPstureByApplyPhone(
          this,
          selectVal.phonenumber,
          selectVal.userId,
          this.form.applyName
        )
      }
    },

    // 确定选择被监管方--企业
    async onChangeSupervisorEnter(val) {
      const selectVal = this.supervisorEnterList.find(
        (item) => item.tenantId == val
      );
      if (selectVal.tenantId) {
        this.supervisorEnterInfo = selectVal;
      } else {
        this.supervisorEnterInfo = {};
      }
      this.form.applyPhone = selectVal.phoneNumber || ""; //被监管方名称电话
      this.form.subjectUserId = selectVal.tenantId || "";
      this.form.applyName = selectVal.companyName;

      if (!val) {
        if (this.addBaseForm.superviseType == 1) {
          await pstureModel(this, "", this.supervisorEnterList[0].companyName);
          return;
        }
      }

      if (this.addBaseForm.superviseType == 1) {
        await getPstureByApplyPhone(
          this,
          selectVal.phonenumber,
          selectVal.tenantId,
          this.form.applyName
        )
      }
    },

    // 确定选择委托方
    onChangeEnterprise(val) {
      const selectVal = this.enterpriseList.find(
        (item) => item.tenantId == val
      );
      this.enterpriseInfo = selectVal;

      this.form.bankId = selectVal.tenantId;
      this.form.bankName = selectVal.companyName;
      this.form.loanOfficerName = selectVal.corprateName || "";
      this.form.loanOfficerPhone = selectVal.adminPhonenumber || "";
    },

    //远程搜索被监管方---个人
    remoteMethod(data) {
      if (this.form.subjectType == "") {
        this.$message({
          message: "请先选被监管方类型",
          type: "error",
        });
        this.form.applyPhone = "";
        return;
      }
      searchUser({ phonenumber: data }).then(async (res) => {
        if (res.code == 200) {
          const result = res.result || [];
          if (result.length == 0&&this.addBaseForm.superviseType == 2) {
            result.push({
              applyPhone: "",
              subjectUserId: "",
              nickName: data,
            });

            // if (this.addBaseForm.superviseType == 2) {
            //   result.push({
            //     applyPhone: "",
            //     subjectUserId: "",
            //     nickName: data,
            //   });
            // }

            // if (this.addBaseForm.superviseType == 1) {
            //   this.$message({
            //     message: `被监管方 【${data}】在平台不存在`,
            //     type: "error",
            //   });
            //   return;
            // }
          }
          this.selectList = result;
        }
      });
    },

    //远程搜索被监管方---企业
    supervisorEnterMethod(data) {
      if (this.form.subjectType == "") {
        this.$message({
          message: "请先选被监管方类型",
          type: "error",
        });
        this.form.applyPhone = "";
        return;
      }
      listEnterprise({
        companyName: data,
        approveStatus: 1,
        companyTypeList: [1, 2, 5, 6, 7, 8, 9, 10 ,12 ,100],
      }).then(async (res) => {
        if (res.code == 200) {
          const result = res.result.list || [];
          if (result.length == 0&&this.addBaseForm.superviseType == 2) {
            result.push({
              phoneNumber: "",
              companyName: data,
            });
          }
          this.supervisorEnterList = result;
        }
      });
    },
    handelRange(){
        let listString=this.$store.state.user.user.areaDataRange
        let rangeData=[]
        let areaDataRange =listString.split(';')
        areaDataRange.map(item=>{
            let newArr= item.split(",")
            rangeData.push(newArr.slice(1))
        })
        return rangeData.join(";")
    },

    //远程搜索委托方信息    listString: 
    remoteEnterprise(data) {
        let areaDataRange=this.handelRange()
      listEnterprise({
        companyTypeList: [3, 4],
        companyName: data,
        approveStatus: 1,
        params:{
                areaDataRange:areaDataRange
            }

      }).then((res) => {
        if (res.code == 200) {
          this.enterpriseList = res.result.list || [];
        }
      });
    },
    //关闭弹框
    close() {
      this.$emit("closeAddBaseForm");
    },

    /** 提交按钮 */
    submitForm() {
      this.$refs["ruleForm"].validate(async (valid) => {
        if (valid) {
          if (this.addBaseForm.superviseType == 1) {
            const psture = await getPstureByApplyPhone(
              this,
              this.form.applyPhone,
              this.form.subjectUserId,
              this.form.applyName
            );
            if (psture == "") return;
          }
          this.$emit("addSupervision", this.form);
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.fieldList {
  .el-dialog__header {
    background-color: #f4f4f4;
  }

  .el-dialog__footer {
    text-align: center;
  }

  .selectWidth {
    width: 100%;
  }
}
.head-title {
  font-size: 16px;
  color: #999;
  margin-bottom: 20px;
}
.head-title span {
  color: red;
}
.patit {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 20px;
}
.qiangti {
  color: rgb(218, 96, 96);
  font-size: 12px;
}
.loadicon {
  font-size: 18px;
  margin-left: 10px;
  vertical-align: text-bottom;
}
</style>

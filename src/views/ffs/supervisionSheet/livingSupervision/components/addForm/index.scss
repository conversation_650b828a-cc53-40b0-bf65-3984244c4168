.setmain .mtop {
  margin-top: 30px;
}

.setmain .loadpc {
  margin: 0 30px;
}

.setmain .wbrdw {
  text-decoration: underline;
  color: rgb(59, 59, 206);
  cursor: pointer;
}

.el-icon-folder {
  color: #fc922b;
  font-size: 20px;
  margin-right: 5px;
}

.fc {
  display: flex;
  align-items: center;
}

.marrt20 {
  margin-right: 10px;
}

.mylabew {
  width: 115px;
}

.latipt {
  border: 1px solid #ddd;
  border-radius: 2px;
  height: 32px;
  background: #eee;
  margin-right: 5px;
  line-height: 32px;
  padding: 0 10px;
}

.iputiirt {
  font-size: 14px;
  color: #303133;
  font-weight: bold;
  margin-bottom: 30px;
}

.inputWidth {
  width: 100% !important;
}

.qiangti {
  color: rgb(218, 96, 96);
  font-size: 12px;
}

.smidf {
  font-size: 14px;
  color: #303133;
  font-weight: bold;
}

.smidfmid {
  margin-top: 10px;
  margin-bottom: 5px;
}
.tablebew {
  margin-top: 20px;
  text-align: right;
  line-height: 30px;
  margin-bottom: 10px;
}
.smidfieym {
  border-bottom: 6px solid #eee;
  margin-bottom: 30px;
}
.tablebewtosa {
  text-align: center;
  font-weight: bold;
  font-size: 16px;
}
.skeme {
  margin-top: 50px;
}

.nkfdj {
  margin-bottom: 10px;
}

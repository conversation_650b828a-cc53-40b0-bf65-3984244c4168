<template>
  <div>
    <el-dialog
      :title="dialogAdd.title"
      :visible.sync="dialogAdd.open"
      width="600px"
      :close-on-click-modal="false"
      @close="close"
      class="fieldList"
      append-to-body
    >
      <el-form
        :model="form"
        :rules="rules"
        ref="ruleForm"
        label-width="135px"
        class="demo-ruleForm"
      >
        <el-form-item label="圈舍名称" prop="penName">
          <el-input v-model="form.penName" placeholder="请输入圈舍名称" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="close">关闭</el-button>
        <el-button type="primary" @click="submitForm">提交</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { selectPasturePenInfo, addPasturePen, editPasturePen } from "@/api/ffs/supervisionSheet/siteManagement";
export default {
  props: {
    dialogAdd: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      form: {
        penName: ''
      },
      supplierName: "",
      options: [],
      optionsList:[],
      rules: {
        penName: [
          {
            required: true,
            message: "请填写圈舍名称",
            trigger: "blur",
          },
        ],
      },
    };
  },
  created() {
    this.form.pastureId = this.$route.query.id;
    this.getInfo();
  },

  methods: {
    getInfo() {
      console.log('getInfo')
      if (!this.dialogAdd.id) {
        return;
      } else {
        // 查询圈舍，回显编辑
        selectPasturePenInfo({penId:this.dialogAdd.id}).then(res=>{
          if(res.code===200){
            console.log(res)
            this.form=res.result
          }
        })
      }
    },
    close() {
      this.$emit("close");
    },
    //修改信息
    updataInfo() {
      editPasturePen(this.form).then((res) => {
        if (res.code == 200) {
          this.$message({
            type: "success",
            message: "修改成功",
          });
          this.$emit("refresh");
          this.close();
        }
      });
    },
    //添加信息
    addInfo() {
      addPasturePen(this.form).then((res) => {
        if (res.code == 200) {
          this.$message({
            type: "success",
            message: "添加成功",
          });
          this.$emit("refresh");
          this.close();
        }
      });
    },
    submitForm() {
      this.$refs["ruleForm"].validate(async (valid) => {
        if (!valid) {
          return;
        }
        if (this.dialogAdd.id) {
          this.updataInfo();
        } else {
          this.addInfo();
        }
      });
    },
  },
};
</script>

<style lang="scss">
.fieldList {
  .el-dialog__header {
    background-color: #f4f4f4;
  }
  .el-dialog__footer {
    text-align: center;
  }
  .selectWidth {
    width: 100%;
  }
}
</style>

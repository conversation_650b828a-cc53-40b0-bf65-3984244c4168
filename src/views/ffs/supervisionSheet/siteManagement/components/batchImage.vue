<template>
  <div>
    <el-dialog
      :title="batch.title"
      :visible.sync="batch.open"
      width="600px"
      :close-on-click-modal="false"
      @close="refuse"
      class="bachImage"
      append-to-body
    >
      <div class="box">
        <div class="bachImage-box">
            <div class="warning f">
        <i class="el-icon-warning" style="color:red;font-size: 18px; padding-right: 5px;"></i>
        <span
          v-show="batch.type==1"
        >上传之前，请您将图片的名称重命名为对应活畜的耳标编号，系统会根据耳标编号将图片进行上传绑定。您可以选择多张图片同时上传。如果您上传的图片名称在该养殖场找不到对应耳标，系统不会做任何处理。</span>
        <span v-show="batch.type==2">请先使用导出活畜功能下载当前养殖场的活畜信息，完善信息之后，通过此功能上传即可。</span>
      </div>
          <div>
            <div class="f" v-if="batch.type==1">
              <obsFileUpload v-model="form.batchUrl" :fileType="fileTypeImage"></obsFileUpload>
            </div>
            <div class="f" v-if="batch.type==2">
              <el-upload
              class="bachImageupload"
                action="hh"
                :http-request="uploadPost"
                :before-upload="handleBeforeUpload"
                :limit="1"
                :show-file-list="false"
                :file-list="fileList"
              >
                <div class="fc upload-box">
                  <i class="el-icon-upload"></i>
                  <span class="el-icon-txt">选择文件</span>
                </div>
              </el-upload>
            </div>
          </div>
    
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="refuse">关闭</el-button>
        <el-button type="primary" @click="submitForm" v-if="batch.type==1">提交</el-button>
      </span>
    </el-dialog>
  </div>
</template>
 <script>
import {
  batchCompleteLivestock,
  batchModel,
} from "@/api/ffs/supervisionSheet/siteManagement.js";
import obsFileUpload from "@/components/obsFileUpload/index.vue";
import { uploadFile } from "@/utils/request";
export default {
  components: { obsFileUpload },
  props: {
    batch: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      fileTypeImage: ["png", "jpg", "jpeg", "jf"],
      fileType: ["xlsx", "xls"],
      fileList: [],
      form: {
        batchUrl: [],
      },
    };
  },
  mounted() {},
  methods: {
    submitForm() {
      this.batchSubmit();
    },

    batchSubmit() {
      if (this.form.batchUrl.length == 0) {
        that.$message.error("请选择图片");
        return;
      } else {
        let livestockList = [];
        this.form.batchUrl.forEach((item) => {
          const index = livestockList.findIndex(
            (item1) =>
              item1.earTagNo === item.name.slice(0, item.name.indexOf("."))
          );
          if (index > -1) {
            livestockList[index].livestockImage =
              livestockList[index].livestockImage + "," + item.url;
          } else {
            livestockList.push({
              earTagNo: item.name.slice(0, item.name.indexOf(".")),
              livestockImage: item.url,
            });
          }
        });
        let obj = {
          pastureId: this.$route.query.pastureId,
          livestockList: livestockList,
        };
        batchCompleteLivestock(obj).then((res) => {
          if (res.code == 200) {
            console.log("22");
            this.$message({
              type: "success",
              message: "批量上传成功",
            });
            this.$emit("refresh");
            this.refuse();
          }
        });
      }
    },

    async uploadPost(item) {
      let formData = new FormData();
      formData.append("file", item.file);
      formData.append("pastureId", this.$route.query.pastureId);
      let loadUrl = batchModel();

      uploadFile(loadUrl, formData)
        .then((res) => {
          this.$emit("refresh");
          this.refuse();

          this.$modal.closeLoading();
        })
        .catch((error) => {
          this.fileList = [];
          this.$modal.closeLoading();
        });
    },

    // 上传前校检格式和大小
    handleBeforeUpload(file) {
      // 校检文件类型
      if (this.fileType) {
        let fileExtension = "";
        if (file.name.lastIndexOf(".") > -1) {
          fileExtension = file.name.slice(file.name.lastIndexOf(".") + 1);
        }
        const isTypeOk = this.fileType.some((type) => {
          if (file.type.indexOf(type) > -1) return true;
          if (fileExtension && fileExtension.indexOf(type) > -1) return true;
          return false;
        });
        if (!isTypeOk) {
          this.$modal.msgError(
            `文件格式不正确, 请上传${this.fileType.join("/")}格式文件!`
          );
          return false;
        }
      }
      this.$modal.loading("正在上传文件，请稍候...");
      return true;
    },
    refuse() {
      this.$emit("close");
    },
  },
};
</script>
  
<style lang="scss">
.bachImage {
  .el-dialog__header {
    background-color: #f4f4f4;
  }
  .el-dialog__footer {
    text-align: center;
  }
  .el-dialog__body {
    padding: 15px 15px;
  }

  &-info {
    background: #f4f4f4;
    width: 540px;
    margin: 0 0 22px 120px;
    border-radius: 8px;
    padding: 15px 20px;
    &-row {
      padding: 5px 0;
    }
  }
  .f {
    display: flex;
    justify-content: center;
  }
  .warning {
    margin-bottom: 10px;
    text-align: justify;
  }
  .upload-box {
    width: 100%;
    border: 1px dashed #e4e7ed;
    border-radius: 10px;
    padding: 30px;
  }
  .upload-box:hover {
    border: 1px dashed #1890ff;
  }
  .el-icon-upload {
    font-size: 50px;
    color: #1890ff;
  }
  .el-icon-upload {
    margin: 0 !important;
  }
  .el-upload-dragger {
    width: 400px;
    height: 100px;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  .el-icon-txt {
    display: block;
    font-size: 16px;
  }
  .box {
    max-height: 400px;
    overflow: auto;
  }
  .el-upload{
    width: 100%;
  }
  .bachImageupload {
   width: 400px;
   margin-top: 10px;
  }
  .fc{
    display: flex;
    flex-direction: column;
  }
}
</style>
  
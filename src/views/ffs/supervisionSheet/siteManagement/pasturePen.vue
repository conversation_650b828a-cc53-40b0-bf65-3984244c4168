<template>
  <div class="app-container">
    <el-card class="mb10 form_box" shadow="never" ref="formBox">
      <el-button  class="mb8" type="success" size="mini" icon="el-icon-arrow-left" @click="goback">返回</el-button>
<!--      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="140px">-->
<!--        <el-row :gutter="10" class="mb8">-->
<!--          <el-form-item label="圈舍名称" prop="gatewayName">-->
<!--            <el-input v-model="queryParams.gatewayName" placeholder="请输入圈舍名称" clearable />-->
<!--          </el-form-item>-->
<!--          <el-form-item>-->
<!--            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>-->
<!--            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>-->
<!--          </el-form-item>-->
<!--        </el-row>-->
<!--      </el-form>-->
    </el-card>
    <el-card shadow="never" class="list_table">
      <el-row class="mb8 form_btn">
        <el-col class="form_btn_col">
          <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="addFrom">新增</el-button>
        </el-col>
      </el-row>
      <!-- 表格数据 -->
      <el-table :data="tableData" stripe style="width: 100%" v-loading="loading" :height="tableHeight" border>
        <el-table-column type="index" label="序号" width="100" align="center"></el-table-column>
        <el-table-column prop="penName" label="圈舍名称" align="center" />
        <el-table-column label="操作" fixed="right" width="140" align="center">
          <template slot-scope="scope">
            <el-button class="btn_color_t" @click="handelEdit(scope.row.penId)" icon="el-icon-edit" size="mini" type="text">编辑</el-button>
            <el-button class="btn_color_f" @click="handeldel(scope.row.penId)" icon="el-icon-delete" size="mini" type="text">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <addPen v-if="dialogAdd.open" :dialogAdd="dialogAdd" @close="close" @refresh="refreshList"></addPen>
  </div>
</template>
<script>
import { selectPasturePenList, deletePasturePen } from "@/api/ffs/supervisionSheet/siteManagement";
import addPen from "./components/addPen.vue";
import { tableUi } from "@/utils/mixin/tableUi.js";
export default {
  components: {
    addPen,
  },
  mixins: [tableUi],
  data() {
    return {
      dialogAdd: {
        open: false,
        title: "",
      },
      queryParams: {
        gatewayName:'',
        pageNum: 1,
        pageSize: 10,
      },
      loading: true,
      tableData: [],
    };
  },
  computed: {
    handelColor (){
      return (value)=>{
        return value==1?'rgb(18, 174, 99)':'#9DA1A8'
      }
    }
  },
  created() {
    this.queryParams.pastureId = this.$route.query.id;
    this.getList();
  },

  methods: {
    //列表查询
    getList() {
      selectPasturePenList(this.queryParams).then((res) => {
        if (res.code === 200) {
          this.tableData = res.result;
          this.loading = false;
        }
      });
    },

    //页面返回
    goback() {
      this.$tab.closeOpenPage();
      this.$router.go(-1);
    },
    reset() {
      this.resetForm("queryForm");
    },
    close() {
      this.dialogAdd.open=false
      this.dialogAdd.id=''
    },
    //重置
    resetQuery() {
      this.reset();
      this.handleQuery();
    },
    //刷新页面
    refreshList() {
      this.getList();
    },
    //搜索
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    addFrom() {
      this.dialogAdd.open = true;
      this.dialogAdd.title = "新增";
    },
    //编辑数据
    handelEdit(id) {
      console.log(id)
      this.dialogAdd.open = true;
      this.dialogAdd.title = "编辑";
      this.dialogAdd.id=id
    },

    //删除
    handeldel(id) {
      this.$confirm("此操作将永久删除, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          deletePasturePen({ penId: id }).then((res) => {
            if (res.code === 200) {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              this.getList();
            }
          });
        })
    },
  },
};
</script>

<style lang="scss" scoped>
</style>


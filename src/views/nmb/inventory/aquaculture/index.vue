<template>
    <div class="app-container">
        <el-card class="mb10 form_box" shadow="never" ref="formBox">
            <el-row :gutter="10" class="mb8">
                <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
                    <el-row class="form_row">
                        <el-col class="form_col">
                            <el-form-item label="姓名：" prop="nickName">
                                <el-input v-model="queryParams.nickName" placeholder="请输入姓名" clearable />
                            </el-form-item>
                            <el-form-item label="联系电话：" prop="phonenumber">
                                <el-input v-model="queryParams.phonenumber" placeholder="请输入联系电话" clearable />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col>
                            <el-form-item>
                                <el-button type="primary" icon="el-icon-search" size="mini"
                                    @click="handleQuery">搜索</el-button>
                                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </el-row>
        </el-card>
        <el-card shadow="never">
            <el-row class="mb8 form_btn">
                <el-col class="form_btn_col">
                    <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="addFrom">新增</el-button>
                </el-col>
            </el-row>
            <el-table :data="tableData" border v-loading="loading" style="width: 100%" height="300" v-tableHeight="{ bottomOffset: 69 }">
                <el-table-column type="index" align="center" label="序号" fixed="left" width="50"></el-table-column>
                <el-table-column prop="nickName" label="姓名" min-width="120" align="center" />
                <el-table-column prop="phonenumber" label="联系电话" width="150" align="center"></el-table-column>
                <el-table-column prop="breederTypeName" label="角色" width="120" align="center"></el-table-column>
                <el-table-column prop="remark" label="职责" width="200" align="center"></el-table-column>
                <el-table-column prop="todayRecordNum" label="今日操作数量" width="120" align="center"></el-table-column>
                <el-table-column label="操作" width="200" align="center">
                    <template slot-scope="scope">
                        <el-button @click="goEdit(scope.row)" icon="el-icon-edit" size="mini"
                            type="text">编辑</el-button>
                        <el-button class="btn_color_four" @click="goOperationRecord(scope.row)" icon="el-icon-document"
                            size="mini" type="text">操作记录</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
                :limit.sync="queryParams.pageSize" @pagination="getList" />
        </el-card>

        <!-- 新增/编辑弹窗 -->
        <breeder-form
            v-if="dialogAdd.open"
            :dialog-data="dialogAdd"
            @close="close"
            @refresh="refresh"
        />

        <!-- 操作记录弹窗 -->
        <operation-record
            v-if="operationDialog.open"
            :dialog-data="operationDialog"
            @close="closeOperationDialog"
        />
    </div>
</template>

<script>
import { tableUi } from "@/utils/mixin/tableUi.js";
import BreederForm from "./components/BreederForm.vue";
import OperationRecord from "./components/OperationRecord.vue";

export default {
    mixins: [tableUi],
    components: {
        BreederForm,
        OperationRecord
    },
    data() {
        return {
            //新增/编辑弹窗
            dialogAdd: {
                open: false,
                title: "",
                id: null,
            },
            //操作记录弹窗
            operationDialog: {
                open: false,
                title: "操作记录",
                breederId: null,
                breederName: ""
            },
            queryParams: {
                nickName: '',
                phonenumber: '',
                pageNum: 1,
                pageSize: 20,
            },
            loading: true,
            total: 0,
            tableData: []
        };
    },
    created() {
        this.getList();
    },

    methods: {
        //列表查询
        getList() {
            this.loading = true;
            const { breederList } = require("@/api/nmb/inventory/index.js");
            breederList(this.queryParams).then((res) => {
                if (res.code == 200) {
                    this.tableData = res.result.list || [];
                    this.total = Number(res.result.total || 0);
                    this.loading = false;
                } else {
                    this.$message.error(res.message || '获取数据失败');
                    this.loading = false;
                }
            }).catch(() => {
                this.loading = false;
            });
        },

        //关闭弹窗
        close() {
            this.dialogAdd.open = false;
            this.dialogAdd.id = null;
        },

        //关闭操作记录弹窗
        closeOperationDialog() {
            this.operationDialog.open = false;
            this.operationDialog.breederId = null;
            this.operationDialog.breederName = "";
        },

        //新增人员
        addFrom() {
            this.dialogAdd.open = true;
            this.dialogAdd.title = "新增养殖人员";
            this.dialogAdd.id = null;
        },

        //编辑人员
        goEdit(row) {
            this.dialogAdd.open = true;
            this.dialogAdd.title = "编辑养殖人员";
            this.dialogAdd.id = row.xmbBreederId;
        },

        //查看操作记录
        goOperationRecord(row) {
            this.operationDialog.open = true;
            this.operationDialog.breederId = row.xmbBreederId;
            this.operationDialog.breederName = row.nickName;
        },
        //重置查询
        resetQuery() {
            this.resetForm("queryForm");
            this.handleQuery();
        },

        //刷新页面
        refresh() {
            this.getList();
        },

        //搜索
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
    },
};
</script>

<style lang="scss" scoped>
.el-col-12 {
    display: flex;
    align-items: center;
    margin: 10px 0;
}
</style>

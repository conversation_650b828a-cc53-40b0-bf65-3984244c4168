<template>
    <div>
        <el-dialog
            :title="dialogData.title"
            :visible.sync="dialogData.open"
            width="600px"
            :close-on-click-modal="false"
            @close="close"
            append-to-body
        >
            <el-form
                :model="form"
                :rules="rules"
                ref="ruleForm"
                label-width="120px"
                class="demo-ruleForm"
                v-loading="detailLoading"
                element-loading-text="加载详情中..."
            >
                <div class="form-section">
                    <div class="section-title">
                        <span class="section-icon"></span>
                        人员信息
                    </div>
                    
                    <el-form-item label="姓名：" prop="nickName">
                        <el-input v-model="form.nickName" placeholder="请输入姓名" />
                    </el-form-item>
                    
                    <el-form-item label="岗位：" prop="breederType">
                        <el-select v-model="form.breederType" placeholder="请选择岗位" style="width: 100%">
                            <el-option label="饲养组长" :value="1"></el-option>
                            <el-option label="兽医" :value="2"></el-option>
                        </el-select>
                    </el-form-item>
                    
                    <el-form-item label="联系电话：" prop="phonenumber">
                        <el-input v-model="form.phonenumber" placeholder="请输入联系电话" />
                    </el-form-item>
                    
                    <el-form-item label="职责：" prop="remark">
                        <el-input 
                            type="textarea" 
                            v-model="form.remark" 
                            placeholder="请输入职责描述"
                            :rows="4"
                        />
                    </el-form-item>
                </div>
            </el-form>
            
            <span slot="footer" class="dialog-footer">
                <el-button @click="close">取 消</el-button>
                <el-button type="primary" @click="submitForm" :loading="submitLoading">确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import { breederAdd, breederEdit, breederInfo } from "@/api/nmb/inventory/index.js";

export default {
    props: {
        dialogData: {
            type: Object,
            required: true
        }
    },
    data() {
        return {
            form: {
                nickName: '',
                breederType: '',
                phonenumber: '',
                remark: ''
            },
            rules: {
                nickName: [
                    { required: true, message: '请输入姓名', trigger: 'blur' }
                ],
                breederType: [
                    { required: true, message: '请选择岗位', trigger: 'change' }
                ],
                phonenumber: [
                    { required: true, message: '请输入联系电话', trigger: 'blur' },
                    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
                ],
                remark: [
                    { required: true, message: '请输入职责描述', trigger: 'blur' }
                ]
            },
            submitLoading: false,
            detailLoading: false
        };
    },
    watch: {
        'dialogData.open'(val) {
            if (val) {
                console.log('弹窗打开，dialogData:', this.dialogData); // 调试信息
                this.initForm();
            }
        },
        'dialogData.id'(val) {
            console.log('ID变化:', val); // 调试信息
        }
    },
    methods: {
        // 初始化表单
        initForm() {
            if (this.dialogData.id) {
                // 编辑模式，获取详情
                this.getBreederInfo();
            } else {
                // 新增模式，重置表单
                this.resetForm();
            }
        },
        
        // 获取人员详情
        getBreederInfo() {
            this.detailLoading = true;
            breederInfo({ xmbBreederId: this.dialogData.id }).then((res) => {
                this.detailLoading = false;
                if (res.code == 200) {
                    const data = res.result;
                    console.log('获取到的人员详情数据:', data); // 调试信息

                    this.form = {
                        nickName: data.nickName || '',
                        breederType: data.breederType || '',
                        phonenumber: data.phonenumber || '',
                        remark: data.remark || ''
                    };

                    // 清除表单验证状态
                    this.$nextTick(() => {
                        if (this.$refs.ruleForm) {
                            this.$refs.ruleForm.clearValidate();
                        }
                    });
                } else {
                    this.$message.error(res.message || '获取人员信息失败');
                }
            }).catch((error) => {
                this.detailLoading = false;
                console.error('获取人员详情失败:', error);
                this.$message.error('获取人员信息失败，请稍后重试');
            });
        },
        
        // 重置表单
        resetForm() {
            this.form = {
                nickName: '',
                breederType: '',
                phonenumber: '',
                remark: ''
            };
            this.detailLoading = false;
            this.submitLoading = false;

            this.$nextTick(() => {
                if (this.$refs.ruleForm) {
                    this.$refs.ruleForm.resetFields();
                    this.$refs.ruleForm.clearValidate();
                }
            });
        },
        
        // 提交表单
        submitForm() {
            this.$refs.ruleForm.validate((valid) => {
                if (valid) {
                    this.submitLoading = true;
                    
                    const submitData = { ...this.form };
                    
                    if (this.dialogData.id) {
                        // 编辑
                        submitData.xmbBreederId = this.dialogData.id;
                        breederEdit(submitData).then((res) => {
                            this.handleSubmitResponse(res, '编辑');
                        }).catch(() => {
                            this.submitLoading = false;
                            this.$message.error('编辑失败');
                        });
                    } else {
                        // 新增
                        breederAdd(submitData).then((res) => {
                            this.handleSubmitResponse(res, '新增');
                        }).catch(() => {
                            this.submitLoading = false;
                            this.$message.error('新增失败');
                        });
                    }
                }
            });
        },
        
        // 处理提交响应
        handleSubmitResponse(res, action) {
            this.submitLoading = false;
            if (res.code == 200) {
                this.$message.success(`${action}成功`);
                this.$emit('refresh');
                this.close();
            } else {
                this.$message.error(res.message || `${action}失败`);
            }
        },
        
        // 关闭弹窗
        close() {
            this.resetForm();
            this.$emit('close');
        }
    }
};
</script>

<style lang="scss" scoped>
.form-section {
    .section-title {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        font-size: 16px;
        font-weight: bold;
        color: #409EFF;
        
        .section-icon {
            width: 4px;
            height: 16px;
            background-color: #409EFF;
            margin-right: 8px;
        }
    }
}

.dialog-footer {
    text-align: right;
}
</style>

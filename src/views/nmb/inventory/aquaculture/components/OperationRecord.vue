<template>
    <div>
        <el-dialog
            :title="`${dialogData.title} - ${dialogData.breederName}`"
            :visible.sync="dialogData.open"
            width="1000px"
            :close-on-click-modal="false"
            @close="close"
            append-to-body
        >
            <!-- 搜索区域 -->
            <el-card class="mb10 form_box" shadow="never">
                <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
                    <el-form-item label="操作时间：" prop="operTime">
                        <el-date-picker
                            v-model="operTime"
                            type="date"
                            placeholder="请选择操作时间"
                            value-format="yyyy-MM-dd"
                            @change="handleTimeChange"
                        />
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-card>

            <!-- 操作记录表格 -->
            <el-table 
                :data="tableData" 
                border 
                v-loading="loading" 
                style="width: 100%" 
                height="400"
            >
                <el-table-column type="index" align="center" label="序号" width="60"></el-table-column>
                <el-table-column prop="nickName" label="姓名" width="120" align="center" />
                <el-table-column prop="phonenumber" label="联系电话" width="140" align="center" />
                <el-table-column prop="operContent" label="操作内容" min-width="200" align="center" />
                <el-table-column prop="operTime" label="操作时间" width="180" align="center" />
            </el-table>

            <!-- 分页 -->
            <div style="margin-top: 20px; text-align: right;">
                <el-pagination
                    v-show="total > 0"
                    :total="total"
                    :page.sync="queryParams.pageNum"
                    :limit.sync="queryParams.pageSize"
                    @pagination="getList"
                    layout="total, sizes, prev, pager, next, jumper"
                    :page-sizes="[10, 20, 50, 100]"
                />
            </div>

            <span slot="footer" class="dialog-footer">
                <el-button @click="close">关 闭</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
export default {
    props: {
        dialogData: {
            type: Object,
            required: true
        }
    },
    data() {
        return {
            operTime: '',
            queryParams: {
                breederId: null,
                operTime: '',
                pageNum: 1,
                pageSize: 20
            },
            loading: false,
            total: 0,
            tableData: []
        };
    },
    watch: {
        'dialogData.open'(val) {
            if (val) {
                this.initData();
            }
        }
    },
    methods: {
        // 初始化数据
        initData() {
            this.queryParams.breederId = this.dialogData.breederId;
            this.resetQuery();
        },
        
        // 时间选择变化
        handleTimeChange(val) {
            this.queryParams.operTime = val;
        },
        
        // 获取操作记录列表
        getList() {
            this.loading = true;
            
            // 模拟数据，实际项目中应该调用真实的API
            setTimeout(() => {
                const mockData = this.generateMockData();
                this.tableData = mockData.list;
                this.total = mockData.total;
                this.loading = false;
            }, 500);
        },
        
        // 生成模拟数据
        generateMockData() {
            const mockList = [
                {
                    nickName: '张伟',
                    phonenumber: '18820262787',
                    operContent: '肉牛进食情况检测',
                    operTime: '2025-04-30 23:26:08'
                },
                {
                    nickName: '张伟',
                    phonenumber: '18820262787',
                    operContent: '肉牛喂养',
                    operTime: '2025-04-30 23:26:08'
                },
                {
                    nickName: '张伟',
                    phonenumber: '18820262787',
                    operContent: '肉牛喂养',
                    operTime: '2025-04-30 23:26:08'
                },
                {
                    nickName: '张伟',
                    phonenumber: '18820262787',
                    operContent: '肉牛喂养',
                    operTime: '2025-04-30 23:26:08'
                },
                {
                    nickName: '张伟',
                    phonenumber: '18820262787',
                    operContent: '肉牛喂养',
                    operTime: '2025-04-30 23:26:08'
                },
                {
                    nickName: '张伟',
                    phonenumber: '18820262787',
                    operContent: '肉牛喂养',
                    operTime: '2025-04-30 23:26:08'
                }
            ];
            
            // 根据查询条件过滤数据
            let filteredList = mockList;
            if (this.queryParams.operTime) {
                filteredList = mockList.filter(item => 
                    item.operTime.includes(this.queryParams.operTime)
                );
            }
            
            // 分页处理
            const start = (this.queryParams.pageNum - 1) * this.queryParams.pageSize;
            const end = start + this.queryParams.pageSize;
            const pageList = filteredList.slice(start, end);
            
            return {
                list: pageList,
                total: filteredList.length
            };
        },
        
        // 搜索
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        
        // 重置查询
        resetQuery() {
            this.operTime = '';
            this.queryParams.operTime = '';
            this.queryParams.pageNum = 1;
            this.getList();
        },
        
        // 关闭弹窗
        close() {
            this.operTime = '';
            this.queryParams = {
                breederId: null,
                operTime: '',
                pageNum: 1,
                pageSize: 20
            };
            this.tableData = [];
            this.total = 0;
            this.$emit('close');
        }
    }
};
</script>

<style lang="scss" scoped>
.mb10 {
    margin-bottom: 10px;
}

.form_box {
    padding: 15px;
}

.dialog-footer {
    text-align: right;
}
</style>
